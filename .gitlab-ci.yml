stages:
  - build
  - deploy
variables:
  DEPLOY_APP_NAME: "fs-k8s-app-manager"
  DEPLOY_APP_DOCKER_IMAGE: "reg.firstshare.cn/app/fs-k8s-app-manager:${CI_COMMIT_REF_NAME}"
  DEPLOY_APP_DOCKER_IMAGE_FONESHARE: "reg.foneshare.cn/app/fs-k8s-app-manager:${CI_COMMIT_REF_NAME}"

build_image:
  stage: build
  when: manual
  tags:
    - docker-build
  script:
    - docker pull ${DEPLOY_APP_DOCKER_IMAGE} || true
    - docker build --pull -t ${DEPLOY_APP_DOCKER_IMAGE} .
    - docker push ${DEPLOY_APP_DOCKER_IMAGE}
    - docker tag ${DEPLOY_APP_DOCKER_IMAGE} ${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}
    - docker push ${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}

firstshare_configmap:
  extends: .deploy_template
  variables:
    DEPLOY_K8S_CLUSTER: "firstshare-k8s1"
    DEPLOY_K8S_NAMESPACE: "firstshare"
    DEPLOY_K8S_CONFIGMAP_NAME: "configmap-firstshare.yml"
  script:
    - "echo === deploy config: ${DEPLOY_K8S_CONFIGMAP_NAME}"
    - kubectl-proxy replace --print-manifest -f k8s-manifest/templates/${DEPLOY_K8S_CONFIGMAP_NAME}

firstshare_all:
  extends: .deploy_template
  when: manual
  before_script:
    - export DEPLOY_APP_DOCKER_IMAGE="${DEPLOY_APP_DOCKER_IMAGE}"
    - export DEPLOY_K8S_CONFIGMAP_RELOAD_IMAGE="reg.firstshare.cn/ghcr.io/jimmidyson/configmap-reload:v0.13.1"
  variables:
    DEPLOY_K8S_CLUSTER: "firstshare-k8s1"
    DEPLOY_K8S_NAMESPACE: "firstshare"
    DEPLOY_K8S_CONFIGMAP_NAME: "configmap-firstshare.yml"
    DEPLOY_K8S_DEPLOYMENT_NAME: "deployment-firstshare.yml"
    DEPLOY_APP_LANGUAGE: "Golang"
    DEPLOY_APP_REPLICAS: "1"
    DEPLOY_APP_REQUESTS_CPU: "2000m"
    DEPLOY_APP_REQUESTS_MEMORY: "2048Mi"
    DEPLOY_APP_LIMITS_CPU: "4000m"
    DEPLOY_APP_LIMITS_MEMORY: "4096Mi"

foneshare_configmap:
  extends: .deploy_template
  tags:
    - k8s-deploy-foneshare
  variables:
    DEPLOY_K8S_CLUSTER: "foneshare-k8s0"
    DEPLOY_K8S_NAMESPACE: "foneshare"
    DEPLOY_K8S_CONFIGMAP_NAME: "configmap-foneshare.yml"
  script:
    - "echo === deploy config: ${DEPLOY_K8S_CONFIGMAP_NAME}"
    - kubectl-proxy replace --print-manifest -f k8s-manifest/templates/${DEPLOY_K8S_CONFIGMAP_NAME}

foneshare_all:
  extends: .deploy_template
  when: manual
  tags:
    - k8s-deploy-foneshare
  before_script:
    - export DEPLOY_APP_DOCKER_IMAGE="${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}"
    - export DEPLOY_K8S_CONFIGMAP_RELOAD_IMAGE="reg.foneshare.cn/ghcr.io/jimmidyson/configmap-reload:v0.13.1"
  variables:
    DEPLOY_K8S_CLUSTER: "foneshare-k8s0"
    DEPLOY_K8S_NAMESPACE: "foneshare"
    DEPLOY_K8S_CONFIGMAP_NAME: "configmap-foneshare.yml"
    DEPLOY_K8S_DEPLOYMENT_NAME: "deployment-foneshare.yml"
    DEPLOY_APP_LANGUAGE: "Golang"
    DEPLOY_APP_REPLICAS: "2"
    DEPLOY_APP_REQUESTS_CPU: "2000m"
    DEPLOY_APP_REQUESTS_MEMORY: "2048Mi"
    # 线上大版本发布时，需要较高的资源。这里按照大版本发布的场景吧limits资源调大，在大版本分布式只需扩容副本数即可
    DEPLOY_APP_LIMITS_CPU: "6000m"
    DEPLOY_APP_LIMITS_MEMORY: "6096Mi"

.deploy_template:
  stage: deploy
  tags:
    - k8s-deploy
  image: reg.firstshare.cn/base/fs-kubectl:v3.0
  script:
    - "echo === deploy redis.yml"
    - kubectl-proxy apply --print-manifest -f k8s-manifest/templates/redis.yml
    - "echo === deploy config: ${DEPLOY_K8S_CONFIGMAP_NAME}"
    - kubectl-proxy replace --print-manifest -f k8s-manifest/templates/${DEPLOY_K8S_CONFIGMAP_NAME}
    - "echo === deploy deployment.yml"
    - kubectl-proxy replace --print-manifest -f k8s-manifest/templates/${DEPLOY_K8S_DEPLOYMENT_NAME}
    - "echo === deploy service.yml"
    - kubectl-proxy apply --print-manifest -f k8s-manifest/templates/service.yml


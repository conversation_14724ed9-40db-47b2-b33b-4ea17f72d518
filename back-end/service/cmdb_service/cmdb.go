package cmdb_service

import (
	"encoding/json"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/cms"
	"fs-k8s-app-manager/service/user_service"
	log "github.com/sirupsen/logrus"
	"strings"
	"time"
)

type ServiceOwners struct {
	Service   string   `json:"service"`
	Owners    []string `json:"owners"`
	MainOwner []string `json:"mainOwner"`
	Desc      string   `json:"desc"`
}

type cacheData map[string]ServiceOwners

var cacheKey = "service-owners"
var mainOwnerCacheKey = "service-main-owner"

func ClearServiceOwnersCache() {
	cache.Delete(key.Pre().CMDB.Key(cacheKey))
}

// func GetOwnerNames(app string, joinChar string) string {
// 	if owners := GetOwners(app); len(owners) > 0 {
// 		return strings.Join(owners, joinChar)
// 	} else {
// 		return ""
// 	}
// }

// func GetOwnerIds(app string) []int {
// 	ret := make([]int, 0, 2)
// 	owners := GetOwners(app)
// 	if owners != nil {
// 		for _, it := range owners {
// 			if u, err := user_service.FindByRealName(it); err == nil {
// 				ret = append(ret, u.EmployeeId)
// 			}
// 		}
// 	}
// 	return ret
// }

// func GetOwners(app string) []string {
// 	var cacheD cacheData
// 	if v, found := cache.GetStruct(key.Pre().CMDB.Key(cacheKey), cacheData{}); found {
// 		cacheD = v
// 	} else {
// 		cacheD = make(map[string]ServiceOwners)
// 		if items, err := getDataFromCmdb(); err == nil {
// 			for _, it := range items {
// 				cacheD[it.Service] = it
// 			}
// 			_ = cache.SetStruct(key.Pre().CMDB.Key(cacheKey), cacheD, 2*time.Hour)
// 		} else {
// 			log.Error("can't get service info from cmdb, " + err.Error())
// 		}
// 	}
// 	if v, found := cacheD[app]; found {
// 		return v.Owners
// 	} else {
// 		return make([]string, 0)
// 	}
// }

func GetMainOwnerNames(app string, joinChar string) string {
	if owners := GetMainOwners(app); len(owners) > 0 {
		return strings.Join(owners, joinChar)
	} else {
		return ""
	}
}

func GetMainOwnerIds(app string) []int {
	ret := make([]int, 0, 2)
	owners := GetMainOwners(app)
	if owners != nil {
		for _, it := range owners {
			if u, err := user_service.FindByRealName(it); err == nil {
				ret = append(ret, u.EmployeeId)
			}
		}
	}
	return ret
}

func GetMainOwners(app string) []string {
	var cacheD cacheData
	if v, found := cache.GetStruct(key.Pre().CMDB.Key(cacheKey), cacheData{}); found {
		cacheD = v
	} else {
		cacheD = make(map[string]ServiceOwners)
		if items, err := getDataFromCmdb(); err == nil {
			for _, it := range items {
				cacheD[it.Service] = it
			}
			_ = cache.SetStruct(key.Pre().CMDB.Key(cacheKey), cacheD, 2*time.Hour)
		} else {
			log.Error("can't get service info from cmdb, " + err.Error())
		}
	}
	if v, found := cacheD[app]; found {
		return v.MainOwner
	} else {
		return make([]string, 0)
	}
}

func AllAppOwners() ([]ServiceOwners, error) {
	return getDataFromCmdb()
}

func getDataFromCmdb() ([]ServiceOwners, error) {
	configPaths := strings.Split(config.Conf.CMS.CmdbConfigPath, "/")
	content, err := cms.GetConfig(configPaths[0], configPaths[1])
	if err != nil {
		return nil, err
	}

	var respData []map[string]string
	err = json.Unmarshal([]byte(content), &respData)
	if err != nil {
		return nil, err
	}

	dbUsers := make(map[string]models.User)
	users, err := user_service.All()
	if err != nil {
		return nil, err
	}
	for _, it := range users {
		dbUsers[it.RealName] = it
	}

	var ret []ServiceOwners
	for _, it := range respData {
		ret = append(ret, ServiceOwners{
			Service:   it["service"],
			Owners:    strings.Split(it["owner"], ","),
			MainOwner: []string{it["mainOwner"]},
			Desc:      it["info"],
		})
	}
	return ret, nil
}

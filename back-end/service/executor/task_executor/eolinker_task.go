package task_executor

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/eolinker"
	"fs-k8s-app-manager/service/task_service"
	"github.com/go-resty/resty/v2"
	"github.com/mitchellh/mapstructure"
	"time"
)

type EolinkerParam struct {
	App         string               `json:"app"`
	Cluster     string               `json:"cluster"`
	Namespace   string               `json:"namespace"`
	Version     string               `json:"version"`
	Remark      string               `json:"remark"`
	EolinkerIDs datatype.EolinkerIDs `json:"eolinkerIDs"`
}

type EolinkerTaskExecutor struct {
	TaskAbstractExecutor
	Params EolinkerParam
}

func (s EolinkerTaskExecutor) Build(t *models.Task) (TaskExecutor, error) {
	if err := mapstructure.Decode(t.Params, &s.Params); err != nil {
		return s, err
	}
	s.Task = t
	return s, nil
}

func (s EolinkerTaskExecutor) Run() {
	defer s.catchPanic()
	if s.Task.IsSkip() {
		panic("task is skip")
	}
	if !s.Task.IsWait() {
		panic("task is not wait")
	}
	if err := s.Start(); err != nil {
		_ = s.Fail()
		panic(err.Error())
	}
	if err := s.process(); err != nil {
		panic(err.Error())
	}
	if !s.Task.IsCancel() {
		if err := s.Success(); err != nil {
			panic(err.Error())
		}
	}
}

func (s EolinkerTaskExecutor) process() error {
	var execTimedTaskErrorMsg string
	start := time.Now()
	// 可能会有多个不同的project，先把跳转的project注释掉了
	//_ = stage_service.AppendExtraAttr(t.StageId, "projectID", t.ProjectID)
	client := eolinker.Default()
	var reportIDs []int
	for _, v := range s.Params.EolinkerIDs {
		reportID, err := client.ExecuteTimedTask(v.ProjectID, v.TimedTaskID)
		if err != nil {
			execTimedTaskErrorMsg += fmt.Sprintf("执行定时任务失败, projectID: %s, timedTaskID: %s, err: %s\n", v.ProjectID, v.TimedTaskID, err.Error())
			_ = s.AppendOutput(execTimedTaskErrorMsg)
			continue
		}
		reportIDs = append(reportIDs, reportID)
	}
	var loopErr error = nil
	var reports []eolinker.Report
LOOP:
	for {
		_ = s.ReloadTask()
		if s.Task.IsCancel() {
			_ = s.AppendOutput("task canceled")
			break
		}
		if time.Now().Unix()-start.Unix() > int64(s.Task.TimeoutSeconds) {
			loopErr = errors.New(fmt.Sprintf("task timeout [%d seconds]", s.Task.TimeoutSeconds))
			break
		}
		time.Sleep(10 * time.Second)
		reports = make([]eolinker.Report, 0, len(reportIDs)) //清空
		for i, v := range reportIDs {
			output, err := client.GetReport(s.Params.EolinkerIDs[i].ProjectID, s.Params.EolinkerIDs[i].TimedTaskID, v)
			if err != nil {
				_ = s.AppendOutput(fmt.Sprintf("获取报告信息失败, err: %s, reportID: %v", err.Error(), v))
				// 嵌套for循环，continue跳过当前的for循环，所以使用了 continue 标签，用于回到第一层for循环
				continue LOOP
			}
			reports = append(reports, output)
		}
		for _, v := range reports {
			if v.TestStatus == "testing" {
				_ = s.AppendOutput("任务正在执行中...")
				continue LOOP
			}
		}

		// 暂时不清空output，保留错误信息
		//_ = stage_service.UpdateOutput(t.StageId, "")
		var reportDownloadUrls []string
		for _, v := range reports {
			reportDownloadUrls = append(reportDownloadUrls, v.ReportDownloadURL)
		}
		_ = task_service.AppendAttribute(s.Task.ID, "reportDownloadUrls", reportDownloadUrls)
		if outputJson, err := json.Marshal(reports); err != nil {
			_ = s.AppendOutput("parse report json fail: " + err.Error())
		} else {
			output := "========== 测试报告结果  ==========\n"
			output += string(outputJson)
			_ = s.AppendOutput(output)
		}
		for _, v := range reports {
			if v.TestStatus != "success" {
				loopErr = errors.New("任务执行失败, url: " + v.ReportDownloadURL)
				break
			}
		}
		break
	}
	if loopErr != nil {
		_ = s.AppendOutput(loopErr.Error())
	}
	//上报测试报告
	if config.Conf.Eolinker.ReportHost != "" && len(reports) > 0 {
		url := fmt.Sprintf("%s/eolinker/report/createReleaseReport", config.Conf.Eolinker.ReportHost)
		if e := s.uploadReport(url, reports); e == nil {
			_ = s.AppendOutput("报告上传成功")
		} else {
			_ = s.AppendOutput("报告上传失败： " + e.Error())
		}
	}
	return loopErr
}

func (s *EolinkerTaskExecutor) uploadReport(url string, reports []eolinker.Report) error {

	body := make(map[string]interface{})
	body["app"] = s.Params.App
	body["cluster"] = s.Params.Cluster
	body["namespace"] = s.Params.Namespace
	body["auth"] = fmt.Sprintf("发布系统(%s)", s.Task.Author)
	body["version"] = s.Params.Version
	body["remark"] = s.Params.Remark
	body["eolinker_task_reports"] = reports
	b, _ := json.Marshal(body)
	reqData := string(b)
	output := "========== 上传测试报告 ==========\n"
	output += "地址：" + url + "\n"
	output += "内容：" + reqData + "\n"
	output += "================================"
	_ = s.AppendOutput(output)
	resp, err := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
		R().
		SetHeader("Content-Type", "application/json; charset=utf-8").
		SetBody(reqData).
		Post(url)
	if err != nil {
		return err
	}
	if resp.StatusCode() != 200 {
		return fmt.Errorf("report upload fail, code: %d, message: %s", resp.StatusCode(), resp.Body())
	}
	return nil
}

package es_service

import (
	"context"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/pipeline_service"
	es "github.com/olivere/elastic/v7"
	log "github.com/sirupsen/logrus"
	"time"
)

type PipelineAuditLog struct {
	Timestamp     int64   `json:"timestamp"`
	AppName       string  `json:"appName,omitempty"`
	Namespace     string  `json:"namespace,omitempty"`
	Replicas      uint64  `json:"replicas,omitempty"`
	RequestCPU    float32 `json:"requestCPU,omitempty"`
	LimitCPU      float32 `json:"limitCPU,omitempty"`
	RequestMemory uint64  `json:"requestMemory,omitempty"`
	LimitMemory   uint64  `json:"limitMemory,omitempty"`
	ScheduleNode  string  `json:"scheduleNode,omitempty"`
	Status        string  `json:"status,omitempty"`
	RunningPodNum int32   `json:"RunningPodNum,omitempty"`
}

var esClient *es.Client

func init() {
	var err error
	addr := config.Conf.Es.Hosts
	username := config.Conf.Es.Username
	password := config.Conf.Es.Password
	cli := es.SetURL(addr...)
	basicAuth := es.SetBasicAuth(username, password)
	esClient, err = es.NewClient(es.SetSniff(false), cli, basicAuth)
	if err != nil {
		log.Errorf("es client init err: %v", err)
	}
}

func EsClient() *es.Client {
	return esClient
}

func SavePipelineAuditLogs() error {
	pipelines, err := pipeline_service.FindAll()
	if err != nil {
		return err
	}
	timestamp := time.Now().UnixNano() / 1e6
	pipes, err := dto.ParsePipelineList(pipelines)

	for idx := range pipes {
		item := &pipes[idx]
		//todo: 改用批量接口获取数据
		if dep, err := k8s_service.GetDeploymentDTO(item.Cluster, item.Namespace, item.App); err == nil {
			item.AddExtraAttr(dep)
		}
		auditLog := PipelineAuditLog{
			Timestamp:     timestamp,
			AppName:       item.App,
			Namespace:     item.Namespace,
			Replicas:      uint64(item.Replicas),
			RequestCPU:    item.Resources.RequestCPU,
			LimitCPU:      item.Resources.LimitCPU,
			RequestMemory: uint64(item.Resources.RequestMemory),
			LimitMemory:   uint64(item.Resources.LimitMemory),
			ScheduleNode:  item.Schedule.Node,
			Status:        item.Status,
			RunningPodNum: item.ExtraAttr.RunningPodNum,
		}
		resp, err := EsClient().Index().Index(config.Conf.Es.Index).BodyJson(auditLog).Do(context.Background())
		if err != nil {
			log.Errorf("insert es error %s", err)
			continue
		}
		log.Infof("app: %s namespace: %s result: %s", item.App, item.Namespace, resp.Result)
	}
	return nil
}

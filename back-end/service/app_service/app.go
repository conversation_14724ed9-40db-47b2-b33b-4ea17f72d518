package app_service

import (
	"fs-k8s-app-manager/models"
)

func FindAll() (ret []models.App, err error) {
	err = models.DB().Find(&ret).Error
	return
}
func Search(keyword string, level string, page, limit int) (ret []models.App, err error) {
	db := models.DB().Model(&models.App{})
	if keyword != "" {
		db.Where("name LIKE ?", "%"+keyword+"%").
			Or("admins LIKE ?", "%"+keyword+"%").
			Or("remark LIKE ?", "%"+keyword+"%")
	}
	if level != "" {
		db.Where("level = ?", level)
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}
func Count(keyword string, level string) int64 {
	var count int64
	db := models.DB().Model(&models.App{})
	if keyword != "" {
		db.Where("name LIKE ?", "%"+keyword+"%").
			Or("admins LIKE ?", "%"+keyword+"%").
			Or("remark LIKE ?", "%"+keyword+"%")
	}
	if level != "" {
		db.Where("level = ?", level)
	}
	db.Count(&count)
	return count
}
func FindByName(name string) (ret models.App, err error) {
	err = models.DB().Where("name = ?", name).Take(&ret).Error
	return
}
func Exist(name string) bool {
	_, err := FindByName(name)
	return err == nil
}
func DeleteByName(name string) error {
	return models.DB().Unscoped().Where("name = ?", name).Delete(&models.App{}).Error
}

func Create(entity *models.App) (err error) {
	return models.DB().Create(entity).Error
}

func Update(entity *models.App) (err error) {
	return models.DB().Save(entity).Error
}

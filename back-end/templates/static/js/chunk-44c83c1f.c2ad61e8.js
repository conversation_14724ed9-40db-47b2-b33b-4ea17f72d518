(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-44c83c1f"],{"0604":function(t,e,a){},"8f3f":function(t,e,a){"use strict";a("0604")},b562:function(t,e,a){"use strict";a.d(e,"p",(function(){return r})),a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return c})),a.d(e,"l",(function(){return l})),a.d(e,"j",(function(){return o})),a.d(e,"d",(function(){return u})),a.d(e,"i",(function(){return s})),a.d(e,"h",(function(){return p})),a.d(e,"m",(function(){return m})),a.d(e,"o",(function(){return d})),a.d(e,"f",(function(){return h})),a.d(e,"e",(function(){return g})),a.d(e,"c",(function(){return f})),a.d(e,"k",(function(){return b})),a.d(e,"q",(function(){return v})),a.d(e,"n",(function(){return _})),a.d(e,"g",(function(){return y}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/app/search",method:"get",params:t})}function i(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function c(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function l(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function o(t){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function u(t){return Object(n["a"])({url:"/v1/app",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/v1/app",method:"put",data:t})}function p(t){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function m(t,e,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:a}})}function d(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function h(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function f(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function b(t,e){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function v(t,e){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(t){return Object(n["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:t}})}},cd5c:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container",attrs:{"element-loading-text":"数据加载中"}},[a("div",{staticStyle:{width:"500px",margin:"0 auto"}},[a("el-input",{attrs:{placeholder:"输入关键字可过滤"},model:{value:t.searchName,callback:function(e){t.searchName=e},expression:"searchName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.loadData()}},slot:"append"},[t._v("查找")])],1)],1),t._v(" "),a("div",{staticClass:"app_info_panel",staticStyle:{"text-align":"center","padding-top":"20px"}},[a("b",[t._v("应用:")]),a("span",[t._v(t._s(this.$route.query.app))]),t._v(" "),a("b",[t._v("Git地址:")]),a("span",[t._v(t._s(this.$route.query.git_url))])]),t._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-divider",{attrs:{"content-position":"left"}},[a("b",[t._v("分支列表")]),t._v(" "),a("small",{staticStyle:{"padding-left":"10px"}},[t._v("(最多显示 2000 条数据)")])]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.batchDeleteGitBranch()}}},[t._v("批量删除")]),t._v(" "),a("el-table",{key:"name",staticStyle:{width:"100%","max-height":"700px",overflow:"auto"},attrs:{data:t.tableData.branches},on:{"selection-change":t.branchSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",sortable:"",label:"分支名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"commitCreatedAt",label:"对应Commit时间",sortable:""}}),t._v(" "),a("el-table-column",{attrs:{prop:"message",label:"描述"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-divider",{attrs:{"content-position":"left"}},[a("b",[t._v("Tag列表")]),t._v(" "),a("small",{staticStyle:{"padding-left":"10px"}},[t._v("(最多显示 2000 条数据)")])]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.batchDeleteGitTag()}}},[t._v("批量删除")]),t._v(" "),a("el-table",{key:"name",staticStyle:{width:"100%","max-height":"700px",overflow:"auto"},attrs:{data:t.tableData.tags},on:{"selection-change":t.tagSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",sortable:"",label:"Tag名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"commitCreatedAt",label:"对应Commit时间",sortable:""}}),t._v(" "),a("el-table-column",{attrs:{prop:"message",label:"描述"}})],1)],1)],1)],1)},r=[],i=(a("7f7f"),a("b562")),c={name:"git-tag-batch-delete",data:function(){return{loading:!1,tableData:{},multipleSelectionBranches:[],multipleSelectionTags:[],searchName:""}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;this.loading=!0,Object(i["k"])(this.$route.query.git_url,this.searchName).then((function(e){e.data.branches=e.data.branches.filter((function(t){return"main"!==t.name&&"master"!==t.name})),t.tableData=e.data,t.loading=!1})).catch((function(e){t.$message.warning("查询失败！ "+e.message),t.loading=!1}))},tagSelectionChange:function(t){this.multipleSelectionTags=t},branchSelectionChange:function(t){this.multipleSelectionBranches=t},batchDeleteGitTag:function(){for(var t=this,e=[],a=0;a<this.multipleSelectionTags.length;a++)e.push(this.multipleSelectionTags[a].name);e.length<1?this.$message.info("请选择需要删除的数据"):this.$confirm("确认删除？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a={};a.tags=e,a.gitUrl=t.$route.query.git_url,t.loading=!0,Object(i["c"])(a).then((function(e){t.$message.success("删除成功"),t.loadData()})).catch((function(e){t.$message.warning("删除失败！ "+e.message)}))}))},batchDeleteGitBranch:function(){for(var t=this,e=[],a=0;a<this.multipleSelectionBranches.length;a++)e.push(this.multipleSelectionBranches[a].name);e.length<1?this.$message.info("请选择需要删除的数据"):this.$confirm("确认删除？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a={};a.branches=e,a.gitUrl=t.$route.query.git_url,t.loading=!0,Object(i["c"])(a).then((function(e){t.$message.success("删除成功"),t.loadData()})).catch((function(e){t.$message.warning("删除失败！ "+e.message)}))}))}}},l=c,o=(a("8f3f"),a("2877")),u=Object(o["a"])(l,n,r,!1,null,"7b797e8a",null);e["default"]=u.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f99211b"],{"02f4":function(t,e,a){var n=a("4588"),i=a("be13");t.exports=function(t){return function(e,a){var r,o,l=String(i(e)),s=n(a),c=l.length;return s<0||s>=c?t?"":void 0:(r=l.charCodeAt(s),r<55296||r>56319||s+1===c||(o=l.charCodeAt(s+1))<56320||o>57343?t?l.charAt(s):r:t?l.slice(s,s+2):o-56320+(r-55296<<10)+65536)}}},"0da1":function(t,e,a){"use strict";a("5e12")},"1c4c":function(t,e,a){"use strict";var n=a("9b43"),i=a("5ca1"),r=a("4bf8"),o=a("1fa8"),l=a("33a4"),s=a("9def"),c=a("f1ae"),u=a("27ee");i(i.S+i.F*!a("5cc5")((function(t){Array.from(t)})),"Array",{from:function(t){var e,a,i,p,d=r(t),f="function"==typeof this?this:Array,m=arguments.length,b=m>1?arguments[1]:void 0,v=void 0!==b,h=0,g=u(d);if(v&&(b=n(b,m>2?arguments[2]:void 0,2)),void 0==g||f==Array&&l(g))for(e=s(d.length),a=new f(e);e>h;h++)c(a,h,v?b(d[h],h):d[h]);else for(p=g.call(d),a=new f;!(i=p.next()).done;h++)c(a,h,v?o(p,b,[i.value,h],!0):i.value);return a.length=h,a}})},"279d":function(t,e,a){"use strict";a("97ab")},"4f7f":function(t,e,a){"use strict";var n=a("c26b"),i=a("b39a"),r="Set";t.exports=a("e0b8")(r,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return n.def(i(this,r),t=0===t?0:t,t)}},n)},"57c3":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[this.maintainIsOpen?a("div",{staticStyle:{margin:"0 auto",padding:"10px"}},[a("el-alert",{attrs:{title:"",closable:!1,type:"warning"}},[a("template",{slot:"title"},[a("div",{staticStyle:{"line-height":"18px",color:"orangered","font-weight":"bold"}},[a("div",{staticStyle:{"font-size":"16px"}},[a("span",{staticStyle:{color:"orangered"}},[a("i",{staticClass:"el-icon-warning"})]),t._v(" "),a("span",{staticStyle:{"padding-left":"5px"}},[t._v("系统提示")])]),t._v(" "),a("div",{staticStyle:{"padding-top":"10px","font-size":"14px"},domProps:{innerHTML:t._s(this.maintainDesc)}})])])],2)],1):a("div",{staticStyle:{display:"none"}})])},i=[],r={name:"maintain-alert",props:{maintainType:{type:String,required:!0}},data:function(){return{}},watch:{},computed:{maintainIsOpen:function(){return"CD"===this.maintainType.toUpperCase()?this.$settings.maintain.cd.open:"CI"===this.maintainType.toUpperCase()&&this.$settings.maintain.ci.open},maintainDesc:function(){return"CD"===this.maintainType.toUpperCase()?this.$settings.maintain.cd.desc:"CI"===this.maintainType.toUpperCase()?this.$settings.maintain.ci.desc:""}},mounted:function(){},methods:{}},o=r,l=a("2877"),s=Object(l["a"])(o,n,i,!1,null,null,null);e["a"]=s.exports},"59bf":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container image-build-container",staticStyle:{margin:"0",padding:"0"}},[a("menu-tabs",{attrs:{"tab-name":"image-build"}}),t._v(" "),a("maintain-alert",{attrs:{"maintain-type":"ci"}}),t._v(" "),a("app-selector2",{attrs:{"update-history":!0,showDetail:!0},on:{change:t.appChange}}),t._v(" "),a("job-runner-alert",{ref:"jobRunnerAlert",attrs:{app:this.currApp,"job-type":"CI"}}),t._v(" "),a("div",{staticStyle:{"min-height":"180px",margin:"10px"}},[a("div",{staticStyle:{"padding-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-position"},on:{click:t.imageBuildDialog}},[t._v("批量构建镜像")]),t._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",icon:"el-icon-price-tag"},on:{click:function(e){return t.gitTagPage(null)}}},[t._v("创建Tag")]),t._v(" "),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-position"},on:{click:t.pipelinePage}},[t._v("去发布")]),t._v(" "),a("el-button",{staticStyle:{"margin-left":"30px",color:"#E6A23C"},attrs:{type:"text",icon:"el-icon-position"},on:{click:t.urgentDeploy}},[t._v("紧急构建")])],1),t._v(" "),a("div",{staticStyle:{clear:"both"}})]),t._v(" "),a("el-table",{ref:"deployModuleTable",attrs:{data:t.tableData,"element-loading-text":"数据加载中...","highlight-selection-row":!0,fit:""}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"60"}}),t._v(" "),a("el-table-column",{attrs:{label:"",width:"300",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{icon:"el-icon-search",type:"text"},on:{click:function(a){return t.imagePage(e.row)}}},[t._v("查看镜像")]),t._v(" "),a("el-button",{staticStyle:{display:"none"},attrs:{type:"text",icon:"el-icon-price-tag"},on:{click:function(a){return t.gitTagPage(e.row.gitUrl)}}},[t._v("创建Tag")]),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-s-order"},on:{click:function(a){return t.jobHistoryPage(e.row.gitUrl,e.row.module)}}},[t._v("构建记录")]),t._v(" "),a("el-button",{staticStyle:{padding:"5px 7px","margin-left":"10px"},attrs:{type:"primary",size:"mini",icon:"el-icon-position"},on:{click:function(a){return t.showDialog1([e.row])}}},[t._v("构建镜像")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"Git地址",prop:"gitUrl"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:e.row.gitUrl,target:"_blank"}},[t._v(t._s(e.row.gitUrl))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"Maven子模块",prop:"module"}}),t._v(" "),a("el-table-column",{attrs:{label:"默认编译环境"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.mavenImage.split("/").pop()))])]}}])})],1)],1),t._v(" "),a("el-dialog",{staticClass:"deploy-dialog",attrs:{visible:t.dialog1Visible,"close-on-click-modal":!1,top:"5vh",width:"860px"},on:{"update:visible":function(e){t.dialog1Visible=e}}},[a("template",{slot:"title"},[t._v("\n      部署模块镜像构建（"+t._s(this.currApp)+"）\n      "),a("span",{staticStyle:{"padding-left":"10px",color:"#777","font-size":"12px"}})]),t._v(" "),a("el-form",{ref:"buildFormRef",attrs:{model:t.buildForm,"label-width":"100px",rules:t.buildFormRules}},[t._l(t.buildForm.modules,(function(e,n){return a("div",[a("el-form-item",{staticClass:"module-git-url",staticStyle:{padding:"0",margin:"0 0 -5px 0"},attrs:{label:""}},[a("span",{staticStyle:{"font-size":"12px",color:"#b4532a","font-weight":"bold"}},[t._v("\n            部署模块： "+t._s(e.gitUrl)+" --- "+t._s(e.module)+"\n          ")])]),t._v(" "),a("el-form-item",{attrs:{label:"版本号"}},[a("div",{staticStyle:{display:"inline-block",width:"400px"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:t.buildForm.modules[n].tag,callback:function(e){t.$set(t.buildForm.modules[n],"tag",e)},expression:"buildForm.modules[index].tag"}},[t.getVersionOptions(e.gitUrl).branchOptions.length>0?a("el-option-group",{attrs:{label:"Git分支"}},t._l(t.getVersionOptions(e.gitUrl).branchOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}},[a("span",[a("b",[t._v(t._s(e.name))])])])})),1):t._e(),t._v(" "),a("el-option-group",{attrs:{label:"GitTag (message)"}},t._l(t.getVersionOptions(e.gitUrl).tagOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}},[a("span",[a("b",[t._v(t._s(e.name))]),a("span",{staticStyle:{"font-size":"12px",color:"#888","padding-left":"5px"}},[t._v(t._s(e.message?" ("+e.message.substring(0,30)+")":""))])])])})),1)],1)],1),t._v(" "),a("div",{staticStyle:{display:"inline-block",width:"300px","text-align":"right"}},[a("b",{staticStyle:{color:"#606266"}},[t._v("编译环境")]),t._v(" "),a("el-select",{staticStyle:{width:"220px"},attrs:{filterable:""},model:{value:t.buildForm.modules[n].mavenImage,callback:function(e){t.$set(t.buildForm.modules[n],"mavenImage",e)},expression:"buildForm.modules[index].mavenImage"}},t._l(t.mavenOptions,(function(t){return a("el-option",{key:t,attrs:{label:t.split("/").pop(),value:t}})})),1)],1)])],1)})),t._v(" "),a("el-form-item",{attrs:{label:"父POM",prop:"parentPom"}},[a("div",{staticStyle:{display:"inline-block",width:"700px"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.buildForm.parentPom,callback:function(e){t.$set(t.buildForm,"parentPom",e)},expression:"buildForm.parentPom"}},[t._l(this.$settings.parentPoms,(function(e){return a("el-option",{attrs:{label:e.name,value:e.value}},[a("span",[t._v(t._s(e.name)+" ")]),t._v(" "),a("span",{staticStyle:{"padding-left":"20px",color:"#8492a6","font-size":"13px"}},[a("label",{staticStyle:{color:"orangered","font-size":"12px"}},[t._v(t._s(e.desc))])])])})),t._v(" "),a("div",{staticStyle:{"margin-top":"10px","font-size":"12px",color:"#888","margin-left":"20px"}},[t._v("\n              如果要查看父POM所管理的Jar包及其版本信息，请点击 "),a("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md",target:"_blank"}},[t._v("链接")])])],2)],1),t._v(" "),a("div",{staticStyle:{display:"inline-block"}},[a("el-tooltip",{staticClass:"item",staticStyle:{float:"right"},attrs:{effect:"light",placement:"left"}},[a("template",{staticStyle:{"line-height":"1.2em"},slot:"content"},[a("p",[t._v("不同父POM中定义的依赖包版本区别如下：")]),t._v(" "),a("p",[a("b",[t._v("测试版（Alpha)：")]),t._v(" 包含未经测试的高版本依赖包，只在线下环境可用")]),t._v(" "),a("p",[a("b",[t._v("候选版（RC): ")]),t._v(" 通过了线下环境测试的依赖包版本，或者一些紧急Bug修复的依赖包版本")]),t._v(" "),a("p",[a("b",[t._v("稳定版 (RELEASE)：")]),t._v(" 在候选版通过充分测试后，需要进入到全网的依赖包版本")]),t._v(" "),a("el-divider",{staticClass:"env-divider"}),t._v(" "),a("p",[t._v("依赖包版本的升级常规流程如下：首先进入【测试版】在线下环境进行测试，通过后进入"),a("br"),t._v("【候选版】在线上环境进行试用,通过后进入【稳定版】推广到全网。")])],1),t._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1)]),t._v(" "),a("el-form-item",{attrs:{label:"备注信息"}},[a("div",{staticStyle:{display:"inline-block",width:"700px"}},[a("el-input",{attrs:{type:"textarea",rows:2,maxlength:32,max:32},model:{value:t.buildForm.remark,callback:function(e){t.$set(t.buildForm,"remark",e)},expression:"buildForm.remark"}})],1)]),t._v(" "),a("el-form-item",[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"如果镜像存在，则覆盖"},model:{value:t.buildForm.forceCodeCompile,callback:function(e){t.$set(t.buildForm,"forceCodeCompile",e)},expression:"buildForm.forceCodeCompile"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n            默认情况下，当同名镜像已经存在的时候，不允许直接构建镜像，需要选择覆盖。勾选此选项后，会强制重新编译代码并覆盖原有镜像。\n          ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2),t._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"执行单元测试"},model:{value:t.buildForm.unitTest,callback:function(e){t.$set(t.buildForm,"unitTest",e)},expression:"buildForm.unitTest"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n            代码在进行Maven编译时，是否执行单元测试\n          ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2),t._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"依赖包版本校验",disabled:""},model:{value:t.buildForm.dependencyCheck,callback:function(e){t.$set(t.buildForm,"dependencyCheck",e)},expression:"buildForm.dependencyCheck"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n            对工程的依赖Jar包进行两方面检测。1：同一jar包是否存在多个版本；2：某个jar包版本是否低于平台所要求的最低版本\n          ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),t._v(" "),a("el-form-item",{attrs:{label:"提示"}},[a("el-alert",{attrs:{closable:!1,type:"info"}},[a("template",{slot:"title"},[a("div",[a("b",[t._v("镜像版本号生成机制:")])]),t._v(" "),a("div",[t._v("\n              1. 如果版本号为tag，则直接使用该名称"),a("br"),t._v("\n              2. 如果版本号为分支，则添加时间后缀。比如 master 会被替换为 master--202406041130"),a("br"),t._v("\n              3. 如果版本号中包含了斜线 /，则将其替换为 ---。比如 version/910 会被替换为 version---910"),a("br")])])],2)],1)],2),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialog1Visible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.buildBtnLoading,expression:"buildBtnLoading"}],attrs:{type:"primary"},on:{click:t.buildSubmit}},[t._v("开始构建")])],1)],2)],1)},i=[],r=(a("1c4c"),a("ac6a"),a("5df3"),a("4f7f"),a("7f7f"),a("2d63")),o=a("db72"),l=a("b562"),s=a("6a4c"),c=a("b144"),u=a("76fe"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-selector-wrapper"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,model:t.searchForm,rules:t.rules}},[a("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群"},on:{change:t.loadApp},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(this.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择Namespace"},on:{change:t.loadApp},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(this.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-select",{staticStyle:{width:"340px"},attrs:{filterable:"",placeholder:"选择应用"},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app",e)},expression:"searchForm.app"}},t._l(this.appOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1)],1)],1)},d=[],f=a("8504"),m={name:"AppSelector",props:{},data:function(){return{searchForm:{cluster:"",namespace:"",app:""},rules:{cluster:[{required:!0,message:"请选择k8s集群"}],namespace:[{required:!0,message:"请选择运行环境"}],app:[{required:!0,message:"请输入应用名"}]},cache:{}}},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var t,e=Object(r["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var a=t.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(n){e.e(n)}finally{e.f()}}return[]}},beforeDestroy:function(){},methods:{loadApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace;if(t&&e){var a=t+"##"+e+"##app",n=this.cache[a];if(n)this.appOptions=n;else{var i=this;Object(f["g"])(t,e).then((function(t){i.appOptions=t.data,i.cache[a]=t.data})).catch((function(t){console.error(t)}))}}else this.appOptions=[]}}},b=m,v=(a("279d"),a("2877")),h=Object(v["a"])(b,p,d,!1,null,null,null),g=h.exports,_=a("71df"),y=a("84d4"),x=a("bcbd"),k=a("57c3"),w={name:"image-build",watch:{currApp:function(t){this.loadTableData()}},data:function(){return{loading:!1,tableData:[],currApp:"",dialog1Visible:!1,buildOptions:{versionOptions:{}},buildBtnLoading:!1,buildForm:{app:"",modules:[],unitTest:!1,forceCodeCompile:!1,dependencyCheck:!0,parentPom:"",remark:""},buildFormRules:{parentPom:[{required:!0,message:"请选择父POM"}]}}},components:{MaintainAlert:k["a"],JobRunnerAlert:x["a"],MenuTabs:y["a"],AppSelector2:_["a"],AppSelector:g},computed:{mavenOptions:function(){return this.$settings.mavenImages||[]}},mounted:function(){},methods:{loadTableData:function(){var t=this;this.loading=!0,this.currApp?Object(l["n"])(this.currApp).then((function(e){if(t.tableData=e.data,1===t.tableData.length&&"true"===t.$route.query.openDialog){t.$refs.deployModuleTable.toggleAllSelection();var a=t;setTimeout((function(){a.imageBuildDialog();var e=Object(o["a"])({},t.$route.query);delete e["openDialog"],a.$router.push({query:e})}),300)}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1})):this.tableData=[]},appChange:function(t){this.currApp=t,this.updateQueryParam(),this.loadTableData()},updateQueryParam:function(){var t=Object(c["a"])(this.$route.query);t["app"]=this.currApp,this.$router.push({query:t})},getVersionOptions:function(t){return this.buildOptions.versionOptions[t]||{tagOptions:[],branchOptions:[]}},urgentDeploy:function(){this.$alert('如果需要紧急构建，请申请临时操作权限。 点击 <a href="/#/auth/temp-path" target="_blank" style="color: #3a8ee6">链接</a> 打开临时授权申请页面',"紧急构建提示",{dangerouslyUseHTMLString:!0})},versionIsBranch:function(t,e){var a,n=this.getVersionOptions(t),i=Object(r["a"])(n.branchOptions);try{for(i.s();!(a=i.n()).done;){var o=a.value;if(o.name===e)return!0}}catch(l){i.e(l)}finally{i.f()}return!1},isProdEnv:function(){return window.location.host.indexOf("foneshare")>-1},imageBuildDialog:function(){var t=this.$refs.deployModuleTable.store.states.selection;!t||t.length<1?this.$message.warning("请选择部署模块"):this.showDialog1(t)},showDialog1:function(t){var e=this;this.loading=!0;var a,n=new Set,i=[],o=Object(r["a"])(t);try{for(o.s();!(a=o.n()).done;){var l=a.value;n.add(l.gitUrl),i.push({gitUrl:l.gitUrl,module:l.module,mavenImage:l.mavenImage,tag:""})}}catch(c){o.e(c)}finally{o.f()}Object(s["c"])({gitUrls:Array.from(n)}).then((function(t){e.buildOptions.versionOptions=t.data,e.buildForm.modules=i,e.dialog1Visible=!0})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},buildSubmit:function(){var t=this;this.$refs["buildFormRef"].validate((function(e){if(e){var a,n={items:[]},i=Object(c["a"])(t.buildForm),o=Object(r["a"])(i.modules);try{for(o.s();!(a=o.n()).done;){var l=a.value;n.items.push({app:t.currApp,gitUrl:l.gitUrl,gitModule:l.module,gitTag:l.tag,mavenImage:l.mavenImage,unitTest:i.unitTest,forceCodeCompile:i.forceCodeCompile,dependencyCheck:i.dependencyCheck,parentPom:i.parentPom,remark:i.remark})}}catch(s){o.e(s)}finally{o.f()}t.buildBtnLoading=!0,Object(u["a"])(n).then((function(e){1===e.data.length?t.jobPage(e.data[0].jobId):t.jobHistoryPage("",""),t.dialog1Visible=!1})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.buildBtnLoading=!1}))}}))},gitTagPage:function(t){var e={app:this.currApp};t&&(e.gitUrl=t);var a=this.$router.resolve({name:"git-tag",query:e});window.open(a.href,"_blank")},imagePage:function(t){var e=this.$router.resolve({name:"cicd-image-list",query:{gitUrl:t.gitUrl,gitModule:t.module}});window.open(e.href,"_blank")},jobPage:function(t){var e=this.$router.resolve({name:"cicd-image-build-detail",query:{jobId:t}});window.open(e.href,"_blank")},jobHistoryPage:function(t,e){var a=this.$router.resolve({name:"cicd-image-build-history",query:{gitUrl:t,gitModule:e,author:""}});window.open(a.href,"_blank")},pipelinePage:function(){var t=this.$router.resolve({name:"cicd-app-deploy",query:{app:this.currApp}});window.open(t.href,"_blank")}}},j=w,S=(a("0da1"),Object(v["a"])(j,n,i,!1,null,null,null));e["default"]=S.exports},"5df3":function(t,e,a){"use strict";var n=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,a=this._i;return a>=e.length?{value:void 0,done:!0}:(t=n(e,a),this._i+=t.length,{value:t,done:!1})}))},"5e12":function(t,e,a){},"67ab":function(t,e,a){var n=a("ca5a")("meta"),i=a("d3f4"),r=a("69a8"),o=a("86cc").f,l=0,s=Object.isExtensible||function(){return!0},c=!a("79e5")((function(){return s(Object.preventExtensions({}))})),u=function(t){o(t,n,{value:{i:"O"+ ++l,w:{}}})},p=function(t,e){if(!i(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!r(t,n)){if(!s(t))return"F";if(!e)return"E";u(t)}return t[n].i},d=function(t,e){if(!r(t,n)){if(!s(t))return!0;if(!e)return!1;u(t)}return t[n].w},f=function(t){return c&&m.NEED&&s(t)&&!r(t,n)&&u(t),t},m=t.exports={KEY:n,NEED:!1,fastKey:p,getWeak:d,onFreeze:f}},"6a4c":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o}));var n=a("b775");function i(t){return Object(n["a"])({url:"/v1/gitlab/ci-file/create",method:"post",data:t})}function r(t){window.open("/api/v1/gitlab/ci-file/download?fileId="+t)}function o(t){return Object(n["a"])({url:"/v1/gitlab/tags",method:"post",data:t})}},"6d2d":function(t,e,a){},"76fe":function(t,e,a){"use strict";a.d(e,"k",(function(){return i})),a.d(e,"i",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"e",(function(){return l})),a.d(e,"b",(function(){return s})),a.d(e,"f",(function(){return c})),a.d(e,"c",(function(){return u})),a.d(e,"g",(function(){return p})),a.d(e,"h",(function(){return d})),a.d(e,"d",(function(){return f})),a.d(e,"l",(function(){return m})),a.d(e,"j",(function(){return b}));var n=a("b775");function i(t){return Object(n["a"])({url:"/v1/job/search",method:"post",data:t})}function r(t){return Object(n["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function o(t){return Object(n["a"])({url:"/v1/job/build-image",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function c(t,e,a,i){return Object(n["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:i}})}function u(t,e,a,i){return Object(n["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:i}})}function p(t){return Object(n["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(n["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function f(t){return Object(n["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function m(t,e){return Object(n["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function b(t){return Object(n["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},"84d4":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cicd-menu-tabs"},[a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.handleClick},model:{value:t.currTab,callback:function(e){t.currTab=e},expression:"currTab"}},[a("el-tab-pane",{attrs:{label:"aaa",name:"app-deploy"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"guide"}}),t._v("应用发布")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"app-deploy-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("发布记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像构建")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("构建记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-list"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像列表")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"doc",disabled:!0}},[a("span",{staticStyle:{color:"#909399"},attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),t._v(" "),a("a",{attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474"}},[t._v("查看使用手册")])],1)])],1)],1)},i=[],r=(a("7f7f"),{props:{tabName:{type:String,default:""}},mounted:function(){this.currTab=this.tabName},data:function(){return{currTab:""}},computed:{},methods:{handleClick:function(t,e){"app-deploy"===t.name?this.$router.push({name:"cicd-app-deploy"}):"app-deploy-history"===t.name?this.$router.push({name:"cicd-app-deploy-history"}):"image-build"===t.name?this.$router.push({name:"cicd-image-build"}):"image-build-history"===t.name?this.$router.push({name:"cicd-image-build-history"}):"image-list"===t.name?this.$router.push({name:"cicd-image-list"}):"doc"===t.name||this.$message.error("未知操作")}}}),o=r,l=(a("df01"),a("2877")),s=Object(l["a"])(o,n,i,!1,null,null,null);e["a"]=s.exports},"97ab":function(t,e,a){},b39a:function(t,e,a){var n=a("d3f4");t.exports=function(t,e){if(!n(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},bcbd:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.jobs&&t.jobs.length>0?a("el-alert",{attrs:{type:"info"}},[a("template",{slot:"title"},[a("span",[t._v(t._s(t.jobTitle)+"：")]),t._v(" "),t._l(t.jobs,(function(e){return a("el-link",{staticStyle:{margin:"0 10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return t.jobPage(e.id)}}},["CD"===t.jobType?a("div",[t._v("\n          "+t._s(e.params.namespace)+" ("+t._s(e.params.cluster)+")\n        ")]):a("div",[t._v("\n          "+t._s(e.id)+"\n        ")])])}))],2)],2):t._e()],1)},i=[],r=a("76fe"),o={name:"job-runner-alert",props:{app:{type:String,default:""},jobType:{type:String,required:!0},jobTitle:{type:String,default:"运行中的任务"}},data:function(){return{jobs:[]}},computed:{},mounted:function(){this.loadJobs()},watch:{app:function(t){this.jobs=[],this.loadJobs()}},methods:{loadJobs:function(){var t=this;if(this.app){var e={params:{},app:this.app,status:["WAIT","RUNNING"],type:this.jobType,page:1,limit:50};Object(r["k"])(e).then((function(e){t.jobs=e.data.data})).catch((function(t){console.log("load job fail, "+t.message)}))}},jobPage:function(t){var e="";"CI"===this.jobType?e="cicd-image-build-detail":"CD"===this.jobType?e="cicd-app-deploy-detail":this.$message.error("未知的任务类型："+this.jobType);var a=this.$router.resolve({name:e,query:{jobId:t}});window.open(a.href,"_blank")}}},l=o,s=a("2877"),c=Object(s["a"])(l,n,i,!1,null,null,null);e["a"]=c.exports},c26b:function(t,e,a){"use strict";var n=a("86cc").f,i=a("2aeb"),r=a("dcbc"),o=a("9b43"),l=a("f605"),s=a("4a59"),c=a("01f9"),u=a("d53b"),p=a("7a56"),d=a("9e1e"),f=a("67ab").fastKey,m=a("b39a"),b=d?"_s":"size",v=function(t,e){var a,n=f(e);if("F"!==n)return t._i[n];for(a=t._f;a;a=a.n)if(a.k==e)return a};t.exports={getConstructor:function(t,e,a,c){var u=t((function(t,n){l(t,u,e,"_i"),t._t=e,t._i=i(null),t._f=void 0,t._l=void 0,t[b]=0,void 0!=n&&s(n,a,t[c],t)}));return r(u.prototype,{clear:function(){for(var t=m(this,e),a=t._i,n=t._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete a[n.i];t._f=t._l=void 0,t[b]=0},delete:function(t){var a=m(this,e),n=v(a,t);if(n){var i=n.n,r=n.p;delete a._i[n.i],n.r=!0,r&&(r.n=i),i&&(i.p=r),a._f==n&&(a._f=i),a._l==n&&(a._l=r),a[b]--}return!!n},forEach:function(t){m(this,e);var a,n=o(t,arguments.length>1?arguments[1]:void 0,3);while(a=a?a.n:this._f){n(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function(t){return!!v(m(this,e),t)}}),d&&n(u.prototype,"size",{get:function(){return m(this,e)[b]}}),u},def:function(t,e,a){var n,i,r=v(t,e);return r?r.v=a:(t._l=r={i:i=f(e,!0),k:e,v:a,p:n=t._l,n:void 0,r:!1},t._f||(t._f=r),n&&(n.n=r),t[b]++,"F"!==i&&(t._i[i]=r)),t},getEntry:v,setStrong:function(t,e,a){c(t,e,(function(t,a){this._t=m(t,e),this._k=a,this._l=void 0}),(function(){var t=this,e=t._k,a=t._l;while(a&&a.r)a=a.p;return t._t&&(t._l=a=a?a.n:t._t._f)?u(0,"keys"==e?a.k:"values"==e?a.v:[a.k,a.v]):(t._t=void 0,u(1))}),a?"entries":"values",!a,!0),p(e)}}},df01:function(t,e,a){"use strict";a("6d2d")},e0b8:function(t,e,a){"use strict";var n=a("7726"),i=a("5ca1"),r=a("2aba"),o=a("dcbc"),l=a("67ab"),s=a("4a59"),c=a("f605"),u=a("d3f4"),p=a("79e5"),d=a("5cc5"),f=a("7f20"),m=a("5dbc");t.exports=function(t,e,a,b,v,h){var g=n[t],_=g,y=v?"set":"add",x=_&&_.prototype,k={},w=function(t){var e=x[t];r(x,t,"delete"==t||"has"==t?function(t){return!(h&&!u(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return h&&!u(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,a){return e.call(this,0===t?0:t,a),this})};if("function"==typeof _&&(h||x.forEach&&!p((function(){(new _).entries().next()})))){var j=new _,S=j[y](h?{}:-0,1)!=j,O=p((function(){j.has(1)})),C=d((function(t){new _(t)})),F=!h&&p((function(){var t=new _,e=5;while(e--)t[y](e,e);return!t.has(-0)}));C||(_=e((function(e,a){c(e,_,t);var n=m(new g,e,_);return void 0!=a&&s(a,v,n[y],n),n})),_.prototype=x,x.constructor=_),(O||F)&&(w("delete"),w("has"),v&&w("get")),(F||S)&&w(y),h&&x.clear&&delete x.clear}else _=b.getConstructor(e,t,v,y),o(_.prototype,a),l.NEED=!0;return f(_,t),k[t]=_,i(i.G+i.W+i.F*(_!=g),k),h||b.setStrong(_,t,v),_}},f1ae:function(t,e,a){"use strict";var n=a("86cc"),i=a("4630");t.exports=function(t,e,a){e in t?n.f(t,e,i(0,a)):t[e]=a}}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-10a52e78"],{"5b06":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"cms-config-migrate"}}),t._v(" "),a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.form}},[a("el-form-item",{attrs:{label:"模式",prop:"sourceCluster"}},[a("el-select",{model:{value:t.form.op,callback:function(e){t.$set(t.form,"op",e)},expression:"form.op"}},[a("el-option",{attrs:{label:"NodePort转Service",value:"nodeport-to-service"}}),t._v(" "),a("el-option",{attrs:{label:"Service短名转长名",value:"serviceShort-to-serviceLong"}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"环境",prop:"sourceCluster"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{"value-key":"id",filterable:""},model:{value:t.form.sourceCluster,callback:function(e){t.$set(t.form,"sourceCluster",e)},expression:"form.sourceCluster"}},t._l(t.clusterOptions,(function(t){return a("el-option",{key:t.id,attrs:{label:t.cluster+"/"+t.namespace,value:t}})})),1),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[t._v("查询")])],1)],1)],1),t._v(" "),a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.data,size:"mini"}},[a("el-table-column",{attrs:{prop:"name",label:"文件"}}),t._v(" "),a("el-table-column",{attrs:{prop:"profile",label:"配置组"}}),t._v(" "),a("el-table-column",{attrs:{prop:"editor",label:"最后修改人"}}),t._v(" "),a("el-table-column",{attrs:{label:"内容",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return t.cmsContentPreview(e.row)}}},[t._v("查看")])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"addrs",label:"地址","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.repairAddrs,(function(n,o){return a("span",{staticStyle:{margin:"2px 5px",padding:"0 2px",border:"1px solid #e2e2e2",display:"inline-block"}},[t._v("\n            "+t._s(o)+"\n            "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.editCMS(e.row.id)}}},[t._v("修改")]),t._v(" "),a("el-tooltip",{attrs:{content:o+" -> "+n,placement:"right"}},[a("el-button",{staticStyle:{"margin-left":"5px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.batchEditCMS(e.row.profile,o,n)}}},[t._v("批量替换")])],1)],1)}))}}])})],1)],1),t._v(" "),a("div",[a("el-dialog",{attrs:{title:t.cmsPreview.name+" ("+t.cmsPreview.profile+")",visible:t.cmsPreview.visible,width:"50%"},on:{"update:visible":function(e){return t.$set(t.cmsPreview,"visible",e)}}},[a("div",{staticStyle:{"margin-top":"-40px",border:"1px solid #e2e2e2",padding:"5px","max-height":"600px",overflow:"auto"}},[a("pre",[t._v(t._s(t.cmsPreview.content))])])])],1)],1)},o=[],r=(a("7f7f"),a("2d63")),l=a("c1ab"),c=a("a68b"),i={components:{appManageTab:c["a"]},data:function(){return{form:{op:"nodeport-to-service",sourceCluster:{cluster:null,namespace:null}},data:[],loading:!1,cmsPreview:{visible:!1,name:"",profile:"",content:""}}},computed:{clusterOptions:function(){var t,e=[],a=0,n=Object(r["a"])(this.$settings.clusters);try{for(n.s();!(t=n.n()).done;){var o,l=t.value,c=Object(r["a"])(l.namespaces);try{for(c.s();!(o=c.n()).done;){var i=o.value,u={};u.cluster=l.name,u.namespace=i,u.id=a,e.push(u),a++}}catch(s){c.e(s)}finally{c.f()}}}catch(s){n.e(s)}finally{n.f()}return e}},methods:{loadData:function(){var t=this;this.form.sourceCluster.cluster&&this.form.sourceCluster.namespace?(this.loading=!0,Object(l["m"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.op).then((function(e){t.data=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.error("请选择环境")},editCMS:function(t){var e="/api/page/redirect?type=cmsEdit&cmsId=".concat(t,"&_t")+Date.now();window.open(e)},batchEditCMS:function(t,e,a){var n="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(a,"&_t")+Date.now();window.open(n)},cmsContentPreview:function(t){this.cmsPreview.name=t.name,this.cmsPreview.profile=t.profile,this.cmsPreview.content=t.content,this.cmsPreview.visible=!0}}},u=i,s=a("2877"),p=Object(s["a"])(u,n,o,!1,null,"9d6aec22",null);e["default"]=p.exports},a68b:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},o=[],r=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),l=r,c=(a("d54f"),a("2877")),i=Object(c["a"])(l,n,o,!1,null,null,null);e["a"]=i.exports},c1ab:function(t,e,a){"use strict";a.d(e,"i",(function(){return o})),a.d(e,"k",(function(){return r})),a.d(e,"y",(function(){return l})),a.d(e,"z",(function(){return c})),a.d(e,"d",(function(){return i})),a.d(e,"g",(function(){return u})),a.d(e,"C",(function(){return s})),a.d(e,"E",(function(){return p})),a.d(e,"x",(function(){return d})),a.d(e,"b",(function(){return m})),a.d(e,"f",(function(){return f})),a.d(e,"B",(function(){return v})),a.d(e,"D",(function(){return b})),a.d(e,"w",(function(){return h})),a.d(e,"F",(function(){return g})),a.d(e,"l",(function(){return y})),a.d(e,"e",(function(){return _})),a.d(e,"a",(function(){return j})),a.d(e,"A",(function(){return w})),a.d(e,"j",(function(){return O})),a.d(e,"h",(function(){return x})),a.d(e,"r",(function(){return C})),a.d(e,"u",(function(){return $})),a.d(e,"v",(function(){return k})),a.d(e,"n",(function(){return S})),a.d(e,"o",(function(){return P})),a.d(e,"s",(function(){return E})),a.d(e,"t",(function(){return z})),a.d(e,"c",(function(){return D})),a.d(e,"p",(function(){return M})),a.d(e,"q",(function(){return N})),a.d(e,"m",(function(){return T}));var n=a("b775");function o(t){return Object(n["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function r(t){return Object(n["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function l(t){return Object(n["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function c(t){return Object(n["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function i(){return Object(n["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function u(t){return Object(n["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(n["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,a,o,r,l,c,i){return Object(n["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(r,"&fixVersion=").concat(e,"&suffixVersion=").concat(a,"&message=").concat(o,"&dependencyCheck=").concat(l,"&parentPom=").concat(c),method:"post",data:i})}function v(t){return Object(n["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function h(t){return Object(n["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,a,o){return Object(n["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&overrideNamespace=").concat(o),method:"post"})}function _(t,e,a,o,r){return Object(n["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(a,"&remark=").concat(o,"&dryRun=").concat(r),method:"post"})}function j(){return Object(n["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function w(){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function O(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function x(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function C(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function $(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function k(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function S(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function P(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function E(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function z(t,e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function D(t){return Object(n["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function M(t){return Object(n["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function N(t){return Object(n["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function T(t,e,a){return Object(n["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(a),method:"get"})}},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);
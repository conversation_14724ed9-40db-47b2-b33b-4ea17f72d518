(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0c85b64f"],{"11d1":function(t,e,a){},1316:function(t,e,a){},"1c4c":function(t,e,a){"use strict";var i=a("9b43"),n=a("5ca1"),o=a("4bf8"),l=a("1fa8"),r=a("33a4"),s=a("9def"),c=a("f1ae"),p=a("27ee");n(n.S+n.F*!a("5cc5")((function(t){Array.from(t)})),"Array",{from:function(t){var e,a,n,u,d=o(t),m="function"==typeof this?this:Array,f=arguments.length,v=f>1?arguments[1]:void 0,b=void 0!==v,g=0,h=p(d);if(b&&(v=i(v,f>2?arguments[2]:void 0,2)),void 0==h||m==Array&&r(h))for(e=s(d.length),a=new m(e);e>g;g++)c(a,g,b?v(d[g],g):d[g]);else for(u=h.call(d),a=new m;!(n=u.next()).done;g++)c(a,g,b?l(u,v,[n.value,g],!0):n.value);return a.length=g,a}})},"1e42":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},n=[],o=(a("a481"),a("25ca")),l=a("21a6"),r=a.n(l),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=o["a"].table_to_book(t,{raw:!0}),a=o["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var i="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";r.a.saveAs(new Blob([a],{type:"application/octet-stream"}),i)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,p=a("2877"),u=Object(p["a"])(c,i,n,!1,null,null,null);e["a"]=u.exports},"28a5":function(t,e,a){"use strict";var i=a("aae3"),n=a("cb7c"),o=a("ebd6"),l=a("0390"),r=a("9def"),s=a("5f1b"),c=a("520a"),p=a("79e5"),u=Math.min,d=[].push,m="split",f="length",v="lastIndex",b=4294967295,g=!p((function(){RegExp(b,"y")}));a("214f")("split",2,(function(t,e,a,p){var h;return h="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[f]||2!="ab"[m](/(?:ab)*/)[f]||4!="."[m](/(.?)(.?)/)[f]||"."[m](/()()/)[f]>1||""[m](/.?/)[f]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!i(t))return a.call(n,t,e);var o,l,r,s=[],p=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),u=0,m=void 0===e?b:e>>>0,g=new RegExp(t.source,p+"g");while(o=c.call(g,n)){if(l=g[v],l>u&&(s.push(n.slice(u,o.index)),o[f]>1&&o.index<n[f]&&d.apply(s,o.slice(1)),r=o[0][f],u=l,s[f]>=m))break;g[v]===o.index&&g[v]++}return u===n[f]?!r&&g.test("")||s.push(""):s.push(n.slice(u)),s[f]>m?s.slice(0,m):s}:"0"[m](void 0,0)[f]?function(t,e){return void 0===t&&0===e?[]:a.call(this,t,e)}:a,[function(a,i){var n=t(this),o=void 0==a?void 0:a[e];return void 0!==o?o.call(a,n,i):h.call(String(n),a,i)},function(t,e){var i=p(h,t,this,e,h!==a);if(i.done)return i.value;var c=n(t),d=String(this),m=o(c,RegExp),f=c.unicode,v=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(g?"y":"g"),_=new m(g?c:"^(?:"+c.source+")",v),y=void 0===e?b:e>>>0;if(0===y)return[];if(0===d.length)return null===s(_,d)?[d]:[];var x=0,w=0,S=[];while(w<d.length){_.lastIndex=g?w:0;var k,C=s(_,g?d:d.slice(w));if(null===C||(k=u(r(_.lastIndex+(g?0:w)),d.length))===x)w=l(d,w,f);else{if(S.push(d.slice(x,w)),S.length===y)return S;for(var D=1;D<=C.length-1;D++)if(S.push(C[D]),S.length===y)return S;w=x=k}}return S.push(d.slice(x)),S}]}))},"2fdb":function(t,e,a){"use strict";var i=a("5ca1"),n=a("d2c8"),o="includes";i(i.P+i.F*a("5147")(o),"String",{includes:function(t){return!!~n(this,t,o).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"343d":function(t,e,a){"use strict";a("1316")},"4ad4":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-address-wrapper"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("访问地址")]),t._v(" "),a("span",{staticStyle:{display:"inline-block","margin-left":"20px","font-size":"13px"}},[t._v("\n        ( 应用："+t._s(this.app)+" | 环境："+t._s(this.namespace)+" | 集群："+t._s(this.cluster)+" )\n      ")])]),t._v(" "),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.address}},[a("el-table-column",{attrs:{prop:"name",width:"180",label:"端口名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"port",width:"180",label:"端口号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"protocol",width:"100",label:"协议"}}),t._v(" "),a("el-table-column",{attrs:{label:"集群内访问地址"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.clusterInnerAddress,(function(e,i){return a("p",[a("b",{staticStyle:{"padding-right":"10px"}},[t._v("地址"+t._s(i+1)+":")]),t._v(t._s(e)+"\n            ")])}))}}])}),t._v(" "),a("el-table-column",{attrs:{label:"集群外访问地址"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.clusterOuterAddress,(function(i,n){return a("p",[a("b",{staticStyle:{"padding-right":"10px"}},[t._v("地址"+t._s(n+1)+":")]),t._v(t._s(i)+"\n              "),e.row.clusterOuterAddress&&e.row.clusterOuterAddress.length>1&&0===n?a("span",{staticStyle:{"padding-left":"20px"}},[t._v("\n                  (推荐)\n                ")]):t._e()])}))}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{domProps:{innerHTML:t._s(e.row.remark)}})]}}])})],1)],1)])],1)},n=[],o=a("b562"),l={name:"AppAddress",props:{cluster:{type:String,require:!0},namespace:{type:String,require:!0},app:{type:String,require:!0}},data:function(){return{loading:!1,address:[]}},watch:{cluster:function(t){this.showAddress()},namespace:function(t){this.showAddress()},app:function(t){this.showAddress()}},computed:{},mounted:function(){this.showAddress()},methods:{showAddress:function(){var t=this;this.cluster?this.namespace?this.app?(this.loading=!0,Object(o["m"])(this.cluster,this.namespace,this.app).then((function(e){t.address=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.warning("缺少参数 app"):this.$message.warning("缺少参数 namespace"):this.$message.warning("缺少参数 cluster")}}},r=l,s=(a("de9b"),a("2877")),c=Object(s["a"])(r,i,n,!1,null,null,null);e["a"]=c.exports},"4f7f":function(t,e,a){"use strict";var i=a("c26b"),n=a("b39a"),o="Set";t.exports=a("e0b8")(o,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return i.def(n(this,o),t=0===t?0:t,t)}},i)},"504c":function(t,e,a){var i=a("9e1e"),n=a("0d58"),o=a("6821"),l=a("52a7").f;t.exports=function(t){return function(e){var a,r=o(e),s=n(r),c=s.length,p=0,u=[];while(c>p)a=s[p++],i&&!l.call(r,a)||u.push(t?[a,r[a]]:r[a]);return u}}},5147:function(t,e,a){var i=a("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(a){try{return e[i]=!1,!"/./"[t](e)}catch(n){}}return!0}},"51a9":function(t,e,a){"use strict";a.d(e,"c",(function(){return n})),a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return l})),a.d(e,"l",(function(){return r})),a.d(e,"m",(function(){return s})),a.d(e,"a",(function(){return c})),a.d(e,"f",(function(){return p})),a.d(e,"i",(function(){return u})),a.d(e,"j",(function(){return d})),a.d(e,"k",(function(){return m})),a.d(e,"n",(function(){return f})),a.d(e,"g",(function(){return v})),a.d(e,"b",(function(){return b})),a.d(e,"h",(function(){return g})),a.d(e,"o",(function(){return h}));var i=a("b775");function n(t){return Object(i["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(i["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function l(t,e,a){return Object(i["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:a}})}function r(t){return Object(i["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function c(){return Object(i["a"])({url:"/v1/pipeline/all",method:"get"})}function p(t){return Object(i["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function u(t){return Object(i["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(i["a"])({url:"/v1/pipeline",method:"post",data:t})}function m(t){return Object(i["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function f(t){return Object(i["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function v(t,e,a,n){return Object(i["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:n}})}function b(t){return Object(i["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function g(t,e,a,n){return Object(i["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:n}})}function h(t){return Object(i["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"579f":function(t,e,a){},"57c3":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[this.maintainIsOpen?a("div",{staticStyle:{margin:"0 auto",padding:"10px"}},[a("el-alert",{attrs:{title:"",closable:!1,type:"warning"}},[a("template",{slot:"title"},[a("div",{staticStyle:{"line-height":"18px",color:"orangered","font-weight":"bold"}},[a("div",{staticStyle:{"font-size":"16px"}},[a("span",{staticStyle:{color:"orangered"}},[a("i",{staticClass:"el-icon-warning"})]),t._v(" "),a("span",{staticStyle:{"padding-left":"5px"}},[t._v("系统提示")])]),t._v(" "),a("div",{staticStyle:{"padding-top":"10px","font-size":"14px"},domProps:{innerHTML:t._s(this.maintainDesc)}})])])],2)],1):a("div",{staticStyle:{display:"none"}})])},n=[],o={name:"maintain-alert",props:{maintainType:{type:String,required:!0}},data:function(){return{}},watch:{},computed:{maintainIsOpen:function(){return"CD"===this.maintainType.toUpperCase()?this.$settings.maintain.cd.open:"CI"===this.maintainType.toUpperCase()&&this.$settings.maintain.ci.open},maintainDesc:function(){return"CD"===this.maintainType.toUpperCase()?this.$settings.maintain.cd.desc:"CI"===this.maintainType.toUpperCase()?this.$settings.maintain.ci.desc:""}},mounted:function(){},methods:{}},l=o,r=a("2877"),s=Object(r["a"])(l,i,n,!1,null,null,null);e["a"]=s.exports},"5df3":function(t,e,a){"use strict";var i=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,a=this._i;return a>=e.length?{value:void 0,done:!0}:(t=i(e,a),this._i+=t.length,{value:t,done:!1})}))},"641a":function(t,e,a){"use strict";a("11d1")},6762:function(t,e,a){"use strict";var i=a("5ca1"),n=a("c366")(!0);i(i.P,"Array",{includes:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"67ab":function(t,e,a){var i=a("ca5a")("meta"),n=a("d3f4"),o=a("69a8"),l=a("86cc").f,r=0,s=Object.isExtensible||function(){return!0},c=!a("79e5")((function(){return s(Object.preventExtensions({}))})),p=function(t){l(t,i,{value:{i:"O"+ ++r,w:{}}})},u=function(t,e){if(!n(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,i)){if(!s(t))return"F";if(!e)return"E";p(t)}return t[i].i},d=function(t,e){if(!o(t,i)){if(!s(t))return!0;if(!e)return!1;p(t)}return t[i].w},m=function(t){return c&&f.NEED&&s(t)&&!o(t,i)&&p(t),t},f=t.exports={KEY:i,NEED:!1,fastKey:u,getWeak:d,onFreeze:m}},"699c":function(t,e,a){},"6a4c":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return l}));var i=a("b775");function n(t){return Object(i["a"])({url:"/v1/gitlab/ci-file/create",method:"post",data:t})}function o(t){window.open("/api/v1/gitlab/ci-file/download?fileId="+t)}function l(t){return Object(i["a"])({url:"/v1/gitlab/tags",method:"post",data:t})}},"6d2d":function(t,e,a){},"75fc":function(t,e,a){"use strict";var i=a("a745"),n=a.n(i),o=a("db2a");function l(t){if(n()(t))return Object(o["a"])(t)}var r=a("67bb"),s=a.n(r),c=a("5d58"),p=a.n(c),u=a("774e"),d=a.n(u);function m(t){if("undefined"!==typeof s.a&&null!=t[p.a]||null!=t["@@iterator"])return d()(t)}var f=a("e630");function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t){return l(t)||m(t)||Object(f["a"])(t)||v()}a.d(e,"a",(function(){return b}))},"768b":function(t,e,a){"use strict";var i=a("a745"),n=a.n(i);function o(t){if(n()(t))return t}var l=a("67bb"),r=a.n(l),s=a("5d58"),c=a.n(s);function p(t,e){var a=null==t?null:"undefined"!==typeof r.a&&t[c.a]||t["@@iterator"];if(null!=a){var i,n,o=[],l=!0,s=!1;try{for(a=a.call(t);!(l=(i=a.next()).done);l=!0)if(o.push(i.value),e&&o.length===e)break}catch(p){s=!0,n=p}finally{try{l||null==a["return"]||a["return"]()}finally{if(s)throw n}}return o}}var u=a("e630");function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){return o(t)||p(t,e)||Object(u["a"])(t,e)||d()}a.d(e,"a",(function(){return m}))},"76fe":function(t,e,a){"use strict";a.d(e,"k",(function(){return n})),a.d(e,"i",(function(){return o})),a.d(e,"a",(function(){return l})),a.d(e,"e",(function(){return r})),a.d(e,"b",(function(){return s})),a.d(e,"f",(function(){return c})),a.d(e,"c",(function(){return p})),a.d(e,"g",(function(){return u})),a.d(e,"h",(function(){return d})),a.d(e,"d",(function(){return m})),a.d(e,"l",(function(){return f})),a.d(e,"j",(function(){return v}));var i=a("b775");function n(t){return Object(i["a"])({url:"/v1/job/search",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function l(t){return Object(i["a"])({url:"/v1/job/build-image",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function c(t,e,a,n){return Object(i["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:n}})}function p(t,e,a,n){return Object(i["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:n}})}function u(t){return Object(i["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(i["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(i["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function f(t,e){return Object(i["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function v(t){return Object(i["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},"837b":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{style:{fontSize:t.fontsize}},[t.pipeline.app?a("div",{staticClass:"expand-row-wrapper"},[a("div",{staticClass:"expand-row-item"},[a("label",[t._v("备注")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.remark||"--"))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("k8s集群")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.cluster))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("基础镜像")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.baseImage.substring(t.pipeline.baseImage.lastIndexOf("/")+1)))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("资源池调度")]),t._v(" "),a("span",[t._v("调度策略 ("+t._s(t.pipeline.schedule.strategy)+")")]),t._v(" "),a("span",[t._v("资源池 ("+t._s(t.pipeline.schedule.node)+")")])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("部署策略")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.deployStrategy))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("选项开关")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px",display:"inline-block"}},[a("el-checkbox-group",{model:{value:t.pipeOptions,callback:function(e){t.pipeOptions=e},expression:"pipeOptions"}},t._l(t.pipeOptionsAll,(function(e){return a("el-checkbox",{staticClass:"pipeline-option",style:{fontSize:t.fontsize},attrs:{label:e,size:"mini",disabled:""}})})),1)],1)]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("健康检测")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px",display:"inline-block"}},[a("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"启动检测",checked:t.pipeline.startupProbe.enable,size:"mini",disabled:""}}),t._v(" "),a("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"存活检查",checked:t.pipeline.livenessProbe.enable,size:"mini",disabled:""}}),t._v(" "),a("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"就绪检测",checked:t.pipeline.readinessProbe.enable,size:"mini",disabled:""}})],1)]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("JVM参数")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.jvmOpts||"--"))])]),t._v(" "),t.pipeline.pvc.enable?a("div",{staticClass:"expand-row-item"},[a("label",[t._v("持久存储")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.pvc.enable?t.pipeline.pvc.name:""))])]):t._e(),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("关闭前保留时间")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.preStopRetainSeconds))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("关闭前回调地址")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.preStopWebhook||"--"))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("Eolinker任务数")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.eolinkerIDs&&t.pipeline.eolinkerIDs.length>0?t.pipeline.eolinkerIDs.length:"--"))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("Webhook")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.webhook.url||"--"))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("端口")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.ports.map((function(t){return t.value})).join(",")))])]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("自定义环境变量")]),t._v(" "),a("span",[t._l(t.pipeline.envs,(function(e,i){return["USER"===e.type?a("div",{staticStyle:{display:"inline-block","padding-right":"20px"}},[t._v("\n            "+t._s(e.name)+"="+t._s(e.value)+"\n          ")]):t._e()]}))],2)]),t._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[t._v("创建时间")]),t._v(" "),a("span",[t._v(t._s(t.pipeline.createdTime))])])]):a("div",[t._v("\n    数据丢失\n  ")])])},n=[],o=a("768b"),l=(a("ac6a"),a("ffc1"),a("b144")),r={props:{pipeline:{type:Object,default:function(){return{}},required:!0},fontsize:{type:String,default:"12px",required:!1}},mounted:function(){for(var t=0,e=Object.entries(this.pipeline.options);t<e.length;t++){var a=Object(o["a"])(e[t],2),i=a[0],n=a[1];this.pipeOptionsAll.push(Object(l["b"])(i)),n&&this.pipeOptions.push(Object(l["b"])(i))}this.pipeOptionsOrigin=Object(l["a"])(this.pipeOptions)},computed:{},data:function(){return{pipeOptionsOrigin:[],pipeOptions:[],pipeOptionsAll:[]}},methods:{}},s=r,c=(a("d152"),a("2877")),p=Object(c["a"])(s,i,n,!1,null,null,null);e["a"]=p.exports},"84d4":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cicd-menu-tabs"},[a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.handleClick},model:{value:t.currTab,callback:function(e){t.currTab=e},expression:"currTab"}},[a("el-tab-pane",{attrs:{label:"aaa",name:"app-deploy"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"guide"}}),t._v("应用发布")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"app-deploy-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("发布记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像构建")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("构建记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-list"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像列表")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"doc",disabled:!0}},[a("span",{staticStyle:{color:"#909399"},attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),t._v(" "),a("a",{attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474"}},[t._v("查看使用手册")])],1)])],1)],1)},n=[],o=(a("7f7f"),{props:{tabName:{type:String,default:""}},mounted:function(){this.currTab=this.tabName},data:function(){return{currTab:""}},computed:{},methods:{handleClick:function(t,e){"app-deploy"===t.name?this.$router.push({name:"cicd-app-deploy"}):"app-deploy-history"===t.name?this.$router.push({name:"cicd-app-deploy-history"}):"image-build"===t.name?this.$router.push({name:"cicd-image-build"}):"image-build-history"===t.name?this.$router.push({name:"cicd-image-build-history"}):"image-list"===t.name?this.$router.push({name:"cicd-image-list"}):"doc"===t.name||this.$message.error("未知操作")}}}),l=o,r=(a("df01"),a("2877")),s=Object(r["a"])(l,i,n,!1,null,null,null);e["a"]=s.exports},9684:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container app-deploy-container",staticStyle:{margin:"0",padding:"0"},attrs:{"element-loading-text":"数据加载中"}},[a("menu-tabs",{attrs:{"tab-name":"app-deploy"}}),t._v(" "),a("maintain-alert",{attrs:{"maintain-type":"cd"}}),t._v(" "),a("app-selector2",{attrs:{"show-detail":!0,"update-history":!0},on:{change:t.changeCurrApp}}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:this.currApp,expression:"this.currApp"}],staticStyle:{"min-height":"180px",margin:"10px"}},[a("job-runner-alert",{ref:"jobRunnerAlert",attrs:{app:this.currApp,"job-type":"CD"}}),t._v(" "),a("div",{staticStyle:{"padding-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-position"},on:{click:t.batchDeploy}},[t._v("批量发布")]),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建流程")]),t._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",icon:"el-icon-price-tag"},on:{click:t.imageBuildPage}},[t._v("构建镜像")]),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-document"},on:{click:t.offlineAppDialog}},[t._v("服务下线说明")]),t._v(" "),a("el-button",{staticStyle:{"margin-left":"10px",color:"#bbb"},attrs:{type:"text",icon:"el-icon-price-console"},on:{click:t.multiClusterPodExecPage}},[a("svg-icon",{attrs:{"icon-class":"console"}}),t._v("\n          批量进入第一个容器\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"同时进入所勾选发布流程下的第一个Pod容器里"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),a("el-tooltip",{staticStyle:{display:"none"},attrs:{placement:"top"}},[a("template",{slot:"content"},[a("p",[t._v("背景：在「研发服务模块」对象中，服务等级为 L0 和 L1 的服务，需要在「后端服务健康度 Review」对象中创建数据以进行健康度评估。")]),t._v(" "),a("p",[t._v("功能：发布系统提供了一键对象数据创建功能，并对一些固定内容字段（如 Pod资源 和 Pod自动扩容策略）的值进行填充。其余字段的内容将默认填充为 todo，需相关人员自行补充完整。")])]),t._v(" "),a("el-button",{staticStyle:{"font-size":"12px","margin-right":"20px",color:"#bbb"},attrs:{type:"text"},on:{click:t.createAppHealthReviewInCRM}},[t._v(" 【健康度CRM对象】")])],2),t._v(" "),a("el-button",{staticStyle:{"margin-left":"30px",color:"#E6A23C"},attrs:{type:"text",icon:"el-icon-position"},on:{click:t.urgentDeploy}},[t._v("紧急发布")])],1),t._v(" "),a("div",{staticStyle:{float:"right"}},[a("export-button",{attrs:{"table-ref":this.$refs.pipelineTable}}),t._v(" "),a("span",{staticStyle:{"font-size":"12px",color:"#E6A23C","font-weight":"bold"}},[t._v("状态筛选：")]),t._v(" "),a("el-select",{staticStyle:{width:"120px","margin-right":"20px"},attrs:{size:"small"},on:{change:t.pipelineFilter},model:{value:t.statusFilter,callback:function(e){t.statusFilter=e},expression:"statusFilter"}},t._l(t.statusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" "),a("el-radio-group",{attrs:{size:"mini",fill:"#909399"},on:{input:t.pipelineFilter},model:{value:t.cloudCategoryCurr,callback:function(e){t.cloudCategoryCurr=e},expression:"cloudCategoryCurr"}},[a("el-radio-button",{attrs:{label:"_all_"}},[t._v("所有\n            "),a("span",{staticStyle:{color:"#fff","border-radius":"10px",display:"inline-block","line-height":"1.4em",width:"20px"}},[t._v("\n              \n            ")])]),t._v(" "),t._l(t.cloudCategories,(function(e){return a("el-radio-button",{attrs:{label:e.name}},[t._v(t._s(e.desc)+"\n            "),a("span",{staticStyle:{color:"#fff","background-color":"#409eff","border-radius":"10px",display:"inline-block","line-height":"1.4em",width:"20px"}},[t._v("\n              "+t._s(e.pipelineCount)+"\n            ")])])}))],2)],1),t._v(" "),a("div",{staticStyle:{clear:"both"}})]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"pipelineTable",attrs:{data:t.tableData,"highlight-selection-row":!0,"element-loading-text":"数据加载中...",border:"",fit:"","cell-style":t.cellStyle}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"40"}}),t._v(" "),a("el-table-column",{attrs:{type:"expand",label:"详情",width:"50"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("pipeline-expand",{attrs:{pipeline:t.row}})]}}])}),t._v(" "),a("el-table-column",{attrs:{type:"index",width:"40"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态",width:"70",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["enabled"===e.row.status?a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"success",size:"mini"}},[t._v("\n            "+t._s(t.convertStatus(e.row.status))+"\n          ")]):a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",size:"small"}},[t._v("\n            "+t._s(t.convertStatus(e.row.status))+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:t.cicdOneKey?"310px":"220px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"12px"}},[a("router-link",{attrs:{to:{name:"pod-index",query:{cluster:e.row.cluster,namespace:e.row.namespace,app:e.row.app}},target:"_blank"}},[a("i",{staticClass:"el-icon-menu",staticStyle:{color:"#409EFF","font-weight":"500"}},[t._v("实例")])]),t._v(" "),a("router-link",{staticStyle:{"margin-left":"5px"},attrs:{to:{name:"app-pipeline-edit",query:{pipelineId:e.row.id}},target:"_blank"}},[a("i",{staticClass:"el-icon-edit",staticStyle:{color:"#409EFF","font-weight":"500"}},[t._v("编辑")])]),t._v(" "),a("el-dropdown",{staticStyle:{"margin-left":"10px"},on:{command:t.manageDropdownCommand}},[a("span",{staticClass:"el-dropdown-link",staticStyle:{"font-size":"12px"}},[t._v("\n                更多"),a("i",{staticClass:"el-icon-arrow-down "})]),t._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-link",command:"showAddr##"+e.row.id}},[t._v("查看访问地址")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-position",command:"deployHistory##"+e.row.id}},[t._v("发布历史")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-tickets",command:"bugfixBranch##"+e.row.id}},[t._v("创建Bugfix分支")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-tickets",command:"appLog##"+e.row.id}},[t._v("Logback日志")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-copy-document",command:"clone##"+e.row.id}},[t._v("克隆")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-document-copy",command:"syncConfigToOther##"+e.row.id}},[t._v("配置同步到其他流程")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-document-copy",command:"k8sDeployment##"+e.row.id}},[t._v("k8s部署配置")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-setting",command:"updateStatus##"+e.row.id}},[t._v("修改状态")]),t._v(" "),a("el-dropdown-item",{staticStyle:{color:"orange"},attrs:{icon:"el-icon-scissors",command:"remove##"+e.row.id}},[t._v("下线")])],1)],1),t._v(" "),a("el-tooltip",{attrs:{effect:"dark",content:"同时执行【镜像构建】和【服务发布】",placement:"top"}},[t.cicdOneKey?a("el-button",{staticStyle:{padding:"5px 7px","margin-left":"10px"},attrs:{type:"success",size:"mini",disabled:"enabled"!==e.row.status},on:{click:function(a){return t.buildAndDeployDialog(e.row)}}},[t._v("\n                构建+发布\n              ")]):t._e()],1),t._v(" "),a("el-button",{staticClass:"el-icon-position",staticStyle:{padding:"5px 7px","margin-left":"10px"},attrs:{type:"primary",size:"mini",disabled:"enabled"!==e.row.status},on:{click:function(a){return t.showDeployDialog([e.row])}}},[t._v("发布\n            ")])],1),t._v(" "),a("div",[t._l(e.row.extraAttr.clusterLabels,(function(t){return a("el-alert",{key:t,staticClass:"env-label-alert",attrs:{title:t,type:"info","show-icon":!0,closable:!1}})})),t._v(" "),"fstest-metadata"===e.row.namespace?a("el-alert",{staticClass:"env-label-alert",attrs:{title:"元数据专用环境,咨询李磊",type:"info","show-icon":!0,closable:!1}}):t._e(),t._v(" "),e.row.extraAttr.deregisterPodSize>0?a("el-alert",{staticClass:"env-label-alert",attrs:{title:"摘除了"+e.row.extraAttr.deregisterPodSize+"个pod，不受发版影响",type:"info","show-icon":!0,closable:!1}}):t._e()],2)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace",width:"160"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n          运行环境\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("template",{slot:"content"},[a("div",[t._v("第一行为运行环境名，第二行为k8s集群名")])]),t._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)]}},{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-weight":"bold"}},[t._v("\n            "+t._s(e.row.namespace)+"\n          ")]),t._v(" "),a("div",{staticStyle:{"font-weight":"normal",color:"#666","font-size":"10px","margin-top":"-8px"}},[t._v("\n            "+t._s(e.row.cluster)+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"",width:"180"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n          云环境\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("template",{slot:"content"},[a("div",[t._v("第一行为云环境名，第二行为云类型")]),t._v(" "),a("hr"),t._v(" "),a("div",[t._v("云类型分为：")]),t._v(" "),a("p",[t._v("纷享云：部署在纷享机房")]),t._v(" "),a("p",[t._v("公有云：部署在纷享购买的公有云平台（阿里云、华为云、腾讯云、AWS）")]),t._v(" "),a("p",[t._v("专属云：部署在客户购买的三大公有云平台（阿里云、华为云、腾讯云）")]),t._v(" "),a("p",[t._v("混合云：部署在客户自建机房")])]),t._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)]}},{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-weight":"bold"}},[a("div",[t._v(t._s(e.row.extraAttr.clusterSummary))]),t._v(" "),a("div",[e.row.extraAttr&&e.row.extraAttr.cloudCategoryDesc?a("div",{staticStyle:{"font-weight":"normal",color:"#666","font-size":"10px","margin-top":"-8px"}},[t._v("\n                "+t._s(e.row.extraAttr.cloudCategoryDesc)+"\n              ")]):t._e()])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"部署模块",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.appModules.length)+"\n          "),a("el-popover",{attrs:{placement:"right",width:"680",align:"center",trigger:"click"}},[a("el-table",{staticStyle:{"max-height":"460px","overflow-y":"auto"},attrs:{data:e.row.appModules}},[a("el-table-column",{attrs:{type:"index",width:"40"}}),t._v(" "),a("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),t._v(" "),a("el-table-column",{attrs:{prop:"module",label:"子模块"}}),t._v(" "),a("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}})],1),t._v(" "),a("el-button",{staticStyle:{"margin-left":"5px","font-size":"12px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("查看")])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"运行版本",prop:"extraAttr.deployTag","show-overflow-tooltip":"","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.extraAttr.deployModules?a("div",{staticStyle:{display:"inline-block","margin-right":"5px"}},[a("el-popover",{attrs:{placement:"left",width:"760",trigger:"click"}},[a("el-table",{staticStyle:{"max-height":"480px","overflow-y":"auto"},attrs:{data:e.row.extraAttr.deployModules}},[a("el-table-column",{attrs:{type:"index",width:"40"}}),t._v(" "),a("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),t._v(" "),a("el-table-column",{attrs:{prop:"module",label:"子模块"}}),t._v(" "),a("el-table-column",{attrs:{prop:"tag",label:"代码分支/标签"}}),t._v(" "),a("el-table-column",{attrs:{prop:"commitIdShort",label:"提交ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}})],1),t._v(" "),a("el-button",{staticStyle:{margin:"0",padding:"0","font-size":"12px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("详情")])],1)],1):t._e(),t._v(" "),null===e.row.extraAttr.deployTag?a("span",[a("i",{staticClass:"el-icon-loading"})]):a("span",[t._v(t._s(e.row.extraAttr.deployTag))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"最近发布",width:"110","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"10px","line-height":"1.8em",overflow:"hidden"}},[e.row.extraAttr.lastDeployTime?a("div",[t._v(t._s(e.row.extraAttr.lastDeployTime))]):t._e(),t._v(" "),e.row.extraAttr.lastDeployUser?a("div",[t._v("发布人："+t._s(e.row.extraAttr.lastDeployUser))]):t._e()])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"",width:"110"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n          扩缩容\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("template",{slot:"content"},[a("p",[t._v("扩缩容类型：最低副本数 → 最大副本数")]),t._v(" "),a("p",[t._v("比如：")]),t._v(" "),a("hr"),t._v(" "),a("p",[t._v("自动: 4 → 12 （配置了自动扩缩容，且可扩容的最大副本数为12）")]),t._v(" "),a("p",[t._v("定时: 4 → 8 （配置了定时扩缩容，且可扩容的最大副本数为8）")])]),t._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)]}},{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"10px","line-height":"1.8em",overflow:"hidden"}},[null===e.row.extraAttr.autoScaleV2MaxReplicas?a("div",[a("i",{staticClass:"el-icon-loading"})]):a("div",["-"!==e.row.extraAttr.autoScaleV2MaxReplicas?a("div",{staticStyle:{position:"relative"}},[a("b",[t._v("自动:")]),t._v("\n                "+t._s(e.row.extraAttr.autoScaleV2MinReplicas)+" → "+t._s(e.row.extraAttr.autoScaleV2MaxReplicas)+"\n                "),a("el-button",{staticStyle:{"font-size":"10px",padding:"0",margin:"0"},attrs:{type:"text"},on:{click:function(a){return t.scalePage(e.row,"autoscalev2")}}},[t._v("查看")])],1):t._e(),t._v(" "),"-"!==e.row.extraAttr.cronScaleMaxReplicas?a("div",[a("b",[t._v("定时:")]),t._v("\n                "+t._s(e.row.extraAttr.cronScaleMinReplicas)+" → "+t._s(e.row.extraAttr.cronScaleMaxReplicas)+"\n                "),a("el-button",{staticStyle:{"font-size":"10px",padding:"0",margin:"0"},attrs:{type:"text"},on:{click:function(a){return t.scalePage(e.row,"cronscale")}}},[t._v("查看")])],1):t._e()])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"实例（Pod)",align:"center"}},[a("el-table-column",{attrs:{width:"80",align:"center",property:"replicas"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n            配置数\n            "),a("el-tooltip",{attrs:{effect:"dark",content:"发布流程里配置的实例数",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"80",align:"center",label:"运行数"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n            运行数\n            "),a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的实例数",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return[null===e.row.extraAttr.runningPodNum?a("span",[a("i",{staticClass:"el-icon-loading"})]):a("span",[t._v(t._s(e.row.extraAttr.runningPodNum))]),t._v(" "),e.row.extraAttr.runningPodNum>0?a("el-button",{staticStyle:{"margin-left":"5px","font-size":"12px"},attrs:{slot:"reference",type:"text"},on:{click:function(a){return t.showPodTable(e.row)}},slot:"reference"},[t._v("\n              查看\n            ")]):t._e()]}}])})],1),t._v(" "),a("el-table-column",{attrs:{label:"CPU",width:"90",prop:"resources.limitCPU"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n          CPU\n          "),a("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return e.row.resources?[t._v("\n          "+t._s(e.row.resources.requestCPU.toFixed(1))+" - "+t._s(e.row.resources.limitCPU.toFixed(1))+"\n        ")]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{width:"110px",prop:"resources.limitMemory"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n          内存 (MB)\n          "),a("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return e.row.resources?[t._v("\n          "+t._s(e.row.resources.requestMemory)+" - "+t._s(e.row.resources.limitMemory)+"\n        ")]:void 0}}],null,!0)})],1)],1),t._v(" "),a("div",{staticStyle:{margin:"10px"}},[a("pipeline-doc")],1),t._v(" "),a("el-dialog",{staticClass:"deploy-dialog",attrs:{visible:t.deployData.dialogVisible,"close-on-click-modal":!1,top:"5vh",width:"960px"},on:{"update:visible":function(e){return t.$set(t.deployData,"dialogVisible",e)}}},[a("template",{slot:"title"},[t._v("\n      应用发布 "),a("small",{staticStyle:{"padding-left":"20px",color:"#666"}},[t._v("（默认会选择当前运行的版本号）")])]),t._v(" "),a("el-form",{ref:"dialogDeployForm",attrs:{model:t.deployData.form,"label-width":"100px"}},[t._l(t.deployData.imageOptions,(function(e,i){return a("div",[a("el-form-item",{staticClass:"module-git-url",attrs:{label:""}},[a("span",{staticStyle:{color:"#b4532a","font-weight":"bold"}},[t._v("\n            Git地址---模块： "+t._s(e.gitUrl)+" --- "+t._s(e.gitModule?e.gitModule:"[空]")+"\n          ")])]),t._v(" "),a("el-form-item",{attrs:{label:"镜像版本"}},[a("div",{staticStyle:{display:"inline-block",width:"800px"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},on:{change:t.imageTagChange},model:{value:e.imageSelected,callback:function(a){t.$set(e,"imageSelected",a)},expression:"item.imageSelected"}},[a("el-option",{attrs:{value:""}},[a("div",{staticStyle:{"font-weight":"bold",color:"#888","font-size":"12px"}},[a("span",{staticStyle:{display:"inline-block",width:"50%"}},[t._v("版本")]),t._v(" "),a("span",{staticStyle:{display:"inline-block",width:"10%"}},[t._v("父POM")]),t._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[t._v("创建时间")]),t._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[t._v("备注")])])]),t._v(" "),t._l(e.images,(function(e){return a("el-option",{key:e.name,attrs:{label:e.tag+"          (父pom: "+e.parentPom.replace("fxiaoke-parent-pom-","")+")",value:e.name}},[a("div",[a("span",{staticStyle:{display:"inline-block",width:"50%"}},[a("b",[t._v(t._s(e.tag))])]),t._v(" "),a("span",{staticStyle:{display:"inline-block",width:"10%"}},[t._v(t._s(e.parentPom.replace("fxiaoke-parent-pom-","")))]),t._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[t._v(t._s(e.createTime))]),t._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[t._v(t._s(e.remark?e.remark.length>20?e.remark.substring(0,20)+"...":e.remark:""))])])])})),t._v(" "),e.images?t._e():a("div",{staticStyle:{color:"#888","text-align":"center","font-size":"13px",margin:"10px"}},[t._v("\n                无可用的镜像， 去\n                "),a("el-button",{staticStyle:{"font-size":"13px"},attrs:{type:"text"},on:{click:t.imageBuildPage}},[t._v("构建镜像")])],1)],2)],1)])],1)})),t._v(" "),a("el-form-item",{attrs:{label:"每批升级数"}},[a("div",{staticStyle:{display:"inline-block",width:"800px"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.deployData.form.maxSurge,callback:function(e){t.$set(t.deployData.form,"maxSurge",e)},expression:"deployData.form.maxSurge"}},[a("el-option-group",{attrs:{label:"按百分比"}},[a("el-option",{attrs:{label:"100%",value:"100%"}}),t._v(" "),a("el-option",{attrs:{label:"50%",value:"50%"}}),t._v(" "),a("el-option",{attrs:{label:"25%",value:"25%"}})],1),t._v(" "),a("el-option-group",{attrs:{label:"按个数"}},[a("el-option",{attrs:{label:"3",value:"3"}}),t._v(" "),a("el-option",{attrs:{label:"2",value:"2"}}),t._v(" "),a("el-option",{attrs:{label:"1",value:"1"}})],1)],1)],1)]),t._v(" "),a("el-form-item",{attrs:{label:"发布描述"}},[a("div",{staticStyle:{display:"inline-block",width:"800px"}},[a("el-input",{attrs:{type:"textarea",rows:2,maxlength:256,max:256},model:{value:t.deployData.form.remark,callback:function(e){t.$set(t.deployData.form,"remark",e)},expression:"deployData.form.remark"}})],1)]),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"发布环境"}},[t._l(this.deployData.pipelines,(function(e){return a("el-tag",{staticStyle:{"font-weight":"bold","margin-right":"8px","font-size":"14px"},attrs:{effect:"plain"}},[t._v("\n          "+t._s(e.namespace)+" ("+t._s(e.cluster)+")\n        ")])})),t._v(" "),this.deployData.pipelines.length>1?a("div",{staticStyle:{color:"#888","line-height":"1.3em"}},[t._v("\n          提示： 批量发布时，会首先发布第一个环境，等发布成功后再同时并行发布所有剩余环境\n        ")]):t._e(),t._v(" "),this.deployData.pipelines.filter((function(t){return"forceecrm-public-prod"===t.namespace})).length>0?a("div",{staticStyle:{"line-height":"1.3em",color:"#f3794d"}},[t._v("\n          提示： 复制云环境的发布，只能选择复制云专用父POM所构建出来的镜像\n        ")]):t._e()],2),t._v(" "),a("el-form-item",[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"执行Eolinker接口测试"},model:{value:t.deployData.form.eolinkerTest,callback:function(e){t.$set(t.deployData.form,"eolinkerTest",e)},expression:"deployData.form.eolinkerTest"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n            是否执行Eolinker接口测试？如果发布流程没有配置Eolinker接口测试，则该选项不会产生任何作用"),a("br"),t._v("\n            大版本发布期间，建议关闭Eolinker接口测试可以提升发布效率，避免因接口测试导致发布失败。\n          ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1)],2),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticStyle:{"font-size":"13px","margin-right":"20px"},attrs:{type:"text"},on:{click:t.imageBuildPage}},[t._v("去构建镜像")]),t._v(" "),a("el-button",{on:{click:function(e){t.deployData.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.deployData.dialogLoading,expression:"deployData.dialogLoading"}],attrs:{type:"primary"},on:{click:t.deploySubmit}},[t._v("发 布")])],1)],2),t._v(" "),a("el-dialog",{attrs:{title:"发布流程复制",visible:t.dialogCloneVisible,width:"40%","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogCloneVisible=e}}},[a("el-form",{ref:"dialogCloneForm",attrs:{model:t.dialogCloneForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"应用名",prop:"app"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.dialogCloneForm.app,callback:function(e){t.$set(t.dialogCloneForm,"app","string"===typeof e?e.trim():e)},expression:"dialogCloneForm.app"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"源环境"}},[a("el-row",[a("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[a("el-form-item",{attrs:{prop:"cluster"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",disabled:""},model:{value:t.dialogCloneForm.sourceCluster,callback:function(e){t.$set(t.dialogCloneForm,"sourceCluster",e)},expression:"dialogCloneForm.sourceCluster"}},t._l(t.dialogCloneForm.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{prop:"namespace"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",disabled:""},model:{value:t.dialogCloneForm.sourceNamespace,callback:function(e){t.$set(t.dialogCloneForm,"sourceNamespace",e)},expression:"dialogCloneForm.sourceNamespace"}},t._l(t.dialogCloneForm.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1)],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"目标环境"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{prop:"namespace"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",placeholder:"选择运行环境"},model:{value:t.dialogCloneForm.targetNamespaces,callback:function(e){t.$set(t.dialogCloneForm,"targetNamespaces",e)},expression:"dialogCloneForm.targetNamespaces"}},t._l(t.dialogCloneForm.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("div",{staticStyle:{color:"#888","margin-top":"-10px","font-size":"12px"}},[t._v("备注：每个环境都会打开一个发布流程新建页面，每个都需要人工进行配置确认和提交")])],1)],1)],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogCloneVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.clonePipeline()}}},[t._v("确 定")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"实例列表",visible:t.dialogPodTableVisible,width:"60%"},on:{"update:visible":function(e){t.dialogPodTableVisible=e}}},[a("pod-simple-table",{attrs:{cluster:t.dialogPodTable.cluster,namespace:t.dialogPodTable.namespace,app:t.dialogPodTable.app}})],1),t._v(" "),a("el-dialog",{attrs:{title:"k8s deployment 信息",visible:t.dialogK8sDeploymentVisible,width:"60%"},on:{"update:visible":function(e){t.dialogK8sDeploymentVisible=e}}},[a("deployment-detail",{attrs:{cluster:t.dialogK8sDeployment.cluster,namespace:t.dialogK8sDeployment.namespace,app:t.dialogK8sDeployment.app}})],1),t._v(" "),a("el-dialog",{attrs:{title:"创建Bugfix开发分支",visible:t.bugfixBranchVisible,width:"60%"},on:{"update:visible":function(e){t.bugfixBranchVisible=e}}},[a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-alert",{attrs:{title:"提示说明",type:"info","show-icon":"",description:"创建Bugfix分支时，会以当前环境的版本为基础，创建一个新的Git分支。然后大家在此分支上进行代码修复、提交和发布，避免误把其他环境的代码引入到当前环境。",effect:"dark"}})],1),t._v(" "),a("el-form",{attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{attrs:{disabled:""},model:{value:t.bugfixBranch.app,callback:function(e){t.$set(t.bugfixBranch,"app",e)},expression:"bugfixBranch.app"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"集群"}},[a("el-input",{attrs:{disabled:""},model:{value:t.bugfixBranch.cluster,callback:function(e){t.$set(t.bugfixBranch,"cluster",e)},expression:"bugfixBranch.cluster"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-input",{attrs:{disabled:""},model:{value:t.bugfixBranch.namespace,callback:function(e){t.$set(t.bugfixBranch,"namespace",e)},expression:"bugfixBranch.namespace"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"Git地址"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择部署模块的Git地址"},model:{value:t.bugfixBranch.gitUrl,callback:function(e){t.$set(t.bugfixBranch,"gitUrl",e)},expression:"bugfixBranch.gitUrl"}},t._l(t.bugfixBranch.gitUrlOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.bugfixBranchVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createBugfixBranch()}}},[t._v("确 定")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"确定要下线发布流程吗？",visible:t.removePipeDialogVisible,width:"30%"},on:{"update:visible":function(e){t.removePipeDialogVisible=e}}},[a("p",[t._v("应用："),a("b",[t._v(t._s(this.removePipeDialogData.app))])]),t._v(" "),a("p",[t._v("环境："),a("b",[t._v(t._s(this.removePipeDialogData.namespace)+" ("+t._s(this.removePipeDialogData.cluster)+")")])]),t._v(" "),a("p",{staticStyle:{color:"orangered"}},[t._v("提示：")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px",color:"orangered"}},[a("p",[a("b",[t._v("1. 只有运行副本数为0，才能正常下线")])]),t._v(" "),a("p",[a("b",[t._v("2. 下线后，会删除掉发布流程")])]),t._v(" "),a("p",[a("b",[t._v("3. 当前操作需要系统管理员权限，业务同学请按照页面中的【服务下线说明】的进行操作。")])])]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.removePipeDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.removePipe()}}},[t._v("继续下线")])],1)]),t._v(" "),a("el-dialog",{attrs:{title:"服务下线说明",visible:t.offlineAppDialogVisible,width:"30%"},on:{"update:visible":function(e){t.offlineAppDialogVisible=e}}},[a("div",{staticStyle:{"line-height":"2em"}},[t._v("\n      如果服务已不再使用，请申请下线，避免资源浪费。下线方式： 通过CRM系统的【k8s应用下线申请】对象进行申请。"),a("br"),t._v("\n      对象地址：\n      "),a("a",{staticStyle:{color:"#409EFF","text-decoration":"underline"},attrs:{href:"https://www.fxiaoke.com/XV/UI/Home#crm/list/=/k8s_application_offline_a__c",target:"_blank"}},[t._v("\n        https://www.fxiaoke.com/XV/UI/Home#crm/list/=/k8s_application_offline_a__c\n      ")])])]),t._v(" "),a("el-dialog",{attrs:{title:"确定要修改发布流程状态吗？",visible:t.updateStatusDialogVisible,width:"30%"},on:{"update:visible":function(e){t.updateStatusDialogVisible=e}}},[a("p",[t._v("应用："),a("b",[t._v(t._s(this.updateStatusDialogData.app))])]),t._v(" "),a("p",[t._v("环境："),a("b",[t._v(t._s(this.updateStatusDialogData.namespace)+" ("+t._s(this.updateStatusDialogData.cluster)+")")])]),t._v(" "),a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.updateStatusDialogData.status,callback:function(e){t.$set(t.updateStatusDialogData,"status",e)},expression:"updateStatusDialogData.status"}},t._l(t.statusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.updateStatusDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.updateStatus()}}},[t._v("确认")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"构建+发布",visible:t.buildAndDeployDialogVisible,width:"960px",top:"2vh"},on:{"update:visible":function(e){t.buildAndDeployDialogVisible=e}}},[a("div",{staticStyle:{margin:"-40px -20px -20px"}},[a("build-and-deploy",{attrs:{"pipeline-id":t.buildAndDeployPipelineId},on:{successHandler:function(e){t.buildAndDeployDialogVisible=!1}}})],1)]),t._v(" "),a("el-drawer",{attrs:{withHeader:!1,"destroy-on-close":!0,visible:t.addressVisible,direction:"btt",size:"400px"},on:{"update:visible":function(e){t.addressVisible=e}}},[a("app-address",{attrs:{cluster:t.address.cluster,namespace:t.address.namespace,app:t.address.app,icon:"el-icon-link"}})],1)],1)},n=[],o=(a("28a5"),a("a481"),a("6762"),a("2fdb"),a("7f7f"),a("2d63")),l=a("51a9"),r=a("b144"),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pod-simple-table-wrapper"},[a("div",{staticStyle:{"margin-top":"-30px","text-align":"center"}},[a("b",[t._v("应用：")]),t._v(" "+t._s(this.app)+"\n    "),a("b",{staticStyle:{"margin-left":"20px"}},[t._v("环境：")]),t._v(" "+t._s(this.namespace)+" ("+t._s(this.cluster)+")\n  ")]),t._v(" "),a("div",{staticStyle:{"text-align":"right","padding-right":"20px"}},[a("el-button",{staticClass:"el-icon-menu",attrs:{type:"text"},on:{click:function(e){return t.podPage()}}},[t._v("实例管理页\n    ")]),t._v(" "),a("el-button",{staticClass:"el-icon-bank-card",staticStyle:{"margin-left":"20px"},attrs:{type:"text"},on:{click:function(e){return t.podsShell()}}},[t._v("进入所有容器\n    ")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.pods,border:"",fit:"","highlight-current-row":"","row-key":"name"}},[a("el-table-column",{attrs:{label:"实例名",sortable:"",prop:"name"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{style:{color:e.row.ready?"#67C23A":"#F56C6C"}},[t._v(t._s(e.row.name))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"Pod IP",width:"120",sortable:"",prop:"podIP"}}),t._v(" "),a("el-table-column",{attrs:{label:"Host IP",width:"120",sortable:"",prop:"hostIP"}}),t._v(" "),a("el-table-column",{attrs:{label:"重启次数",width:"110",align:"center",sortable:"",prop:"restartCount"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",prop:"createTime",width:"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.showStdoutLogDialog(e.row)}}},[t._v("标准输出\n        ")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.webShellPage(e.row.name)}}},[t._v("进入容器\n        ")])]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:"容器启动日志（标准输出)",visible:t.podStdoutVisible,top:"5vh","close-on-click-modal":!1,width:"70%","append-to-body":"",center:""},on:{"update:visible":function(e){t.podStdoutVisible=e}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("pod-stdout",{attrs:{cluster:this.podStdout.cluster,namespace:this.podStdout.namespace,pod:this.podStdout.pod,containers:this.podStdout.containers}})],1)])],1)},c=[],p=a("75fc"),u=a("a527"),d=a("cf89"),m={name:"PodSimpleTable",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0}},components:{podStdout:d["a"]},data:function(){return{tableLoading:!1,pods:[],podStdoutVisible:!1,podStdout:{cluster:"",namespace:"",pod:"",containers:[]}}},watch:{cluster:function(t){this.loadPods()},namespace:function(t){this.loadPods()},app:function(t){this.loadPods()}},computed:{},mounted:function(){this.loadPods()},beforeDestroy:function(){},methods:{loadPods:function(){var t=this;this.tableLoading=!0,console.log("load pipeline pods"),Object(u["h"])(this.cluster,this.namespace,this.app).then((function(e){t.pods=e.data})).catch((function(e){t.$message.error("加载实例信息失败: "+e.message)})).finally((function(){t.tableLoading=!1}))},podsShell:function(){this.pods&&this.pods.length>0&&this.webShellPage(this.pods.map((function(t){return t.name})).join(","))},podPage:function(){var t=this.$router.resolve({name:"pod-index",query:{cluster:this.cluster,namespace:this.namespace,app:this.app}});window.open(t.href,"_blank")},showStdoutLogDialog:function(t){this.podStdoutVisible=!0,this.podStdout.cluster=t.cluster,this.podStdout.namespace=t.namespace,this.podStdout.pod=t.name,this.podStdout.containers=[t.container0Name].concat(Object(p["a"])(t.initContainersName))},webShellPage:function(t){var e="/api/page/redirect?type=webShell&cluster=".concat(this.cluster,"&namespace=").concat(this.namespace,"&app=").concat(this.app,"&pods=").concat(t);window.open(e)}}},f=m,v=(a("343d"),a("2877")),b=Object(v["a"])(f,s,c,!1,null,null,null),g=b.exports,h=a("8504"),_=a("837b"),y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"divider-blockquote qa",staticStyle:{"padding-top":"20px"}},[a("span",[t._v("FAQ (常见问题解答）")]),t._v(" "),a("el-divider"),t._v(" "),t._m(0)],1)])},x=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"font-size":"12px",color:"#777","max-width":"1200px"}},[a("p",[a("b",[t._v("Q: 如何创建发布流程")])]),t._v(" "),a("p",[t._v("A: 研发同学在页面新建发布流程，创建好以后发布流程状态为[待审核] → 联系 @魏贺 进行审批 → 使用发布流程进行发布")]),t._v(" "),a("p",[a("b",[t._v("Q: 发布流程下线包含哪些操作？")])]),t._v(" "),a("p",[t._v("A: 首先会把环境下的实例数缩容为0，然后再把发布流程删除掉。对于其他Meta信息，管理员后期会统一做清理")]),t._v(" "),a("p",[a("b",[t._v("Q: 应用实例需要访问外网怎么办？")])]),t._v(" "),a("p",[t._v("A: 出于安全考虑，默认情况下应用是无法访问外网的。如果需要访问外网，请按照\n        "),a("a",{staticStyle:{color:"#01AAED"},attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=353404601"}},[t._v("k8s应用外网访问-申请")]),t._v(" 文档进行处理\n      ")]),t._v(" "),a("p",[a("b",[t._v("Q: 如何获取k8s环境下应用访问外网的出口IP？")])]),t._v(" "),a("p",[t._v("A: 请咨询 @丰莹莹")]),t._v(" "),a("p",[a("b",[t._v("Q: 为什么会有 [运行实例数] > [配置实例数] 的情况？")])]),t._v(" "),a("p",[t._v("A: 服务进行了自动扩容或手动扩容")]),t._v(" "),a("p",[a("b",[t._v("Q: 日志中心搜不到服务任何日志？")])]),t._v(" "),a("p",[t._v("A: 如果应用日志需要被收集起来，请在发布流程里勾上 [接入ClickHouse日志] 选项，并且在工程里引入和配置我们的oss-metrics组件"),a("br"),t._v("\n        oss-metrics组件使用说明：http://git.firstshare.cn/JavaCommon/oss-metrics")])])}],w={props:{},mounted:function(){},computed:{},data:function(){return{}},methods:{}},S=w,k=(a("bfce"),Object(v["a"])(S,y,x,!1,null,"186d9122",null)),C=k.exports,D=a("03b6"),O=a("76fe"),j=a("71df"),P=a("bcbd"),A=a("84d4"),T=a("4ad4"),$=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"deployment-detail"},[a("vue-json-pretty",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{},attrs:{data:t.data}})],1)},F=[],I=a("d538"),V=a.n(I),L={components:{VueJsonPretty:V.a},props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0}},created:function(){this.loadData()},watch:{cluster:function(t){this.loadData()},namespace:function(t){this.loadData()},app:function(t){this.loadData()}},computed:{},data:function(){return{loading:!0,data:{}}},methods:{loadData:function(){var t=this;this.loading=!0,Object(h["c"])(this.cluster,this.namespace,this.app).then((function(e){t.data=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))}}},R=L,M=Object(v["a"])(R,$,F,!1,null,null,null),z=M.exports,q=a("b562"),E=a("bb0b"),B=a("1e42"),N=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.gitModuleLoading||t.pipelineLoading||t.gitTagLoading||t.submitLoading,expression:"gitModuleLoading || pipelineLoading || gitTagLoading || submitLoading"}],staticClass:"app-container build-and-deploy-container"},[a("div",{staticStyle:{"font-size":"15px","font-weight":"bold",margin:"-32px 110px 10px"}},[t._v(" | "+t._s(this.pipeline.app))]),t._v(" "),a("el-card",{staticClass:"box-card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-size":"16px"}},[t._v("构建配置")])]),t._v(" "),a("div",[a("el-form",{ref:"buildFormRef",attrs:{size:"small",model:t.buildForm,"label-width":"120px",rules:t.buildFormRules}},[t._l(t.buildForm.modules,(function(e,i){return a("div",{key:i},[a("el-form-item",{staticClass:"module-git-url",staticStyle:{padding:"0",margin:"0 0 -5px 0"},attrs:{label:""}},[a("span",{staticStyle:{"font-size":"12px",color:"#b4532a","font-weight":"bold"}},[t._v("\n            部署模块： "+t._s(e.gitUrl)+" --- "+t._s(e.module)+"\n          ")])]),t._v(" "),a("el-form-item",{attrs:{label:"版本号"}},[a("el-row",[a("el-col",{attrs:{span:14}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:t.buildForm.modules[i].tag,callback:function(e){t.$set(t.buildForm.modules[i],"tag",e)},expression:"buildForm.modules[index].tag"}},[t.buildForm.modules[i].branchOptions.length>0?a("el-option-group",{attrs:{label:"Git分支"}},t._l(t.buildForm.modules[i].branchOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}},[a("span",[a("b",[t._v(t._s(e.name))])])])})),1):t._e(),t._v(" "),a("el-option-group",{attrs:{label:"GitTag (message)"}},t._l(t.buildForm.modules[i].tagOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}},[a("span",[a("b",[t._v(t._s(e.name))]),a("span",{staticStyle:{"font-size":"12px",color:"#888","padding-left":"5px"}},[t._v(t._s(e.message?" ("+e.message.substring(0,30)+")":""))])])])})),1)],1)],1),t._v(" "),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:9,offset:1}},[a("b",{staticStyle:{color:"#606266"}},[t._v("编译环境")]),t._v(" "),a("el-select",{staticStyle:{width:"230px"},attrs:{filterable:""},model:{value:t.buildForm.modules[i].mavenImage,callback:function(e){t.$set(t.buildForm.modules[i],"mavenImage",e)},expression:"buildForm.modules[index].mavenImage"}},t._l(t.mavenOptions,(function(t){return a("el-option",{key:t,attrs:{label:t.split("/").pop(),value:t}})})),1)],1)],1)],1)],1)})),t._v(" "),a("el-form-item",{attrs:{prop:"parentPom"}},[a("template",{slot:"label"},[t._v("\n            父POM\n            "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"right"}},[a("template",{slot:"content"},[a("p",[t._v("不同父POM中定义的依赖包版本区别如下：")]),t._v(" "),a("p",[a("b",[t._v("测试版（Alpha)：")]),t._v(" 包含未经测试的高版本依赖包，只在线下环境可用")]),t._v(" "),a("p",[a("b",[t._v("候选版（RC): ")]),t._v(" 通过了线下环境测试的依赖包版本，或者一些紧急Bug修复的依赖包版本")]),t._v(" "),a("p",[a("b",[t._v("稳定版 (RELEASE)：")]),t._v(" 在候选版通过充分测试后，需要进入到全网的依赖包版本")]),t._v(" "),a("el-divider",{staticClass:"env-divider"}),t._v(" "),a("p",[t._v("依赖包版本的升级常规流程如下：首先进入【测试版】在线下环境进行测试，通过后进入"),a("br"),t._v("【候选版】在线上环境进行试用,通过后进入【稳定版】推广到全网。")])],1),t._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),t._v(" "),a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.buildForm.parentPom,callback:function(e){t.$set(t.buildForm,"parentPom",e)},expression:"buildForm.parentPom"}},[t._l(this.parentPoms,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}},[a("span",[t._v(t._s(e.name)+" ")]),t._v(" "),a("span",{staticStyle:{"padding-left":"20px",color:"#8492a6","font-size":"13px"}},[a("label",{staticStyle:{color:"orangered","font-size":"12px"}},[t._v(t._s(e.desc))])])])})),t._v(" "),a("div",{staticStyle:{"margin-top":"10px","font-size":"12px",color:"#888","margin-left":"20px"}},[t._v("\n                如果要查看父POM所管理的Jar包及其版本信息，请点击 "),a("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md",target:"_blank"}},[t._v("链接")])])],2)],1)],2),t._v(" "),a("el-form-item",{attrs:{label:"备注信息"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-input",{attrs:{type:"textarea",rows:1,maxlength:32,max:32},model:{value:t.buildForm.remark,callback:function(e){t.$set(t.buildForm,"remark",e)},expression:"buildForm.remark"}})],1)]),t._v(" "),a("el-form-item",{attrs:{label:"构建选项"}},[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"如果镜像存在，则覆盖"},model:{value:t.buildForm.forceCodeCompile,callback:function(e){t.$set(t.buildForm,"forceCodeCompile",e)},expression:"buildForm.forceCodeCompile"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n              默认情况下，当同名镜像已经存在的时候，不允许直接构建镜像，需要选择覆盖。勾选此选项后，会强制重新编译代码并覆盖原有镜像。\n            ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2),t._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"执行单元测试"},model:{value:t.buildForm.unitTest,callback:function(e){t.$set(t.buildForm,"unitTest",e)},expression:"buildForm.unitTest"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n              代码在进行Maven编译时，是否执行单元测试\n            ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2),t._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"依赖包版本校验",disabled:""},model:{value:t.buildForm.dependencyCheck,callback:function(e){t.$set(t.buildForm,"dependencyCheck",e)},expression:"buildForm.dependencyCheck"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n              对工程的依赖Jar包进行两方面检测。1：同一jar包是否存在多个版本；2：某个jar包版本是否低于平台所要求的最低版本\n            ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),t._v(" "),a("el-form-item",{attrs:{label:"提示"}},[a("el-alert",{attrs:{closable:!1,type:"info"}},[a("template",{slot:"title"},[a("div",[a("b",[t._v("镜像版本号生成机制:")])]),t._v(" "),a("div",[t._v("\n                1. 如果版本号为tag，则直接使用该名称"),a("br"),t._v("\n                2. 如果版本号为分支，则添加时间后缀。比如 master 会被替换为 master--202406041130"),a("br"),t._v("\n                3. 如果版本号中包含了斜线 /，则将其替换为 ---。比如 version/910 会被替换为 version---910"),a("br")]),t._v(" "),t.buildForm.modules&&t.buildForm.modules.length>1?a("div",{staticStyle:{"margin-top":"10px",color:"orangered"}},[a("b",[t._v("提示:")]),t._v(" 受系统设计限制，在【构建+部署】场景下，若存在多个部署模块，模块镜像将按串行方式构建。\n              ")]):t._e()])],2)],1)],2)],1)]),t._v(" "),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-size":"16px"}},[t._v("发布配置")])]),t._v(" "),a("div",[a("el-form",{ref:"deployFormRef",attrs:{size:"small",model:t.deployForm,"label-width":"100px",rules:t.deployFormRules}},[a("el-form-item",{attrs:{label:"每批升级数",prop:"maxSurge"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.deployForm.maxSurge,callback:function(e){t.$set(t.deployForm,"maxSurge",e)},expression:"deployForm.maxSurge"}},[a("el-option-group",{attrs:{label:"按百分比"}},[a("el-option",{attrs:{label:"100%",value:"100%"}}),t._v(" "),a("el-option",{attrs:{label:"50%",value:"50%"}}),t._v(" "),a("el-option",{attrs:{label:"25%",value:"25%"}})],1),t._v(" "),a("el-option-group",{attrs:{label:"按个数"}},[a("el-option",{attrs:{label:"3",value:"3"}}),t._v(" "),a("el-option",{attrs:{label:"2",value:"2"}}),t._v(" "),a("el-option",{attrs:{label:"1",value:"1"}})],1)],1)],1)]),t._v(" "),a("el-form-item",{attrs:{label:"发布描述",prop:"remark"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-input",{attrs:{type:"textarea",rows:1,maxlength:256,max:256},model:{value:t.deployForm.remark,callback:function(e){t.$set(t.deployForm,"remark",e)},expression:"deployForm.remark"}})],1)]),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"发布环境"}},[a("div",{staticStyle:{display:"inline-block"}},[a("el-tag",{staticStyle:{"font-weight":"bold","margin-right":"8px","font-size":"14px"},attrs:{effect:"plain"}},[t._v("\n              "+t._s(this.pipeline.namespace)+" ("+t._s(this.pipeline.cluster)+")\n            ")])],1),t._v(" "),a("div",{staticStyle:{display:"inline-block","margin-left":"60px"}},[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"执行Eolinker接口测试"},model:{value:t.deployForm.eolinkerTest,callback:function(e){t.$set(t.deployForm,"eolinkerTest",e)},expression:"deployForm.eolinkerTest"}}),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[t._v("\n                是否执行Eolinker接口测试？如果发布流程没有配置Eolinker接口测试，则该选项不会产生任何作用"),a("br"),t._v("\n                大版本发布期间，建议关闭Eolinker接口测试可以提升发布效率，避免因接口测试导致发布失败。\n              ")]),t._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),t._v(" "),"forceecrm-public-prod"===this.pipeline.namespace?a("div",{staticStyle:{"line-height":"1.3em",color:"#f3794d"}},[t._v("\n            提示： 复制云环境的发布，只能选择复制云专用父POM所构建出来的镜像\n          ")]):t._e()])],1)],1)]),t._v(" "),a("div",{staticStyle:{"margin-top":"20px","text-align":"right"}},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.buildSubmit}},[t._v("构建 & 发布")])],1)],1)},U=[],H=a("768b"),K=(a("ffc1"),a("1c4c"),a("ac6a"),a("5df3"),a("4f7f"),a("c5f6"),a("6a4c")),G={name:"build-and-deploy",props:{pipelineId:{type:Number,required:!0}},watch:{pipelineId:function(t){this.loadGitModule(),this.loadPipeline()}},data:function(){return{pipelineLoading:!1,gitModuleLoading:!1,gitTagLoading:!1,submitLoading:!1,buildOptions:{versionOptions:{}},pipeline:{},buildForm:{app:"",modules:[],unitTest:!1,forceCodeCompile:!1,dependencyCheck:!0,parentPom:"",remark:""},buildFormRules:{parentPom:[{required:!0,message:"请选择父POM"}]},deployForm:{pipelineIds:[],maxSurge:"50%",remark:"",eolinkerTest:!1},deployFormRules:{maxSurge:[{required:!0,message:"请选择每批升级数"}]}}},components:{},computed:{mavenOptions:function(){return this.$settings.mavenImages||[]},parentPoms:function(){return this.$settings.parentPoms||[]}},mounted:function(){this.loadGitModule(),this.loadPipeline()},methods:{loadGitModule:function(){var t=this;this.gitModuleLoading=!0,Object(q["n"])("",this.pipelineId).then((function(e){var a,i=Object(o["a"])(e.data);try{for(i.s();!(a=i.n()).done;){var n=a.value;n.tag="",n.branchOptions=[],n.tagOptions=[]}}catch(l){i.e(l)}finally{i.f()}t.buildForm.modules=e.data,t.loadGitTags()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.gitModuleLoading=!1}))},loadPipeline:function(){var t=this;this.pipelineLoading=!0,Object(l["e"])(this.pipelineId).then((function(e){t.pipeline=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pipelineLoading=!1}))},loadGitTags:function(){var t=this;this.gitTagLoading=!0;var e,a=new Set,i=Object(o["a"])(this.buildForm.modules);try{for(i.s();!(e=i.n()).done;){var n=e.value;a.add(n.gitUrl)}}catch(l){i.e(l)}finally{i.f()}Object(K["c"])({gitUrls:Array.from(a)}).then((function(e){for(var a=0,i=Object.entries(e.data);a<i.length;a++){var n,r=Object(H["a"])(i[a],2),s=r[0],c=r[1],p=Object(o["a"])(t.buildForm.modules);try{for(p.s();!(n=p.n()).done;){var u=n.value;u.gitUrl===s&&(u.branchOptions=c.branchOptions,u.tagOptions=c.tagOptions)}}catch(l){p.e(l)}finally{p.f()}}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.gitTagLoading=!1}))},buildSubmit:function(){var t=this;this.$refs["buildFormRef"].validate((function(e){e&&t.$refs["deployFormRef"].validate((function(e){if(e){var a,i=Object(o["a"])(t.buildForm.modules);try{for(i.s();!(a=i.n()).done;){var n=a.value;if(""===n.tag)return void t.$message.error("请选择每个模块的版本号")}}catch(d){i.e(d)}finally{i.f()}t.submitLoading=!0;var l,s={buildImageParam:[],deployParam:[{pipelineId:t.pipeline.id,maxSurge:t.deployForm.maxSurge,remark:t.deployForm.remark,eolinkerTest:t.deployForm.eolinkerTest}]},c=Object(r["a"])(t.buildForm),p=Object(o["a"])(c.modules);try{for(p.s();!(l=p.n()).done;){var u=l.value;s.buildImageParam.push({app:t.pipeline.app,gitUrl:u.gitUrl,gitModule:u.module,gitTag:u.tag,mavenImage:u.mavenImage,unitTest:c.unitTest,forceCodeCompile:c.forceCodeCompile,dependencyCheck:c.dependencyCheck,parentPom:c.parentPom,remark:c.remark})}}catch(d){p.e(d)}finally{p.f()}Object(O["b"])(s).then((function(e){t.$message.success("操作成功"),t.$emit("successHandler"),1===e.data.length?t.jobDetailPage(e.data[0].jobId):t.jobHistoryPage("","")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.submitLoading=!1}))}}))}))},jobDetailPage:function(t){var e=this;Object(O["g"])(t).then((function(a){var i=a.data,n="CD"===i.type?"cicd-app-deploy-detail":"cicd-image-build-detail",o=e.$router.resolve({name:n,query:{jobId:t}});window.open(o.href,"_blank")})).catch((function(t){e.$message.error(t.message)}))},jobHistoryPage:function(t,e){var a=this.$router.resolve({name:"cicd-image-build-history",query:{gitUrl:t,gitModule:e,author:""}});window.open(a.href,"_blank")}}},J=G,Q=(a("641a"),Object(v["a"])(J,N,U,!1,null,null,null)),W=Q.exports,X=a("57c3"),Y={name:"app-deploy",data:function(){return{loading:!1,statusOptions:[{label:"所有",value:"_all_"},{label:"可用",value:"enabled"},{label:"禁用",value:"disabled"},{label:"已迁移",value:"migrated"}],statusFilter:"_all_",cloudCategories:[],cloudCategoryCurr:"_all_",currApp:"",tableDataAll:[],tableData:[],tableLoading:!1,deployData:{dialogVisible:!1,dialogLoading:!1,form:{pipelineIds:[],maxSurge:"50%",remark:"",eolinkerTest:!1,deployModuleImages:[{gitUrl:"",gitModule:"",image:""}]},pipelines:[],imageOptions:[{gitUrl:"",gitModule:"",imageSelected:"",images:[{name:"",tag:"",parentPom:"",remark:""}]}]},removePipeDialogVisible:!1,removePipeDialogData:{},offlineAppDialogVisible:!1,addressVisible:!1,address:{cluster:"",namespace:"",app:""},dialogCloneVisible:!1,dialogCloneForm:{clusterOptions:[],namespaceOptions:[],app:"",sourcePipelineId:0,sourceCluster:"",sourceNamespace:"",targetNamespaces:[]},dialogPodTableVisible:!1,dialogPodTable:{cluster:"",namespace:"",app:""},dialogK8sDeploymentVisible:!1,dialogK8sDeployment:{cluster:"",namespace:"",app:""},bugfixBranchVisible:!1,bugfixBranch:{gitUrl:"",namespace:"",cluster:"",app:"",gitUrlOptions:[]},updateStatusDialogVisible:!1,updateStatusDialogData:{},buildAndDeployDialogVisible:!1,buildAndDeployPipelineId:0}},components:{MaintainAlert:X["a"],BuildAndDeploy:W,ExportButton:B["a"],DeploymentDetail:z,AppAddress:T["a"],MenuTabs:A["a"],JobRunnerAlert:P["a"],AppSelector2:j["a"],PipelineApp:D["a"],PipelineDoc:C,PodSimpleTable:g,PipelineExpand:_["a"]},computed:{isProdEnv:function(){return window.location.host.indexOf("foneshare")>-1},cicdOneKey:function(){return!this.isProdEnv}},mounted:function(){},methods:{changeCurrApp:function(t){this.currApp=t,this.updateQueryParam(),this.loadTableData()},pipelineFilter:function(){var t=this.tableDataAll,e=[];if("_all_"===this.cloudCategoryCurr)e=t;else{var a,i=Object(o["a"])(t);try{for(i.s();!(a=i.n()).done;){var n=a.value;n.extraAttr.cloudCategory===this.cloudCategoryCurr&&e.push(n)}}catch(c){i.e(c)}finally{i.f()}}if(t=e,e=[],"_all_"===this.statusFilter)e=t;else{var l,r=Object(o["a"])(this.tableDataAll);try{for(r.s();!(l=r.n()).done;){var s=l.value;s.status===this.statusFilter&&e.push(s)}}catch(c){r.e(c)}finally{r.f()}}this.tableData=e},updateQueryParam:function(){var t=Object(r["a"])(this.$route.query);t["app"]=this.currApp,this.$router.push({query:t})},cloudCategoryStatistics:function(t){var e,a=[],i=Object(o["a"])(t);try{var n=function(){var t=e.value,i=a.filter((function(e){return e.name===t.extraAttr.cloudCategory}));if(i.length<1)return a.push({name:t.extraAttr.cloudCategory,desc:t.extraAttr.cloudCategoryDesc,pipelineCount:1}),"continue";i[0].pipelineCount+=1};for(i.s();!(e=i.n()).done;)n()}catch(l){i.e(l)}finally{i.f()}this.cloudCategories=a},loadTableData:function(){var t=this;this.tableLoading=!0,Object(l["c"])(this.currApp).then((function(e){e.data.map((function(t){t.pods=[],t.extraAttr.deployTag=null,t.extraAttr.deployModules=[],t.extraAttr.runningPodNum=null,t.extraAttr.autoScaleV2MinReplicas=null,t.extraAttr.autoScaleV2MaxReplicas=null,t.extraAttr.cronScaleMinReplicas=null,t.extraAttr.cronScaleMaxReplicas=null,t.extraAttr.deregisterPodSize=0})),t.cloudCategoryStatistics(e.data),t.tableDataAll=e.data,t.tableData=t.tableDataAll;var a,i=Object(o["a"])(t.tableData);try{for(i.s();!(a=i.n()).done;){var n=a.value;t.findDeployment(n.cluster,n.namespace,n.app),t.findScaleConfig(n.cluster,n.namespace,n.app),t.findDeregisterPods(n.cluster,n.namespace,n.app)}}catch(l){i.e(l)}finally{i.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},urgentDeploy:function(){this.$alert('如果需要紧急发布，请申请临时操作权限。 点击 <a href="/#/auth/temp-path" target="_blank" style="color: #3a8ee6">链接</a> 打开临时授权申请页面',"紧急发布提示",{dangerouslyUseHTMLString:!0})},createAppHealthReviewInCRM:function(){var t=this;this.$confirm("此操作将在【后端服务健康度 Review】对象里给服务 ".concat(this.currApp," 创建一条初始化数据，是否继续?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(q["g"])(t.currApp).then((function(t){var e=t.data;window.open(e,"_blank")})).catch((function(e){t.loading=!1,t.$message.error(e.message)})).finally((function(){t.loading=!1}))})).catch((function(){}))},findDeployment:function(t,e,a){var i,n=null,l=Object(o["a"])(this.tableData);try{for(l.s();!(i=l.n()).done;){var r=i.value;if(r.cluster===t&&r.namespace===e&&r.app===a){n=r;break}}}catch(s){l.e(s)}finally{l.f()}null!==n&&Object(h["a"])(t,e,a).then((function(t){n.extraAttr.deployTag=t.data.deployTag,n.extraAttr.deployModules=t.data.deployModules,n.extraAttr.runningPodNum=t.data.replicas,n.extraAttr.lastDeployTime=t.data.deployTime,n.extraAttr.lastDeployUser=t.data.deployUser})).catch((function(t){n.extraAttr.deployTag="--",n.extraAttr.deployModules=[],n.extraAttr.runningPodNum=0,n.extraAttr.lastDeployTime=null,n.extraAttr.lastDeployUser=null,console.error(t.message)}))},findDeregisterPods:function(t,e,a){var i,n=null,l=Object(o["a"])(this.tableData);try{for(l.s();!(i=l.n()).done;){var r=i.value;if(r.cluster===t&&r.namespace===e&&r.app===a){n=r;break}}}catch(s){l.e(s)}finally{l.f()}null!==n&&Object(u["d"])(t,e,a,!1).then((function(t){n.extraAttr.deregisterPodSize=t.data.length})).catch((function(t){}))},findScaleConfig:function(t,e,a){var i,n=this,l=null,r=Object(o["a"])(this.tableData);try{for(r.s();!(i=r.n()).done;){var s=i.value;if(s.cluster===t&&s.namespace===e&&s.app===a){l=s;break}}}catch(c){r.e(c)}finally{r.f()}null!==l&&Object(E["h"])(t,e,a).then((function(t){t.data["autoScaleV2"]?(l.extraAttr.autoScaleV2MinReplicas=t.data["autoScaleV2"]["spec"]["minReplicaCount"],l.extraAttr.autoScaleV2MaxReplicas=t.data["autoScaleV2"]["spec"]["maxReplicaCount"]):(l.extraAttr.autoScaleV2MinReplicas="-",l.extraAttr.autoScaleV2MaxReplicas="-"),t.data["cronScale"]?(l.extraAttr.cronScaleMinReplicas=l.replicas,l.extraAttr.cronScaleMaxReplicas=t.data["cronScale"]["Replicas"]):(l.extraAttr.cronScaleMinReplicas="-",l.extraAttr.cronScaleMaxReplicas="-")})).catch((function(t){n.$message.error(t.message)}))},removePipe:function(){var t=this;this.$prompt("请输入应用名","下线确认提示",{confirmButtonText:"继续下线",confirmButtonClass:"el-button--danger",cancelButtonText:"取消"}).then((function(e){var a=e.value;a===t.removePipeDialogData.app?Object(l["k"])(t.removePipeDialogData.id).then((function(e){t.$message.success("删除成功"),t.loadTableData(t.$route.query.app)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.removePipeDialogVisible=!1})):t.$message.info("应用名输入错误")})).catch((function(){console.log("取消下线确认操作")}))},removePipeDialog:function(t){this.removePipeDialogData=Object(r["a"])(t),this.removePipeDialogVisible=!0},offlineAppDialog:function(){this.offlineAppDialogVisible=!0},updateStatusDialog:function(t){this.updateStatusDialogData=Object(r["a"])(t),this.updateStatusDialogVisible=!0},updateStatus:function(){var t=this,e={};e.id=this.updateStatusDialogData.id,e.status=this.updateStatusDialogData.status,console.log(e),Object(l["o"])(e).then((function(e){t.$message.success("修改成功"),t.loadTableData(t.$route.query.app)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.updateStatusDialogVisible=!1}))},createPage:function(){this.$router.push({name:"app-pipeline-edit",query:{app:this.currApp}})},tempAuthPage:function(){var t=this.$router.resolve({name:"auth-temp-auth",query:{showAddDialog:"true",app:this.currApp}}).href;window.open(t,"_blank")},batchDeploy:function(){var t=this.$refs.pipelineTable.store.states.selection;!t||t.length<1?this.$message.warning("请选择需要发布的流程"):this.showDeployDialog(t)},imageBuildPage:function(){var t=this.$router.resolve({name:"cicd-image-build",query:{app:this.currApp,openDialog:"true"}}).href;window.open(t,"_blank")},multiClusterPodExecPage:function(){var t=this.$refs.pipelineTable.store.states.selection;!t||t.length<1?this.$message.warning("请选择需要发布的流程"):this.$confirm("此操作将同时进入 ".concat(t.length," 个发布流程的第一个实例, 是否继续?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e,a=[],i=Object(o["a"])(t);try{for(i.s();!(e=i.n()).done;){var n=e.value;a.push({cluster:n.cluster,namespace:n.namespace,app:n.app})}}catch(s){i.e(s)}finally{i.f()}var l=JSON.stringify(a),r="/api/page/redirect?type=webShellWithFirstPod&webShellParams=".concat(l,"&_t")+Date.now();window.open(r)})).catch((function(){}))},editPage:function(t){var e={pipelineId:t.id};this.$router.push({name:"app-pipeline-edit",query:e})},syncConfigToOtherPage:function(t){var e={pipelineId:t.id,app:t.app};this.$router.push({name:"app-pipeline-sync-config",query:e})},showPodTable:function(t){this.dialogPodTable.cluster=t.cluster,this.dialogPodTable.namespace=t.namespace,this.dialogPodTable.app=t.app,this.dialogPodTableVisible=!0},scalePage:function(t,e){var a=null;if("autoscalev2"===e)a=this.$router.resolve({name:"pod-auto-scaler",query:{cluster:t.cluster,namespace:t.namespace,app:t.app}});else{if("cronscale"!==e)return void this.$message.error("未知的扩缩容类型："+e);a=this.$router.resolve({name:"app-scale-cron",query:{cluster:t.cluster,namespace:t.namespace,app:t.app}})}window.open(a.href,"_blank")},showK8sDeployment:function(t){this.dialogK8sDeployment.cluster=t.cluster,this.dialogK8sDeployment.namespace=t.namespace,this.dialogK8sDeployment.app=t.app,this.dialogK8sDeploymentVisible=!0},showCreateBugfixBranch:function(t){this.bugfixBranch.cluster=t.cluster,this.bugfixBranch.namespace=t.namespace,this.bugfixBranch.app=t.app,this.bugfixBranchVisible=!0,this.bugfixBranch.gitUrlOptions=[];var e,a=Object(o["a"])(t.appModules);try{for(a.s();!(e=a.n()).done;){var i=e.value;this.bugfixBranch.gitUrlOptions.includes(i.gitUrl)||this.bugfixBranch.gitUrlOptions.push(i.gitUrl)}}catch(n){a.e(n)}finally{a.f()}},createBugfixBranch:function(){var t=this;Object(q["e"])(this.bugfixBranch).then((function(e){t.$message.success("创建成功，分支名："+e.data+"。3秒后跳转到gitlab页面,请确保浏览器允许弹出新窗口"),setTimeout((function(){window.open(t.bugfixBranch.gitUrl.replace(".git","")+"/-/branches","_blank")}),3e3)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.bugfixBranchVisible=!1}))},showDeployDialog:function(t){var e,a=this,i=t[0],n=Object(o["a"])(t);try{for(n.s();!(e=n.n()).done;){var l=e.value;if("enabled"!==l.status)return void this.$message.warning("有发布流程处于不可用状态");if(l.appModules.length!==i.appModules.length)return void this.$message.warning("发布流程之间的部署模块不一样，不能一起批量发布")}}catch(s){n.e(s)}finally{n.f()}this.loading=!0;var r=t.map((function(t){return t.id}));Object(O["i"])(r.join(",")).then((function(e){a.loading=!1,a.deployData.pipelines=t,a.deployData.imageOptions=e.data;var i="50%";a.$settings.maxSurgeForceFull&&(i="100%");var n=!1;a.$settings.eolinkerTestDefault&&(n=!0),a.deployData.form={pipelineIds:r,maxSurge:i,remark:"",eolinkerTest:n,deployModuleImages:[]},a.deployData.dialogVisible=!0})).catch((function(t){a.loading=!1,a.$message.error(t.message)}))},imageTagChange:function(t){var e,a=Object(o["a"])(this.deployData.imageOptions);try{for(a.s();!(e=a.n()).done;){var i,n=e.value,l=Object(o["a"])(n.images);try{for(l.s();!(i=l.n()).done;){var r=i.value;if(r.name===t&&r.remark){this.deployData.form.remark=r.remark;break}}}catch(s){l.e(s)}finally{l.f()}}}catch(s){a.e(s)}finally{a.f()}},manageDropdownCommand:function(t){console.log("manage dropdown command: "+t);var e=t.split("##");if(2===e.length){var a=e[0],i=parseInt(e[1]),n=this.tableData.filter((function(t){return t.id===i}))[0];"edit"===a?this.editPage(n):"showAddr"===a?this.showAddress(n):"remove"===a?this.removePipeDialog(n):"clone"===a?this.showCloneDialog(n):"deployHistory"===a?this.jobHistoryPage(n.app,n.namespace):"address"===a?this.showAddress(n):"updateStatus"===a?this.updateStatusDialog(n):"syncConfigToOther"===a?this.syncConfigToOtherPage(n):"appLog"===a?this.clickhouseLogPage("app_log",n.cluster,n.namespace,n.app):"k8sDeployment"===a?this.showK8sDeployment(n):"bugfixBranch"===a?this.showCreateBugfixBranch(n):this.$message.error("未知操作："+t)}},deploySubmit:function(){var t=this;this.$refs["dialogDeployForm"].validate((function(e){if(!e)return!1;var a,i=Object(o["a"])(t.deployData.imageOptions);try{for(i.s();!(a=i.n()).done;){var n=a.value;if(!n.imageSelected)return t.$message.error("请选择镜像版本"),!1}}catch(r){i.e(r)}finally{i.f()}var l=t.$settings.envConfirmText;l?t.$prompt('请把红色内容输入到文本框: <b style="color:orangered">'.concat(l,"</b> "),"环境确认",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0}).then((function(e){var a=e.value;a===l?t.processDeploy():t.$message.warning("环境确认失败，请重新输入")})):t.processDeploy()}))},processDeploy:function(){var t=this;this.deployData.dialogLoading=!0;var e,a=[],i=Object(o["a"])(this.deployData.imageOptions);try{for(i.s();!(e=i.n()).done;){var n=e.value;a.push({gitUrl:n.gitUrl,gitModule:n.gitModule,image:n.imageSelected})}}catch(p){i.e(p)}finally{i.f()}var l,r={items:[]},s=Object(o["a"])(this.deployData.form.pipelineIds);try{for(s.s();!(l=s.n()).done;){var c=l.value;r.items.push({pipelineId:c,maxSurge:this.deployData.form.maxSurge,remark:this.deployData.form.remark,eolinkerTest:this.deployData.form.eolinkerTest,deployModuleImages:a})}}catch(p){s.e(p)}finally{s.f()}Object(O["e"])(r).then((function(e){1===e.data.length?t.jobPage(e.data[0].jobId):t.jobHistoryPage(t.currApp,""),t.deployData.dialogVisible=!1})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.$refs["jobRunnerAlert"].loadJobs(),t.deployData.dialogLoading=!1}))},jobPage:function(t){var e=this.$router.resolve({name:"cicd-app-deploy-detail",query:{jobId:t}});window.open(e.href,"_blank")},jobHistoryPage:function(t,e){var a=this.$router.resolve({name:"cicd-app-deploy-history",query:{app:t,namespace:e}});window.open(a.href,"_blank")},showCloneDialog:function(t){var e=this;this.dialogCloneForm.app=t.app,this.dialogCloneForm.sourcePipelineId=t.id,this.dialogCloneForm.sourceCluster=t.cluster,this.dialogCloneForm.sourceNamespace=t.namespace;var a,i=[],n=Object(o["a"])(this.$settings.clusters);try{var l=function(){var t,n=a.value,l=Object(o["a"])(n.namespaces);try{var r=function(){var a=t.value;if(e.tableData.filter((function(t){return t.cluster===n.name&&t.namespace===a})).length>0)return console.log("pipeline has exist, remove namespace option: ".concat(n.name,"/").concat(a)),"continue";i.push("".concat(n.name,"/").concat(a))};for(l.s();!(t=l.n()).done;)r()}catch(s){l.e(s)}finally{l.f()}};for(n.s();!(a=n.n()).done;)l()}catch(r){n.e(r)}finally{n.f()}this.dialogCloneForm.namespaceOptions=i,this.dialogCloneVisible=!0},buildAndDeployDialog:function(t){this.buildAndDeployPipelineId=t.id,this.buildAndDeployDialogVisible=!0},clonePipeline:function(){var t=this,e=this.dialogCloneForm;if(e.sourcePipelineId)if(e.targetNamespaces){var a,i=Object(o["a"])(e.targetNamespaces);try{for(i.s();!(a=i.n()).done;){var n=a.value,l=n.split("/");if(2!==l.length)return void this.$message.error("目标环境格式错误");var r=this.$router.resolve({name:"app-pipeline-edit",query:{operate:"clone",pipelineId:e.sourcePipelineId,targetCluster:l[0],targetNamespace:l[1]}});window.open(r.href,"_blank"),setTimeout((function(){t.dialogCloneVisible=!1}),500)}}catch(s){i.e(s)}finally{i.f()}}else this.$message.error("请选择目标环境 ");else this.$message.error("源环境信息缺失")},clickhouseLogPage:function(t,e,a,i){var n="/api/page/redirect?type=clickhouse&logName=".concat(t,"&cluster=").concat(e,"&namespace=").concat(a,"&app=").concat(i,"&_t")+Date.now();window.open(n)},showAddress:function(t){this.address={cluster:t.cluster,namespace:t.namespace,app:t.app},this.addressVisible=!0},convertStatus:function(t){switch(t){case"enabled":return"可用";case"disabled":return"禁用";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}},cellStyle:function(t){var e=t.row,a=t.column;t.rowIndex,t.columnIndex;if("运行数"===a.label&&e.extraAttr&&e.extraAttr.runningPodNum<1)return"background-color:#ffb980"}}},Z=Y,tt=(a("f604"),Object(v["a"])(Z,i,n,!1,null,null,null));e["default"]=tt.exports},a527:function(t,e,a){"use strict";a.d(e,"h",(function(){return n})),a.d(e,"d",(function(){return o})),a.d(e,"i",(function(){return l})),a.d(e,"e",(function(){return r})),a.d(e,"g",(function(){return s})),a.d(e,"c",(function(){return c})),a.d(e,"k",(function(){return p})),a.d(e,"l",(function(){return u})),a.d(e,"m",(function(){return d})),a.d(e,"o",(function(){return m})),a.d(e,"f",(function(){return f})),a.d(e,"r",(function(){return v})),a.d(e,"b",(function(){return b})),a.d(e,"a",(function(){return g})),a.d(e,"p",(function(){return h})),a.d(e,"q",(function(){return _})),a.d(e,"n",(function(){return y})),a.d(e,"j",(function(){return x}));a("96cf"),a("3b8d");var i=a("b775");function n(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:t,namespace:e,app:a}})}function o(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:t,namespace:e,app:a}})}function l(t,e){return Object(i["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:t,namespace:e}})}function r(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:t,namespace:e,pod:a}})}function s(t,e,a,n,o,l){return Object(i["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:t,namespace:e,pod:a,container:n,tailLines:o,previous:l}})}function c(t,e,a,i,n){var o="/api/v1/k8s/pod/stdout/download?cluster=".concat(t,"&namespace=").concat(e,"&pod=").concat(a,"&container=").concat(i,"&tailLines=").concat(n,'&_time="')+(new Date).getTime();window.open(o)}function p(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:t,namespace:e,pod:a}})}function u(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:t,namespace:e,pod:a}})}function d(t){return Object(i["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:t}})}function m(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:t,namespace:e,pod:a}})}function f(t){return Object(i["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:t})}function v(t){return Object(i["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:t})}function b(t,e){window.open("/api/v1/k8s/pod/file/download?fileId="+t+"&fileName="+e+"&_time="+(new Date).getTime())}function g(t){return Object(i["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:t})}function h(t){window.open("/api/v1/k8s/pod/file/preview?fileId="+t)}function _(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:t,namespace:e,pod:a}})}function y(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:t,namespace:e,pod:a}})}function x(t,e,a){return Object(i["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:t,namespace:e,pod:a}})}},aae3:function(t,e,a){var i=a("d3f4"),n=a("2d95"),o=a("2b4c")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==n(t))}},b39a:function(t,e,a){var i=a("d3f4");t.exports=function(t,e){if(!i(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b674:function(t,e,a){},bb0b:function(t,e,a){"use strict";a.d(e,"i",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"d",(function(){return l})),a.d(e,"k",(function(){return r})),a.d(e,"j",(function(){return s})),a.d(e,"b",(function(){return c})),a.d(e,"c",(function(){return p})),a.d(e,"e",(function(){return u})),a.d(e,"g",(function(){return d})),a.d(e,"f",(function(){return m})),a.d(e,"h",(function(){return f}));var i=a("b775");function n(t){return Object(i["a"])({url:"/v1/k8s/scale/cron",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/v1/k8s/scale/cron",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:t}})}function r(t){return Object(i["a"])({url:"/v1/k8s/scale/log",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:t})}function p(t){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:t})}function u(t){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:t})}function d(t){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+t,method:"post"})}function m(){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function f(t,e,a){return Object(i["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:t,namespace:e,app:a}})}},bcbd:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.jobs&&t.jobs.length>0?a("el-alert",{attrs:{type:"info"}},[a("template",{slot:"title"},[a("span",[t._v(t._s(t.jobTitle)+"：")]),t._v(" "),t._l(t.jobs,(function(e){return a("el-link",{staticStyle:{margin:"0 10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return t.jobPage(e.id)}}},["CD"===t.jobType?a("div",[t._v("\n          "+t._s(e.params.namespace)+" ("+t._s(e.params.cluster)+")\n        ")]):a("div",[t._v("\n          "+t._s(e.id)+"\n        ")])])}))],2)],2):t._e()],1)},n=[],o=a("76fe"),l={name:"job-runner-alert",props:{app:{type:String,default:""},jobType:{type:String,required:!0},jobTitle:{type:String,default:"运行中的任务"}},data:function(){return{jobs:[]}},computed:{},mounted:function(){this.loadJobs()},watch:{app:function(t){this.jobs=[],this.loadJobs()}},methods:{loadJobs:function(){var t=this;if(this.app){var e={params:{},app:this.app,status:["WAIT","RUNNING"],type:this.jobType,page:1,limit:50};Object(o["k"])(e).then((function(e){t.jobs=e.data.data})).catch((function(t){console.log("load job fail, "+t.message)}))}},jobPage:function(t){var e="";"CI"===this.jobType?e="cicd-image-build-detail":"CD"===this.jobType?e="cicd-app-deploy-detail":this.$message.error("未知的任务类型："+this.jobType);var a=this.$router.resolve({name:e,query:{jobId:t}});window.open(a.href,"_blank")}}},r=l,s=a("2877"),c=Object(s["a"])(r,i,n,!1,null,null,null);e["a"]=c.exports},bfce:function(t,e,a){"use strict";a("699c")},c26b:function(t,e,a){"use strict";var i=a("86cc").f,n=a("2aeb"),o=a("dcbc"),l=a("9b43"),r=a("f605"),s=a("4a59"),c=a("01f9"),p=a("d53b"),u=a("7a56"),d=a("9e1e"),m=a("67ab").fastKey,f=a("b39a"),v=d?"_s":"size",b=function(t,e){var a,i=m(e);if("F"!==i)return t._i[i];for(a=t._f;a;a=a.n)if(a.k==e)return a};t.exports={getConstructor:function(t,e,a,c){var p=t((function(t,i){r(t,p,e,"_i"),t._t=e,t._i=n(null),t._f=void 0,t._l=void 0,t[v]=0,void 0!=i&&s(i,a,t[c],t)}));return o(p.prototype,{clear:function(){for(var t=f(this,e),a=t._i,i=t._f;i;i=i.n)i.r=!0,i.p&&(i.p=i.p.n=void 0),delete a[i.i];t._f=t._l=void 0,t[v]=0},delete:function(t){var a=f(this,e),i=b(a,t);if(i){var n=i.n,o=i.p;delete a._i[i.i],i.r=!0,o&&(o.n=n),n&&(n.p=o),a._f==i&&(a._f=n),a._l==i&&(a._l=o),a[v]--}return!!i},forEach:function(t){f(this,e);var a,i=l(t,arguments.length>1?arguments[1]:void 0,3);while(a=a?a.n:this._f){i(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function(t){return!!b(f(this,e),t)}}),d&&i(p.prototype,"size",{get:function(){return f(this,e)[v]}}),p},def:function(t,e,a){var i,n,o=b(t,e);return o?o.v=a:(t._l=o={i:n=m(e,!0),k:e,v:a,p:i=t._l,n:void 0,r:!1},t._f||(t._f=o),i&&(i.n=o),t[v]++,"F"!==n&&(t._i[n]=o)),t},getEntry:b,setStrong:function(t,e,a){c(t,e,(function(t,a){this._t=f(t,e),this._k=a,this._l=void 0}),(function(){var t=this,e=t._k,a=t._l;while(a&&a.r)a=a.p;return t._t&&(t._l=a=a?a.n:t._t._f)?p(0,"keys"==e?a.k:"values"==e?a.v:[a.k,a.v]):(t._t=void 0,p(1))}),a?"entries":"values",!a,!0),u(e)}}},cae6:function(t,e,a){},cf89:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.stdout.loading,expression:"stdout.loading"}],staticClass:"pod-stdout"},[a("div",{staticStyle:{position:"relative"}},[a("div",{staticStyle:{"font-weight":"bold",float:"left","line-height":"40px"}},[t._v(t._s(this.pod)+" /\n      "),a("el-select",{staticStyle:{width:"240px"},attrs:{size:"mini",placeholder:"请选择容器"},on:{change:t.loadStdoutLog},model:{value:t.container,callback:function(e){t.container=t._n(e)},expression:"container"}},t._l(t.containers,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("div",{staticStyle:{float:"right","margin-right":"20px"}},[a("span",[a("el-checkbox",{on:{change:t.loadStdoutLog},model:{value:t.stdout.previous,callback:function(e){t.$set(t.stdout,"previous",e)},expression:"stdout.previous"}},[t._v("重启前日志")])],1),t._v(" "),a("span",{staticStyle:{"margin-left":"20px"}},[t._v("\n            行数:\n            "),a("el-select",{staticStyle:{width:"120px"},on:{change:t.loadStdoutLog},model:{value:t.stdout.tailLines,callback:function(e){t.$set(t.stdout,"tailLines",t._n(e))},expression:"stdout.tailLines"}},[a("el-option",{attrs:{label:"2000",value:"2000"}}),t._v(" "),a("el-option",{attrs:{label:"5000",value:"5000"}}),t._v(" "),a("el-option",{attrs:{label:"10000",value:"10000"}}),t._v(" "),a("el-option",{attrs:{label:"50000",value:"50000"}})],1)],1),t._v(" "),a("span",{staticStyle:{display:"none"}},[t._v("\n        自动刷新("+t._s(t.stdout.reloadPeriod)+"秒):\n      "),a("el-switch",{on:{change:t.autoReloadSwitch},model:{value:t.stdout.autoReload,callback:function(e){t.$set(t.stdout,"autoReload",e)},expression:"stdout.autoReload"}})],1),t._v(" "),a("el-button",{staticClass:"el-icon-refresh",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.loadStdoutLog()}}},[t._v("刷新\n      ")]),t._v(" "),a("el-button",{staticClass:"el-icon-download",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.podStdoutLogDownload()}}},[t._v("下载\n      ")])],1),t._v(" "),a("div",{staticStyle:{clear:"both"}})]),t._v(" "),a("div",{staticStyle:{"text-align":"right","margin-right":"5px"}},[t._v("加载时间: "+t._s(t.stdout.lastReloadTime))]),t._v(" "),a("pre",{staticClass:"stdout-log-content",attrs:{id:"stdout-log-content"}},[t._v(t._s(t.stdout.content))])])},n=[],o=a("a527"),l={name:"PodStdout",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!1},containers:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{container:this.containers[0],stdout:{visible:!1,loading:!1,autoReload:!1,previous:!1,tailLines:2e3,reloadPeriod:10,reloadTimer:null,content:"",lastReloadTime:"--"}}},watch:{pod:function(t,e){this.container=this.containers[0],this.loadStdoutLog()}},computed:{},mounted:function(){this.loadStdoutLog()},beforeDestroy:function(){this.stopReloadTimer()},methods:{showStdoutLogDialog:function(){this.stdout.visible=!0,this.loadStdoutLog(),this.stdout.autoReload&&this.startReloadTimer()},loadStdoutLog:function(){var t=this;this.pod&&(console.log("load pod ".concat(this.pod," stdout log")),this.stdout.loading=!0,Object(o["g"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines,this.stdout.previous).then((function(e){t.stdout.content=e.data;var a=t;setTimeout((function(){a.scrollStdoutLogView()}),200),setTimeout((function(){a.scrollStdoutLogView()}),500),setTimeout((function(){a.scrollStdoutLogView()}),700),t.stdout.lastReloadTime=(new Date).toLocaleTimeString()})).catch((function(e){t.$message.error(e.message),t.stopReloadTimer()})).finally((function(){t.stdout.loading=!1})))},podStdoutLogDownload:function(){Object(o["c"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines)},stopReload:function(){this.stdout.autoReload=!1,this.stopReloadTimer()},scrollStdoutLogView:function(){var t=document.getElementById("stdout-log-content");t.scrollTop=t.scrollHeight},startReloadTimer:function(){this.stdout.reloadTimer&&this.stopReloadTimer();var t=this;this.stdout.reloadTimer=setInterval((function(){t.loadStdoutLog()}),1e3*t.stdout.reloadPeriod),console.log("started pod stdout log reload timer :"+this.stdout.reloadTimer)},stopReloadTimer:function(){clearInterval(this.stdout.reloadTimer),console.log("stopped pod stdout log reload timer :"+this.stdout.reloadTimer)},autoReloadSwitch:function(t){this.stdout.autoReload=t,t?this.startReloadTimer():this.stopReloadTimer()}}},r=l,s=(a("fbec"),a("2877")),c=Object(s["a"])(r,i,n,!1,null,"6ff71a9f",null);e["a"]=c.exports},d152:function(t,e,a){"use strict";a("579f")},d2c8:function(t,e,a){var i=a("aae3"),n=a("be13");t.exports=function(t,e,a){if(i(e))throw TypeError("String#"+a+" doesn't accept regex!");return String(n(t))}},de9b:function(t,e,a){"use strict";a("b674")},df01:function(t,e,a){"use strict";a("6d2d")},e0b8:function(t,e,a){"use strict";var i=a("7726"),n=a("5ca1"),o=a("2aba"),l=a("dcbc"),r=a("67ab"),s=a("4a59"),c=a("f605"),p=a("d3f4"),u=a("79e5"),d=a("5cc5"),m=a("7f20"),f=a("5dbc");t.exports=function(t,e,a,v,b,g){var h=i[t],_=h,y=b?"set":"add",x=_&&_.prototype,w={},S=function(t){var e=x[t];o(x,t,"delete"==t||"has"==t?function(t){return!(g&&!p(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return g&&!p(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,a){return e.call(this,0===t?0:t,a),this})};if("function"==typeof _&&(g||x.forEach&&!u((function(){(new _).entries().next()})))){var k=new _,C=k[y](g?{}:-0,1)!=k,D=u((function(){k.has(1)})),O=d((function(t){new _(t)})),j=!g&&u((function(){var t=new _,e=5;while(e--)t[y](e,e);return!t.has(-0)}));O||(_=e((function(e,a){c(e,_,t);var i=f(new h,e,_);return void 0!=a&&s(a,b,i[y],i),i})),_.prototype=x,x.constructor=_),(D||j)&&(S("delete"),S("has"),b&&S("get")),(j||C)&&S(y),g&&x.clear&&delete x.clear}else _=v.getConstructor(e,t,b,y),l(_.prototype,a),r.NEED=!0;return m(_,t),w[t]=_,n(n.G+n.W+n.F*(_!=h),w),g||v.setStrong(_,t,b),_}},e18c:function(t,e,a){},f1ae:function(t,e,a){"use strict";var i=a("86cc"),n=a("4630");t.exports=function(t,e,a){e in t?i.f(t,e,n(0,a)):t[e]=a}},f604:function(t,e,a){"use strict";a("cae6")},fbec:function(t,e,a){"use strict";a("e18c")},ffc1:function(t,e,a){var i=a("5ca1"),n=a("504c")(!0);i(i.S,"Object",{entries:function(t){return n(t)}})}}]);
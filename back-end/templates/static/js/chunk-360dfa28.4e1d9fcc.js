(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-360dfa28"],{"02f4":function(t,e,a){var i=a("4588"),r=a("be13");t.exports=function(t){return function(e,a){var n,o,l=String(r(e)),c=i(a),s=l.length;return c<0||c>=s?t?"":void 0:(n=l.charCodeAt(c),n<55296||n>56319||c+1===s||(o=l.charCodeAt(c+1))<56320||o>57343?t?l.charAt(c):n:t?l.slice(c,c+2):o-56320+(n-55296<<10)+65536)}}},"0390":function(t,e,a){"use strict";var i=a("02f4")(!0);t.exports=function(t,e,a){return e+(a?i(t,e).length:1)}},"0bfb":function(t,e,a){"use strict";var i=a("cb7c");t.exports=function(){var t=i(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"214f":function(t,e,a){"use strict";a("b0c5");var i=a("2aba"),r=a("32e9"),n=a("79e5"),o=a("be13"),l=a("2b4c"),c=a("520a"),s=l("species"),d=!n((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),u=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var a="ab".split(t);return 2===a.length&&"a"===a[0]&&"b"===a[1]}();t.exports=function(t,e,a){var p=l(t),m=!n((function(){var e={};return e[p]=function(){return 7},7!=""[t](e)})),f=m?!n((function(){var e=!1,a=/a/;return a.exec=function(){return e=!0,null},"split"===t&&(a.constructor={},a.constructor[s]=function(){return a}),a[p](""),!e})):void 0;if(!m||!f||"replace"===t&&!d||"split"===t&&!u){var g=/./[p],v=a(o,p,""[t],(function(t,e,a,i,r){return e.exec===c?m&&!r?{done:!0,value:g.call(e,a,i)}:{done:!0,value:t.call(a,e,i)}:{done:!1}})),b=v[0],h=v[1];i(String.prototype,t,b),r(RegExp.prototype,p,2==e?function(t,e){return h.call(t,this,e)}:function(t){return h.call(t,this)})}}},2352:function(t,e,a){"use strict";a("a242")},"520a":function(t,e,a){"use strict";var i=a("0bfb"),r=RegExp.prototype.exec,n=String.prototype.replace,o=r,l="lastIndex",c=function(){var t=/a/,e=/b*/g;return r.call(t,"a"),r.call(e,"a"),0!==t[l]||0!==e[l]}(),s=void 0!==/()??/.exec("")[1],d=c||s;d&&(o=function(t){var e,a,o,d,u=this;return s&&(a=new RegExp("^"+u.source+"$(?!\\s)",i.call(u))),c&&(e=u[l]),o=r.call(u,t),c&&o&&(u[l]=u.global?o.index+o[0].length:e),s&&o&&o.length>1&&n.call(o[0],a,(function(){for(d=1;d<arguments.length-2;d++)void 0===arguments[d]&&(o[d]=void 0)})),o}),t.exports=o},"530d":function(t,e,a){"use strict";a.d(e,"d",(function(){return r})),a.d(e,"e",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return c}));var i=a("b775");function r(){return Object(i["a"])({url:"/v1/artifact/all",method:"get"})}function n(t){return Object(i["a"])({url:"/v1/artifact/search",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/v1/artifact/analysis",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/v1/artifact",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/v1/artifact",method:"delete",params:{id:t}})}},"5f1b":function(t,e,a){"use strict";var i=a("23c6"),r=RegExp.prototype.exec;t.exports=function(t,e){var a=t.exec;if("function"===typeof a){var n=a.call(t,e);if("object"!==typeof n)throw new TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==i(t))throw new TypeError("RegExp#exec called on incompatible receiver");return r.call(t,e)}},a242:function(t,e,a){},acaa:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-row",[a("el-col",{attrs:{span:4}},[a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建")])],1)]),t._v(" "),a("el-col",{attrs:{span:20}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword",e)},expression:"searchForm.keyword"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1)],1)])],1)],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,layout:"total,prev,pager,next",total:t.tableData.count},on:{"current-change":t.PageChange}}),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"Git地址",sortable:"",prop:"gitUrl"}}),t._v(" "),a("el-table-column",{attrs:{label:"模块",sortable:"",prop:"module"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建人",width:"120px",prop:"author"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",width:"140px",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popconfirm",{attrs:{title:"删除部署模块不应影响到应用和发布流程，确定继续删除吗？"},on:{confirm:function(a){return t.deleteRow(e.$index,e.row)}}},[a("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1)]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:"新建",visible:t.dialogEditVisible,width:"50%","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogEditVisible=e}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:t.dialogEditForm,"label-width":"120px",rules:t.dialogEditFormRules}},[a("el-form-item",{attrs:{label:"Git地址",prop:"gitUrl"}},[a("el-input",{model:{value:t.dialogEditForm.gitUrl,callback:function(e){t.$set(t.dialogEditForm,"gitUrl","string"===typeof e?e.trim():e)},expression:"dialogEditForm.gitUrl"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"模块"}},[a("el-input",{model:{value:t.dialogEditForm.module,callback:function(e){t.$set(t.dialogEditForm,"module","string"===typeof e?e.trim():e)},expression:"dialogEditForm.module"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.dialogEditForm.remark,callback:function(e){t.$set(t.dialogEditForm,"remark",e)},expression:"dialogEditForm.remark"}})],1)],1),t._v(" "),a("div",{staticStyle:{"padding-left":"120px","line-height":"1.5em"}},[t._v("\n      【Git地址】: 项目的Git地址，必须为 https 开头"),a("br"),t._v("\n      【模块】: 项目的子模块，如果没有则保留为空\n    ")]),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogEditVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create()}}},[t._v("确 定")])],1)],1)],1)},r=[],n=(a("aef6"),a("f559"),a("530d")),o={name:"artifactList",data:function(){var t=function(t,e,a){e.startsWith("https://")&&e.endsWith(".git")?a():a(new Error("地址必须为https://xxx.git格式"))};return{searchForm:{keyword:"",page:1,limit:10},tableData:[],tableLoading:!1,dialogEditVisible:!1,dialogEditForm:{id:0,gitUrl:"",module:""},dialogEditFormRules:{gitUrl:[{required:!0,message:"请输入项目Git地址",trigger:"blur"},{validator:t,message:"Git地址必须为https://xxx.git格式",trigger:"blur"}]}}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(n["e"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},resetEditForm:function(){this.dialogEditForm.id=0,this.dialogEditForm.gitUrl="",this.dialogEditForm.module=""},PageChange:function(t){this.searchForm.page=t,this.loadTableData()},createPage:function(){this.dialogEditVisible=!0,this.resetEditForm()},deleteRow:function(t,e){var a=this;Object(n["c"])(e.id).then((function(e){a.tableData.data.splice(t,1)})).catch((function(t){a.$message.error(t.message)}))},create:function(){var t=this;this.$refs["dialogEditForm"].validate((function(e){if(!e)return!1;Object(n["b"])(t.dialogEditForm).then((function(e){t.dialogEditVisible=!1,t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))}))}}},l=o,c=(a("2352"),a("2877")),s=Object(c["a"])(l,i,r,!1,null,"6cf89c18",null);e["default"]=s.exports},aef6:function(t,e,a){"use strict";var i=a("5ca1"),r=a("9def"),n=a("d2c8"),o="endsWith",l=""[o];i(i.P+i.F*a("5147")(o),"String",{endsWith:function(t){var e=n(this,t,o),a=arguments.length>1?arguments[1]:void 0,i=r(e.length),c=void 0===a?i:Math.min(r(a),i),s=String(t);return l?l.call(e,s,c):e.slice(c-s.length,c)===s}})},b0c5:function(t,e,a){"use strict";var i=a("520a");a("5ca1")({target:"RegExp",proto:!0,forced:i!==/./.exec},{exec:i})},b562:function(t,e,a){"use strict";a.d(e,"p",(function(){return r})),a.d(e,"b",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"l",(function(){return l})),a.d(e,"j",(function(){return c})),a.d(e,"d",(function(){return s})),a.d(e,"i",(function(){return d})),a.d(e,"h",(function(){return u})),a.d(e,"m",(function(){return p})),a.d(e,"o",(function(){return m})),a.d(e,"f",(function(){return f})),a.d(e,"e",(function(){return g})),a.d(e,"c",(function(){return v})),a.d(e,"k",(function(){return b})),a.d(e,"q",(function(){return h})),a.d(e,"n",(function(){return y})),a.d(e,"g",(function(){return _}));var i=a("b775");function r(t){return Object(i["a"])({url:"/v1/app/search",method:"get",params:t})}function n(){return Object(i["a"])({url:"/v1/app/apps-with-env",method:"get"})}function o(){return Object(i["a"])({url:"/v1/app/all",method:"get"})}function l(){return Object(i["a"])({url:"/v1/app/names",method:"get"})}function c(t){return Object(i["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function s(t){return Object(i["a"])({url:"/v1/app",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/v1/app",method:"put",data:t})}function u(t){return Object(i["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function p(t,e,a){return Object(i["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:a}})}function m(t){return Object(i["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function f(t){return Object(i["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function g(t){return Object(i["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function v(t){return Object(i["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function b(t,e){return Object(i["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function h(t,e){return Object(i["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function y(t,e){return Object(i["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function _(t){return Object(i["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:t}})}},f559:function(t,e,a){"use strict";var i=a("5ca1"),r=a("9def"),n=a("d2c8"),o="startsWith",l=""[o];i(i.P+i.F*a("5147")(o),"String",{startsWith:function(t){var e=n(this,t,o),a=r(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),i=String(t);return l?l.call(e,i,a):e.slice(a,a+i.length)===i}})},f82c:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-tabs",{attrs:{type:"border-card"},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{name:"app",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"5px"},attrs:{"icon-class":"app"}}),t._v("应用")],1),t._v(" "),a("app-list")],1),t._v(" "),a("el-tab-pane",{attrs:{label:"部署模块",name:"artifact",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"5px"},attrs:{"icon-class":"module2"}}),t._v("部署模块")],1),t._v(" "),a("artifact-list")],1)],1)],1)},r=[],n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-row",[a("el-col",{attrs:{span:4}},[a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建")])],1)]),t._v(" "),a("el-col",{attrs:{span:20}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"服务等级"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.searchForm.level,callback:function(e){t.$set(t.searchForm,"level",e)},expression:"searchForm.level"}},[a("el-option",{attrs:{label:"所有",value:""}}),t._v(" "),a("el-option",{attrs:{label:"L0-底层公共服务",value:"L0"}}),t._v(" "),a("el-option",{attrs:{label:"L1-核心业务服务",value:"L1"}}),t._v(" "),a("el-option",{attrs:{label:"L2-一般业务服务",value:"L2"}}),t._v(" "),a("el-option",{attrs:{label:"L3-非业务服务",value:"L3"}})],1)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword",e)},expression:"searchForm.keyword"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1)],1)])],1)],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,layout:"total,prev,pager,next",total:t.tableData.count},on:{"current-change":t.PageChange}}),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"name"}}),t._v(" "),a("el-table-column",{attrs:{label:"服务等级",prop:"level",width:"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"140px",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"发版权限",align:"center",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.orgs&&e.row.orgs.length>0?[a("i",{staticClass:"el-icon-circle-check",staticStyle:{color:"#67c23a","font-weight":"bold","font-size":"20px"}}),a("br"),t._v(" "),a("el-tooltip",{attrs:{effect:"dark",content:"部门："+e.row.orgs.join(","),placement:"top"}},[a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"}},[t._v("查看")])],1)]:[t._v("\n          --"),a("br")],t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.permPage(e.row)}}},[t._v("修改\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"发版窗口",align:"center","show-overflow-tooltip":"",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.timeWindow&&e.row.timeWindow.length>0?[a("i",{staticClass:"el-icon-circle-check",staticStyle:{color:"#67c23a","font-weight":"bold","font-size":"20px"}}),a("br"),t._v(" "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("pre",[t._v(t._s(e.row.timeWindowDesc))])]),t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"}},[t._v("查看")])],1)]:[t._v("\n          --"),a("br")],t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.editPage(e.row.name)}}},[t._v("修改\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"管理员",prop:"owner",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.admins&&e.row.admins.length>0?a("div",{staticStyle:{"font-size":"12px"}},[t._v("\n          "+t._s(e.row.admins.join(","))),a("br")]):a("div",[t._v("\n          --"),a("br")]),t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.editPage(e.row.name)}}},[t._v("修改\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"负责人",prop:"owner"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"220px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return t.editPage(e.row.name)}}},[t._v("修改\n        ")]),t._v(" "),a("el-popconfirm",{attrs:{title:"确定要删除【 "+e.row.name+" 】吗？"},on:{confirm:function(a){return t.deleteApp(e.$index,e.row)}}},[a("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-s-custom"},on:{click:function(a){return t.permPage(e.row)}}},[t._v("权限管理\n        ")]),t._v(" "),a("el-button",{staticStyle:{display:"none"},attrs:{type:"text",icon:"el-icon-position"},on:{click:function(a){return t.pipelinePage(e.row)}}},[t._v("发布流程\n        ")])]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:t.dialogEditTitle,visible:t.dialogEditVisible,width:"900px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogEditVisible=e}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:t.dialogEditForm,"label-width":"120px",rules:t.dialogEditFormRules}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{label:"ID"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.dialogEditForm.id,callback:function(e){t.$set(t.dialogEditForm,"id",e)},expression:"dialogEditForm.id"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"应用名",prop:"name"}},[a("el-input",{attrs:{autocomplete:"off",disabled:t.dialogEditForm.id>0},model:{value:t.dialogEditForm.name,callback:function(e){t.$set(t.dialogEditForm,"name","string"===typeof e?e.trim():e)},expression:"dialogEditForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"服务等级",prop:"level"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.dialogEditForm.level,callback:function(e){t.$set(t.dialogEditForm,"level",e)},expression:"dialogEditForm.level"}},[a("el-option",{attrs:{label:"L0-底层公共服务",value:"L0"}}),t._v(" "),a("el-option",{attrs:{label:"L1-核心业务服务",value:"L1"}}),t._v(" "),a("el-option",{attrs:{label:"L2-一般业务服务",value:"L2"}}),t._v(" "),a("el-option",{attrs:{label:"L3-非业务服务",value:"L3"}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"部门"}},[a("el-autocomplete",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入部门名称","fetch-suggestions":t.getOrgOptions},model:{value:t.dialogEditForm.org,callback:function(e){t.$set(t.dialogEditForm,"org",e)},expression:"dialogEditForm.org"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"管理员"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"","filter-method":function(e){t.userPinYinMatch(e)},placeholder:"请选择"},model:{value:t.dialogEditForm.admins,callback:function(e){t.$set(t.dialogEditForm,"admins",e)},expression:"dialogEditForm.admins"}},t._l(t.userOptions,(function(t){return a("el-option",{attrs:{label:t,value:t}})})),1),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888","margin-top":"3px"}},[t._v("\n          说明：管理员拥有应用所有权限\n        ")])],1),t._v(" "),a("el-form-item",{attrs:{label:"负责人"}},[a("el-input",{attrs:{disabled:""},model:{value:t.dialogEditForm.owner,callback:function(e){t.$set(t.dialogEditForm,"owner","string"===typeof e?e.trim():e)},expression:"dialogEditForm.owner"}}),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888"}},[t._v("\n          说明：负责人可以接受应用的告警信息。数据来源于配置中心系统\n          "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.ownerPage(t.dialogEditForm.name)}}},[t._v("去修改\n          ")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.dialogEditForm.remark,callback:function(e){t.$set(t.dialogEditForm,"remark",e)},expression:"dialogEditForm.remark"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"发版时间窗口",prop:"timeWindow"}},[t._l(t.dialogEditForm.timeWindow,(function(e,i){return a("el-row",{staticStyle:{margin:"5px"}},[a("el-col",{attrs:{span:12}},[a("el-select",{staticStyle:{width:"90%"},attrs:{multiple:"",placeholder:"请选择"},model:{value:e.daysOfWeek,callback:function(a){t.$set(e,"daysOfWeek",a)},expression:"item.daysOfWeek"}},t._l(t.daysOfWeekOptions,(function(t){return a("el-option",{key:t.index,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-time-picker",{staticStyle:{width:"80%"},attrs:{"is-range":"",format:"HH:mm","value-format":"HH:mm","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},model:{value:e.timeRange,callback:function(a){t.$set(e,"timeRange",a)},expression:"item.timeRange"}}),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.delTimePeriod(i)}}},[t._v("删除\n            ")])],1)],1)})),t._v(" "),a("el-button",{attrs:{icon:"el-icon-plus",size:"medium"},on:{click:function(e){return t.addTimePeriod()}}},[t._v("添加\n        ")])],2)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogEditVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createOrUpdate()}}},[t._v("确 定")])],1)],1)],1)},o=[],l=(a("6762"),a("2fdb"),a("4917"),a("7f7f"),a("2d63")),c=a("b562"),s=a("c24f"),d=a("d22a"),u={name:"appList",data:function(){var t=function(t,e,a){var i,r=Object(l["a"])(e);try{for(r.s();!(i=r.n()).done;){var n=i.value;if(!n.daysOfWeek||n.daysOfWeek.length<1)return void a(new Error("内容不能为空"));if(!n.timeRange||n.timeRange.length<1||!n.timeRange[0]||!n.timeRange[1])return void a(new Error("内容不能为空"))}}catch(o){r.e(o)}finally{r.f()}a()};return{daysOfWeekOptions:[{value:1,label:"周一"},{value:2,label:"周二"},{value:3,label:"周三"},{value:4,label:"周四"},{value:5,label:"周五"},{value:6,label:"周六"},{value:7,label:"周日"}],searchForm:{keyword:"",level:"",page:1,limit:10},tableData:[],tableLoading:!1,dialogEditTitle:"",dialogEditVisible:!1,dialogEditForm:{id:0,name:"",level:"",org:"",owner:"",remark:"",timeWindow:[]},userAllOptions:[],userOptions:[],dialogEditFormRules:{name:[{required:!0,message:"请输入应用名",trigger:"blur"}],timeWindow:[{validator:t,trigger:"blur"}]},orgOptions:[{value:"CRM"},{value:"PAAS"},{value:"企信"},{value:"运行平台"}]}},computed:{},watch:{},mounted:function(){this.searchForm.keyword=this.$route.query.app,this.loadTableData(),this.loadUserNames(),"true"===this.$route.query.showEditDialog&&this.$route.query.app&&this.editPage(this.$route.query.app)},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(c["p"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},getOrgOptions:function(t,e){var a=this.orgOptions;t&&(a=a.filter((function(e){return 0===e.value.toLowerCase().indexOf(t.toLowerCase())}))),e(a)},loadUserNames:function(){var t=this;Object(s["f"])().then((function(e){t.userOptions=e.data,t.userAllOptions=e.data})).catch((function(e){t.$message.error("加载用户数据出错：",e.message)}))},resetEditForm:function(){this.dialogEditForm.id=0,this.dialogEditForm.name="",this.dialogEditForm.org="",this.dialogEditForm.owner="",this.dialogEditForm.remark="",this.dialogEditForm.timeWindow=[]},PageChange:function(t){this.searchForm.page=t,this.loadTableData()},createPage:function(){this.dialogEditTitle="新建",this.dialogEditVisible=!0,this.resetEditForm()},deleteApp:function(t,e){var a=this;Object(c["h"])(e.name).then((function(e){a.tableData.data.splice(t,1)})).catch((function(t){a.$message.error(t.message)}))},editPage:function(t){var e=this;Object(c["j"])(t).then((function(t){e.dialogEditForm=t.data,e.dialogEditTitle="修改",e.dialogEditVisible=!0})).catch((function(t){e.$message.error(t.message)}))},createOrUpdate:function(){var t=this;this.$refs["dialogEditForm"].validate((function(e){if(!e)return!1;var a=t.dialogEditForm.id>0?c["i"]:c["d"];a(t.dialogEditForm).then((function(e){t.dialogEditVisible=!1,t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))}))},permPage:function(t){this.$router.push({name:"app-permission",query:{app:t.name}})},adminPage:function(t){this.$router.push({name:"app-permission",query:{app:t.name}})},pipelinePage:function(t){var e={app:t.name};this.$router.push({name:"cicd-app-deploy",query:e})},ownerPage:function(t){var e="/api/page/redirect?type=owner&app=".concat(t);window.open(e)},addTimePeriod:function(){this.dialogEditForm.timeWindow.length<1?(this.dialogEditForm.timeWindow.push({daysOfWeek:[1,2,3,4,5,6,7],timeRange:["23:00","23:59"]}),this.dialogEditForm.timeWindow.push({daysOfWeek:[1,2,3,4,5,6,7],timeRange:["00:00","06:00"]})):this.dialogEditForm.timeWindow.push({daysOfWeek:[],timeRange:["22:00","23:59"]})},delTimePeriod:function(t){this.dialogEditForm.timeWindow.splice(t,1)},userPinYinMatch:function(t){t&&(this.userOptions=this.userAllOptions.reduce((function(e,a){return(d["a"].match(a,t)||a.includes(t))&&e.push(a),e}),[]))}}},p=u,m=a("2877"),f=Object(m["a"])(p,n,o,!1,null,null,null),g=f.exports,v=a("acaa"),b={components:{ArtifactList:v["default"],AppList:g},mounted:function(){},computed:{},data:function(){return{activeTab:"app"}},methods:{}},h=b,y=Object(m["a"])(h,i,r,!1,null,null,null);e["default"]=y.exports}}]);
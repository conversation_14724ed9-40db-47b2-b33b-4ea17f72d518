(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-16a6030f"],{b562:function(t,e,a){"use strict";a.d(e,"p",(function(){return o})),a.d(e,"b",(function(){return n})),a.d(e,"a",(function(){return i})),a.d(e,"l",(function(){return l})),a.d(e,"j",(function(){return s})),a.d(e,"d",(function(){return c})),a.d(e,"i",(function(){return d})),a.d(e,"h",(function(){return u})),a.d(e,"m",(function(){return p})),a.d(e,"o",(function(){return m})),a.d(e,"f",(function(){return f})),a.d(e,"e",(function(){return h})),a.d(e,"c",(function(){return g})),a.d(e,"k",(function(){return v})),a.d(e,"q",(function(){return b})),a.d(e,"n",(function(){return _})),a.d(e,"g",(function(){return x}));var r=a("b775");function o(t){return Object(r["a"])({url:"/v1/app/search",method:"get",params:t})}function n(){return Object(r["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(r["a"])({url:"/v1/app/all",method:"get"})}function l(){return Object(r["a"])({url:"/v1/app/names",method:"get"})}function s(t){return Object(r["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function c(t){return Object(r["a"])({url:"/v1/app",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/v1/app",method:"put",data:t})}function u(t){return Object(r["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function p(t,e,a){return Object(r["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:a}})}function m(t){return Object(r["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function f(t){return Object(r["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function h(t){return Object(r["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function g(t){return Object(r["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function v(t,e){return Object(r["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function b(t,e){return Object(r["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(r["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function x(t){return Object(r["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:t}})}},f0ba:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-alert",{attrs:{type:"info",closable:!1}},[a("template",{slot:"title"},[a("div",{staticStyle:{color:"#555","font-size":"16px","line-height":"18px","font-weight":"bold"}},[a("div",{staticStyle:{"font-size":"18px",color:"#333"}},[a("span",[a("i",{staticClass:"el-icon-warning"})]),t._v(" "),a("span",{staticStyle:{"padding-left":"5px"}},[t._v("提示")])]),t._v(" "),a("div",{staticStyle:{"padding-top":"10px","line-height":"22px"}},[t._v("\n            权限申请后联系以下同学进行审批："+t._s(t.admins.join("、"))+"\n          ")])])])],2)],1),t._v(" "),a("el-dialog",{attrs:{title:"申请临时授权",visible:t.dialogAddVisible,width:"800px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogAddVisible=e}}},[a("el-form",{ref:"dialogAddForm",attrs:{"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"应用名"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请输入应用名称"},model:{value:t.dialogAddForm.app,callback:function(e){t.$set(t.dialogAddForm,"app",e)},expression:"dialogAddForm.app"}},t._l(t.apps,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("el-form-item",{staticStyle:{margin:"-30px 0 10px 0",color:"#555","font-size":"12px"},attrs:{label:" "}},[a("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("需要操作的应用")])]),t._v(" "),a("el-form-item",{attrs:{label:"授权操作"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.dialogAddForm.operate,callback:function(e){t.$set(t.dialogAddForm,"operate",e)},expression:"dialogAddForm.operate"}},t._l(t.operateOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),a("el-form-item",{staticStyle:{margin:"-30px 0 10px 0",color:"#555","font-size":"12px"},attrs:{label:" "}},[a("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("需要执行的操作，比如：镜像构建+发版")])]),t._v(" "),a("el-form-item",{attrs:{label:"授权时间"}},[a("el-time-select",{staticStyle:{width:"160px"},attrs:{placeholder:"起始时间","picker-options":{start:"00:00",step:"01:00",end:"23:59"}},model:{value:t.dialogAddForm.startHour,callback:function(e){t.$set(t.dialogAddForm,"startHour",e)},expression:"dialogAddForm.startHour"}}),t._v(" "),a("span",{staticStyle:{display:"inline-block",padding:"0 5px"}},[t._v("至")]),t._v(" "),a("el-time-select",{staticStyle:{width:"160px"},attrs:{placeholder:"结束时间","picker-options":{start:"00:00",step:"01:00",end:"24:00",minTime:t.dialogAddForm.startHour}},model:{value:t.dialogAddForm.endHour,callback:function(e){t.$set(t.dialogAddForm,"endHour",e)},expression:"dialogAddForm.endHour"}})],1),t._v(" "),a("el-form-item",{staticStyle:{margin:"-30px 0 10px 0",color:"#555","font-size":"12px"},attrs:{label:" "}},[a("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("需要在什么时间段操作，如果跨天请分开申请")])]),t._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{maxlength:"100",placeholder:"请输入备注信息"},model:{value:t.dialogAddForm.remark,callback:function(e){t.$set(t.dialogAddForm,"remark",e)},expression:"dialogAddForm.remark"}})],1),t._v(" "),a("el-form-item",{staticStyle:{margin:"-30px 0 10px 0",color:"#555","font-size":"12px"},attrs:{label:" "}},[a("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("请填写授权原因，不要超过100个字符")])])],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogAddVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create()}}},[t._v("确 定")])],1)],1),t._v(" "),a("div",[a("el-button",{staticStyle:{"margin-bottom":"10px"},attrs:{icon:"el-icon-circle-plus-outline",type:"primary"},on:{click:t.addAuth}},[t._v("申请临时授权")]),t._v(" "),a("div",{staticStyle:{float:"right"}},[a("el-select",{staticStyle:{width:"180px","margin-left":"30px"},attrs:{placeholder:"审批状态"},model:{value:t.searchForm.approved,callback:function(e){t.$set(t.searchForm,"approved",e)},expression:"searchForm.approved"}},[a("el-option",{attrs:{label:"所有",value:""}}),t._v(" "),a("el-option",{attrs:{label:"已审核",value:"true"}}),t._v(" "),a("el-option",{attrs:{label:"待审批",value:"false"}})],1),t._v(" "),a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.search()}}},[t._v("查询")])],1)],1),t._v(" "),a("div",{staticStyle:{float:"right"}},[a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,layout:"total,sizes,prev,pager,next",total:t.tableData.count},on:{"current-change":t.pageChange,"size-change":t.handleSizeChange}})],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"应用",prop:"app"}}),t._v(" "),a("el-table-column",{attrs:{label:"授权操作",prop:"operateDesc"}}),t._v(" "),a("el-table-column",{attrs:{label:"申请人",prop:"user"}}),t._v(" "),a("el-table-column",{attrs:{label:"审核状态",prop:"approved"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.approved?a("el-tag",{attrs:{type:"success"}},[t._v("已审核")]):a("el-tag",{attrs:{type:"info"}},[t._v("待审核")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"允许操作的时间",prop:"date",width:"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n        "+t._s(e.row.date)),a("b",{staticStyle:{"padding-left":"8px"}},[t._v(t._s(e.row.startHour)+" 至 "+t._s(e.row.endHour))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(a){return t.delTempAuthAuth(e.row.id)}}},[a("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1),t._v(" "),e.row.approved?a("el-button",{attrs:{slot:"reference",type:"text"},on:{click:function(a){return t.auditTempAuth(e.row.id,"false")}},slot:"reference"},[t._v("驳回\n        ")]):a("el-button",{attrs:{slot:"reference",type:"text"},on:{click:function(a){return t.auditTempAuth(e.row.id,"true")}},slot:"reference"},[t._v("审批\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"审核人",prop:"approver"}}),t._v(" "),a("el-table-column",{attrs:{label:"申请时间",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"备注","show-overflow-tooltip":"",prop:"remark"}})],1)],1)},o=[],n=a("c24f"),i=a("b775");function l(t){return Object(i["a"])({url:"/v1/temp-auth",method:"post",data:t})}function s(t,e){return Object(i["a"])({url:"/v1/temp-auth/audit?id=".concat(t,"&approved=").concat(e),method:"post"})}function c(t){return Object(i["a"])({url:"/v1/temp-auth/search",method:"get",params:t})}function d(t){return Object(i["a"])({url:"/v1/temp-auth/admins",method:"get",params:t})}function u(t){return Object(i["a"])({url:"/v1/temp-auth",method:"delete",params:{id:t}})}var p=a("b562"),m={data:function(){return{loading:!1,apps:[],dialogAddVisible:!1,admins:[],dialogAddForm:{app:"",user:"",operate:"deploy",startHour:"00:00",endHour:"01:00",remark:""},operateOptions:[{value:"deploy",label:"镜像构建+发布"}],userOptions:[],selectUserLoading:!1,selectUserOptions:[],tableData:[],tableLoading:!1,searchForm:{approved:"",page:1,limit:20}}},computed:{},mounted:function(){this.loadAdmins(),this.loadApps(),this.loadTableData(),"true"===this.$route.query.showAddDialog&&(this.dialogAddForm.app=this.$route.query.app,this.addAuth()),this.loadUserNames()},methods:{addAuth:function(){this.dialogAddVisible=!0;var t=(new Date).getHours(),e=t+2;e=e>24?24:e,this.dialogAddForm.startHour=t>9?"".concat(t,":00"):"0".concat(t,":00"),this.dialogAddForm.endHour=e>9?"".concat(e,":00"):"0".concat(e,":00")},auditTempAuth:function(t,e){var a=this,r="true"===e?"确定要审核通过吗？通过后，被授权者可以执行授权操作":"确定要驳回吗？驳回后，被授权者将不能执行授权操作。";this.$confirm(r,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){s(t,e).then((function(t){a.$message.success("操作成功"),a.loadTableData()})).catch((function(t){a.$message.error(t.message)}))}))},searchUserByRealName:function(t){var e=this;""!==t?(this.selectUserLoading=!0,Object(n["c"])(t).then((function(t){e.selectUserOptions=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.selectUserLoading=!1}))):this.selectUserOptions=[]},loadUserNames:function(){var t=this;Object(n["f"])().then((function(e){t.userOptions=e.data})).catch((function(e){t.$message.error("加载用户数据出错：",e.message)}))},loadApps:function(){var t=this;Object(p["l"])().then((function(e){t.apps=e.data})).catch((function(e){t.$message.error("加载应用数据出错！ "+e.message)}))},create:function(){var t=this;this.dialogAddForm.app?this.dialogAddForm.operate?this.dialogAddForm.startHour&&this.dialogAddForm.endHour?l(this.dialogAddForm).then((function(e){t.dialogAddVisible=!1,t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)})):this.$message.error("请选择授权时间"):this.$message.error("请填写授权类型"):this.$message.error("请选择应用")},loadAdmins:function(){var t=this;d(this.searchForm).then((function(e){t.admins=e.data})).catch((function(e){t.$message.error(e.message)}))},loadTableData:function(){var t=this;this.tableLoading=!0,c(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},pageChange:function(t){this.searchForm.page=t,this.loadTableData()},delTempAuthAuth:function(t){var e=this;u(t).then((function(t){e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)}))},search:function(){this.loadTableData()},handleSizeChange:function(t){this.searchForm.limit=t,this.searchForm.page=1,this.loadTableData()}}},f=m,h=a("2877"),g=Object(h["a"])(f,r,o,!1,null,null,null);e["default"]=g.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0ce4a4a0"],{"88fa":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"app-redeploy"}}),t._v(" "),a("el-alert",{attrs:{title:"应用批量重发",type:"info",description:"使用当前运行的版本重新发布应用。 操作步骤：创建任务 → 开始 → 每间隔1分钟发布一个服务","show-icon":""}}),t._v(" "),a("div",{staticStyle:{margin:"10px"}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){t.appRestartVisible=!0}}},[t._v("创建任务")]),t._v(" "),t.job&&t.job.items?a("div",{staticStyle:{display:"inline-block","margin-left":"30px"}},[0===t.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:t.start}},[t._v("开始")]):t._e(),t._v(" "),1===t.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:t.stop}},[t._v("暂停")]):t._e(),t._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:t.remove}},[t._v("删除")])],1):t._e()],1),t._v(" "),t.job&&t.job.items?a("div",[a("div",{staticStyle:{margin:"20px"}},[t._v("任务状态： "+t._s(t.job.status))]),t._v(" "),a("el-timeline",t._l(t.job.items,(function(e,o){return a("el-timeline-item",{key:o,attrs:{color:e.output.endsWith("success")?"#67C23A":e.output.includes("fail,")?"#F56C6C":"#909399"}},[a("b",{staticStyle:{"padding-right":"10px"}},[t._v(t._s(o+1))]),t._v(t._s(e.output)+"\n      ")])})),1)],1):a("div",{staticStyle:{margin:"20px"}},[t._v("\n    -无任何数据-\n  ")]),t._v(" "),a("el-dialog",{attrs:{title:"服务批量重发",visible:t.appRestartVisible},on:{"update:visible":function(e){t.appRestartVisible=e}}},[a("el-form",{attrs:{"label-width":"100px"}},[a("el-form-item",{attrs:{label:"服务列表"}},[t._t("label",[t._v("\n          内容为json格式，每行格式为 cluster/namespace/app，\n          "),a("el-popover",{attrs:{placement:"bottom-end",trigger:"click"}},[a("div",[t._v("\n              ["),a("br"),t._v('\n                "k8s0/fstest/fs-aaa-bbb",'),a("br"),t._v('\n                "k8s0/fstest/fs-k8s-tomcat-test",'),a("br"),t._v('\n                "k8s0/firstshare/fs-k8s-tomcat-test"'),a("br"),t._v("\n              ]\n            ")]),t._v(" "),a("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("点击查看示例")])],1)]),t._v(" "),a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:20}},model:{value:t.appRestartForm,callback:function(e){t.appRestartForm=e},expression:"appRestartForm"}})],2),t._v(" "),a("el-form-item",{attrs:{label:"发版参数"}},[a("el-checkbox",{model:{value:t.forceCodeCompile,callback:function(e){t.forceCodeCompile=e},expression:"forceCodeCompile"}},[t._v("【强制代码编译】")]),t._v(" "),a("el-checkbox",{model:{value:t.dependencyCheck,callback:function(e){t.dependencyCheck=e},expression:"dependencyCheck"}},[t._v("【依赖包版本校验】")])],1),t._v(" "),a("el-form-item",{attrs:{label:"父POM "}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"如果不填写，则使用上一次发版的父POM"},model:{value:t.parentPom,callback:function(e){t.parentPom=e},expression:"parentPom"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"tag后缀"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"tag后缀"},model:{value:t.suffixVersion,callback:function(e){t.suffixVersion=e},expression:"suffixVersion"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"-20px"},attrs:{"label-width":"100"}},[a("div",{staticStyle:{color:"#666","font-size":"12px","padding-left":"60px"}},[t._v("备注：如果填写了tag后缀，使用当前运行tag+后缀名创建新tag发布；否则，使用当前运行tag重发")])]),t._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"输入备注"},model:{value:t.message,callback:function(e){t.message=e},expression:"message"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.appRestartVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.create}},[t._v("确 定")])],1)],1)],1)},n=[],r=a("c1ab"),c=a("a68b"),s={components:{appManageTab:c["a"]},mounted:function(){this.loadOutput();var t=this.loadOutput;this.timer=setInterval((function(){t()}),3e3)},beforeDestroy:function(){this.timer&&(console.log("close timer"),clearInterval(this.timer))},computed:{},data:function(){return{timer:null,job:{items:[],status:0},appRestartVisible:!1,forceCodeCompile:!0,dependencyCheck:!0,suffixVersion:"",message:"",appRestartForm:"",parentPom:""}},methods:{loadOutput:function(){var t=this;Object(r["b"])().then((function(e){t.job=e.data})).catch((function(e){t.$message.error(e.message)}))},create:function(){var t=this;this.$confirm("是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(r["f"])("redeploy","",t.suffixVersion,t.message,t.forceCodeCompile,t.dependencyCheck,t.parentPom,t.appRestartForm).then((function(e){t.appRestartVisible=!1,t.$message.success("Success"),t.loadOutput()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){console.log("取消")}))},start:function(){var t=this;Object(r["B"])(this.appRestartForm).then((function(e){t.$message.success("Success"),t.loadOutput()})).catch((function(e){t.$message.error(e.message)}))},stop:function(){var t=this;Object(r["D"])(this.appRestartForm).then((function(e){t.$message.success("Success"),t.loadOutput()})).catch((function(e){t.$message.error(e.message)}))},remove:function(){var t=this;this.$confirm("确定要取消?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(r["w"])(t.appRestartForm).then((function(e){t.$message.success("Success")})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){console.log("取消")}))}}},l=s,i=a("2877"),u=Object(i["a"])(l,o,n,!1,null,null,null);e["default"]=u.exports},a68b:function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},n=[],r=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),c=r,s=(a("d54f"),a("2877")),l=Object(s["a"])(c,o,n,!1,null,null,null);e["a"]=l.exports},c1ab:function(t,e,a){"use strict";a.d(e,"i",(function(){return n})),a.d(e,"k",(function(){return r})),a.d(e,"y",(function(){return c})),a.d(e,"z",(function(){return s})),a.d(e,"d",(function(){return l})),a.d(e,"g",(function(){return i})),a.d(e,"C",(function(){return u})),a.d(e,"E",(function(){return p})),a.d(e,"x",(function(){return d})),a.d(e,"b",(function(){return m})),a.d(e,"f",(function(){return f})),a.d(e,"B",(function(){return b})),a.d(e,"D",(function(){return h})),a.d(e,"w",(function(){return v})),a.d(e,"F",(function(){return g})),a.d(e,"l",(function(){return y})),a.d(e,"e",(function(){return _})),a.d(e,"a",(function(){return j})),a.d(e,"A",(function(){return O})),a.d(e,"j",(function(){return x})),a.d(e,"h",(function(){return k})),a.d(e,"r",(function(){return $})),a.d(e,"u",(function(){return C})),a.d(e,"v",(function(){return w})),a.d(e,"n",(function(){return R})),a.d(e,"o",(function(){return S})),a.d(e,"s",(function(){return V})),a.d(e,"t",(function(){return F})),a.d(e,"c",(function(){return P})),a.d(e,"p",(function(){return T})),a.d(e,"q",(function(){return z})),a.d(e,"m",(function(){return B}));var o=a("b775");function n(t){return Object(o["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function r(t){return Object(o["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(o["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function s(t){return Object(o["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function l(){return Object(o["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function i(t){return Object(o["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function u(t){return Object(o["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(o["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(o["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,a,n,r,c,s,l){return Object(o["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(r,"&fixVersion=").concat(e,"&suffixVersion=").concat(a,"&message=").concat(n,"&dependencyCheck=").concat(c,"&parentPom=").concat(s),method:"post",data:l})}function b(t){return Object(o["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function h(t){return Object(o["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function v(t){return Object(o["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(o["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,a,n){return Object(o["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&overrideNamespace=").concat(n),method:"post"})}function _(t,e,a,n,r){return Object(o["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(a,"&remark=").concat(n,"&dryRun=").concat(r),method:"post"})}function j(){return Object(o["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function O(){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function x(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function k(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function $(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function C(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function w(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function R(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function S(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function V(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function F(t,e){return Object(o["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function P(t){return Object(o["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function T(t){return Object(o["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function z(t){return Object(o["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function B(t,e,a){return Object(o["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(a),method:"get"})}},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);
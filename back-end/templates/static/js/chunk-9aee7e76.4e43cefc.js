(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9aee7e76"],{"02f4":function(t,e,n){var o=n("4588"),a=n("be13");t.exports=function(t){return function(e,n){var s,r,i=String(a(e)),l=o(n),c=i.length;return l<0||l>=c?t?"":void 0:(s=i.charCodeAt(l),s<55296||s>56319||l+1===c||(r=i.charCodeAt(l+1))<56320||r>57343?t?i.charAt(l):s:t?i.slice(l,l+2):r-56320+(s-55296<<10)+65536)}}},"0390":function(t,e,n){"use strict";var o=n("02f4")(!0);t.exports=function(t,e,n){return e+(n?o(t,e).length:1)}},"0a49":function(t,e,n){var o=n("9b43"),a=n("626a"),s=n("4bf8"),r=n("9def"),i=n("cd1c");t.exports=function(t,e){var n=1==t,l=2==t,c=3==t,p=4==t,u=6==t,d=5==t||u,f=e||i;return function(e,i,m){for(var v,h,b=s(e),g=a(b),_=o(i,m,3),x=r(g.length),y=0,k=n?f(e,x):l?f(e,0):void 0;x>y;y++)if((d||y in g)&&(v=g[y],h=_(v,y,b),t))if(n)k[y]=h;else if(h)switch(t){case 3:return!0;case 5:return v;case 6:return y;case 2:k.push(v)}else if(p)return!1;return u?-1:c||p?p:k}}},"0bfb":function(t,e,n){"use strict";var o=n("cb7c");t.exports=function(){var t=o(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},1169:function(t,e,n){var o=n("2d95");t.exports=Array.isArray||function(t){return"Array"==o(t)}},"20d6":function(t,e,n){"use strict";var o=n("5ca1"),a=n("0a49")(6),s="findIndex",r=!0;s in[]&&Array(1)[s]((function(){r=!1})),o(o.P+o.F*r,"Array",{findIndex:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")(s)},"214f":function(t,e,n){"use strict";n("b0c5");var o=n("2aba"),a=n("32e9"),s=n("79e5"),r=n("be13"),i=n("2b4c"),l=n("520a"),c=i("species"),p=!s((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),u=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();t.exports=function(t,e,n){var d=i(t),f=!s((function(){var e={};return e[d]=function(){return 7},7!=""[t](e)})),m=f?!s((function(){var e=!1,n=/a/;return n.exec=function(){return e=!0,null},"split"===t&&(n.constructor={},n.constructor[c]=function(){return n}),n[d](""),!e})):void 0;if(!f||!m||"replace"===t&&!p||"split"===t&&!u){var v=/./[d],h=n(r,d,""[t],(function(t,e,n,o,a){return e.exec===l?f&&!a?{done:!0,value:v.call(e,n,o)}:{done:!0,value:t.call(n,e,o)}:{done:!1}})),b=h[0],g=h[1];o(String.prototype,t,b),a(RegExp.prototype,d,2==e?function(t,e){return g.call(t,this,e)}:function(t){return g.call(t,this)})}}},"26ef":function(t,e,n){},"28a5":function(t,e,n){"use strict";var o=n("aae3"),a=n("cb7c"),s=n("ebd6"),r=n("0390"),i=n("9def"),l=n("5f1b"),c=n("520a"),p=n("79e5"),u=Math.min,d=[].push,f="split",m="length",v="lastIndex",h=4294967295,b=!p((function(){RegExp(h,"y")}));n("214f")("split",2,(function(t,e,n,p){var g;return g="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[m]||2!="ab"[f](/(?:ab)*/)[m]||4!="."[f](/(.?)(.?)/)[m]||"."[f](/()()/)[m]>1||""[f](/.?/)[m]?function(t,e){var a=String(this);if(void 0===t&&0===e)return[];if(!o(t))return n.call(a,t,e);var s,r,i,l=[],p=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),u=0,f=void 0===e?h:e>>>0,b=new RegExp(t.source,p+"g");while(s=c.call(b,a)){if(r=b[v],r>u&&(l.push(a.slice(u,s.index)),s[m]>1&&s.index<a[m]&&d.apply(l,s.slice(1)),i=s[0][m],u=r,l[m]>=f))break;b[v]===s.index&&b[v]++}return u===a[m]?!i&&b.test("")||l.push(""):l.push(a.slice(u)),l[m]>f?l.slice(0,f):l}:"0"[f](void 0,0)[m]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,o){var a=t(this),s=void 0==n?void 0:n[e];return void 0!==s?s.call(n,a,o):g.call(String(a),n,o)},function(t,e){var o=p(g,t,this,e,g!==n);if(o.done)return o.value;var c=a(t),d=String(this),f=s(c,RegExp),m=c.unicode,v=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(b?"y":"g"),_=new f(b?c:"^(?:"+c.source+")",v),x=void 0===e?h:e>>>0;if(0===x)return[];if(0===d.length)return null===l(_,d)?[d]:[];var y=0,k=0,S=[];while(k<d.length){_.lastIndex=b?k:0;var C,w=l(_,b?d:d.slice(k));if(null===w||(C=u(i(_.lastIndex+(b?0:k)),d.length))===y)k=r(d,k,m);else{if(S.push(d.slice(y,k)),S.length===x)return S;for(var P=1;P<=w.length-1;P++)if(S.push(w[P]),S.length===x)return S;k=y=C}}return S.push(d.slice(y)),S}]}))},3846:function(t,e,n){n("9e1e")&&"g"!=/./g.flags&&n("86cc").f(RegExp.prototype,"flags",{configurable:!0,get:n("0bfb")})},5147:function(t,e,n){var o=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[o]=!1,!"/./"[t](e)}catch(a){}}return!0}},"51a9":function(t,e,n){"use strict";n.d(e,"c",(function(){return a})),n.d(e,"e",(function(){return s})),n.d(e,"d",(function(){return r})),n.d(e,"l",(function(){return i})),n.d(e,"m",(function(){return l})),n.d(e,"a",(function(){return c})),n.d(e,"f",(function(){return p})),n.d(e,"i",(function(){return u})),n.d(e,"j",(function(){return d})),n.d(e,"k",(function(){return f})),n.d(e,"n",(function(){return m})),n.d(e,"g",(function(){return v})),n.d(e,"b",(function(){return h})),n.d(e,"h",(function(){return b})),n.d(e,"o",(function(){return g}));var o=n("b775");function a(t){return Object(o["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function s(t){return Object(o["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function r(t,e,n){return Object(o["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(o["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function l(t){return Object(o["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function c(){return Object(o["a"])({url:"/v1/pipeline/all",method:"get"})}function p(t){return Object(o["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function u(t){return Object(o["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(o["a"])({url:"/v1/pipeline",method:"post",data:t})}function f(t){return Object(o["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function m(t){return Object(o["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function v(t,e,n,a){return Object(o["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:a}})}function h(t){return Object(o["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function b(t,e,n,a){return Object(o["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:a}})}function g(t){return Object(o["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"520a":function(t,e,n){"use strict";var o=n("0bfb"),a=RegExp.prototype.exec,s=String.prototype.replace,r=a,i="lastIndex",l=function(){var t=/a/,e=/b*/g;return a.call(t,"a"),a.call(e,"a"),0!==t[i]||0!==e[i]}(),c=void 0!==/()??/.exec("")[1],p=l||c;p&&(r=function(t){var e,n,r,p,u=this;return c&&(n=new RegExp("^"+u.source+"$(?!\\s)",o.call(u))),l&&(e=u[i]),r=a.call(u,t),l&&r&&(u[i]=u.global?r.index+r[0].length:e),c&&r&&r.length>1&&s.call(r[0],n,(function(){for(p=1;p<arguments.length-2;p++)void 0===arguments[p]&&(r[p]=void 0)})),r}),t.exports=r},"530d":function(t,e,n){"use strict";n.d(e,"d",(function(){return a})),n.d(e,"e",(function(){return s})),n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return l}));var o=n("b775");function a(){return Object(o["a"])({url:"/v1/artifact/all",method:"get"})}function s(t){return Object(o["a"])({url:"/v1/artifact/search",method:"get",params:t})}function r(t){return Object(o["a"])({url:"/v1/artifact/analysis",method:"get",params:t})}function i(t){return Object(o["a"])({url:"/v1/artifact",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/v1/artifact",method:"delete",params:{id:t}})}},"5f1b":function(t,e,n){"use strict";var o=n("23c6"),a=RegExp.prototype.exec;t.exports=function(t,e){var n=t.exec;if("function"===typeof n){var s=n.call(t,e);if("object"!==typeof s)throw new TypeError("RegExp exec method returned something other than an Object or null");return s}if("RegExp"!==o(t))throw new TypeError("RegExp#exec called on incompatible receiver");return a.call(t,e)}},"6b54":function(t,e,n){"use strict";n("3846");var o=n("cb7c"),a=n("0bfb"),s=n("9e1e"),r="toString",i=/./[r],l=function(t){n("2aba")(RegExp.prototype,r,t,!0)};n("79e5")((function(){return"/a/b"!=i.call({source:"a",flags:"b"})}))?l((function(){var t=o(this);return"/".concat(t.source,"/","flags"in t?t.flags:!s&&t instanceof RegExp?a.call(t):void 0)})):i.name!=r&&l((function(){return i.call(this)}))},"8b9c":function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container pipeline-edit-container",attrs:{"element-loading-text":"拼命加载中"}},[n("div",{staticStyle:{"max-width":"960px"}},[n("el-form",{ref:"form",attrs:{rules:t.rules,model:t.form,"label-width":"120px"}},[n("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{label:"ID"}},[n("el-input",{attrs:{disabled:!0},model:{value:t.form.id,callback:function(e){t.$set(t.form,"id",e)},expression:"form.id"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"所属应用",prop:"app"}},[n("el-input",{attrs:{disabled:!0},model:{value:t.form.app,callback:function(e){t.$set(t.form,"app",e)},expression:"form.app"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"运行环境"}},[n("el-row",[n("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[n("el-form-item",{attrs:{prop:"cluster"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",disabled:t.isEdit||t.isClone,filterable:""},on:{change:t.clusterChange},model:{value:t.form.cluster,callback:function(e){t.$set(t.form,"cluster",e)},expression:"form.cluster"}},t._l(t.clusterOptions,(function(e){return e.enable?n("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}}):t._e()})),1)],1)],1),t._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"namespace"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",disabled:t.isEdit||t.isClone,filterable:""},on:{change:t.namespaceChange},model:{value:t.form.namespace,callback:function(e){t.$set(t.form,"namespace",e)},expression:"form.namespace"}},t._l(t.namespaceOptions,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                k8s集群：一般保持默认即可。如果是专属云，一般都有自己专有的k8s集群，请选择对应的集群\n              ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1)],1),t._v(" "),n("el-form-item",{attrs:{label:"基础镜像",prop:"baseImage"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:t.form.baseImage,callback:function(e){t.$set(t.form,"baseImage",e)},expression:"form.baseImage"}},t._l(t.baseImageOptions,(function(t){return n("el-option",{key:t,attrs:{label:t.split("/").reverse()[0],value:t}})})),1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n              基础镜像包含应用需要的运行环境，比如jdk版本, tomcat版本。一般保持默认即可\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"部署策略"}},[n("el-select",{staticStyle:{width:"100%"},model:{value:t.form.deployStrategy,callback:function(e){t.$set(t.form,"deployStrategy",e)},expression:"form.deployStrategy"}},t._l(t.deployStrategyOptions,(function(t){return n("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[n("b",[t._v("滚动：")]),t._v("启动一个新实例，等达到健康状态后删除一个旧实例。如此循环，直到所有旧实例都被替换掉（平滑度好，推荐）"),n("br"),t._v(" "),n("b",[t._v("重建：")]),t._v("删除所有旧实例 -> 启动所有新实例（平滑度低，不会有多版本并存，发版速度快）\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"实例数"}},[n("el-input-number",{attrs:{step:1,min:0,max:50},on:{change:t.replicaChange},model:{value:t.form.replicas,callback:function(e){t.$set(t.form,"replicas",e)},expression:"form.replicas"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"资源（最大值）"}},[n("el-row",[n("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"0.1",min:"0.2",max:"15"},on:{change:t.limitCPUChange},model:{value:t.form.resources.limitCPU,callback:function(e){t.$set(t.form.resources,"limitCPU",t._n(e))},expression:"form.resources.limitCPU"}},[n("template",{slot:"prepend"},[t._v("CPU")])],2)],1),t._v(" "),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"128",min:"128",max:"30720"},on:{change:t.limitMemChange},model:{value:t.form.resources.limitMemory,callback:function(e){t.$set(t.form.resources,"limitMemory",t._n(e))},expression:"form.resources.limitMemory"}},[n("template",{slot:"prepend"},[t._v("内存")]),t._v(" "),n("template",{slot:"append"},[t._v("MB")])],2)],1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                CPU：单个实例可使用的最大CPU核心数 "),n("br"),t._v("\n                内存：单个实例可使用的最大内存\n              ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1)],1),t._v(" "),n("el-form-item",{attrs:{label:"资源（最小值）"}},[n("el-row",[n("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"0.1",min:"0.2",max:"15",disabled:!this.userIsAdmin},model:{value:t.form.resources.requestCPU,callback:function(e){t.$set(t.form.resources,"requestCPU",t._n(e))},expression:"form.resources.requestCPU"}},[n("template",{slot:"prepend"},[t._v("CPU")])],2)],1),t._v(" "),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"128",min:"128",max:"30720",disabled:!this.userIsAdmin},model:{value:t.form.resources.requestMemory,callback:function(e){t.$set(t.form.resources,"requestMemory",t._n(e))},expression:"form.resources.requestMemory"}},[n("template",{slot:"prepend"},[t._v("内存")]),t._v(" "),n("template",{slot:"append"},[t._v("MB")])],2)],1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                CPU：分配给单个实例的最低保障CPU "),n("br"),t._v("\n                内存：分配给单个实例的最低保障内存\n              ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1)],1),t._v(" "),n("el-form-item",{staticStyle:{"margin-top":"-22px"}},[n("div",{staticStyle:{color:"#dc5907"}},[n("b",[t._v("提示：")]),t._v("您可以降低资源配置（CPU/内存/副本数）。如需上调配置，请走审批进行申请（管理员无此限制）。具体请参考\n          "),n("a",{staticClass:"el-link",staticStyle:{color:"#409EFF"},attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=353404592"}},[t._v("操作文档")])])]),t._v(" "),n("el-form-item",{attrs:{label:"部署模块"}},[n("el-table",{attrs:{data:t.form.appModules,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[n("template",{slot:"empty"},[n("span",{staticStyle:{color:"#F56C6C"}},[t._v("请添加部署模块")])]),t._v(" "),n("el-table-column",{attrs:{label:"Git地址","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n              "+t._s(e.row.gitUrl)+"\n            ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"子模块"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n              "+t._s(e.row.module)+"\n            ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"ContextPath"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n              "+t._s(e.row.contextPath)+"\n            ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"60px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(n){return t.deleteAppModule(e.row.gitUrl,e.row.module)}}},[n("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("删除")])],1)]}}])})],2),t._v(" "),n("div",{staticStyle:{"padding-top":"10px","text-align":"center"}},[n("el-button",{attrs:{icon:"el-icon-plus",size:"small"},on:{click:function(e){t.dialogAppModuleVisible=!0}}},[t._v("添加模块")])],1)],1),t._v(" "),n("el-form-item",{attrs:{label:"JVM参数"}},[n("el-input",{staticStyle:{"word-break":"break-all"},attrs:{type:"textarea",rows:3},model:{value:t.form.jvmOpts,callback:function(e){t.$set(t.form,"jvmOpts",e)},expression:"form.jvmOpts"}}),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[n("p",{staticStyle:{"font-weight":"bold"}},[t._v("Java进程参数")]),t._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0","font-weight":"bold"}},[t._v("● 内部参数说明")]),t._v(" "),n("div",[t._v("\n                -Dprocess.profile.candidates：配置中心的候选配置组，用户跨配置组加载配置文件。详情参考配置中心文档"),n("br"),t._v("\n                -Dserver.tomcat.max-keep-alive-requests：配置 Tomcat Connector maxKeepAliveRequests参数，默认值为500"),n("br"),t._v("\n                -Dserver.tomcat.protocol：配置 Tomcat Connector protocol 参数，默认值为org.apache.coyote.http11.Http11Nio2Protocol"),n("br"),t._v("\n                -Dserver.tomcat.max-threads: 配置 Tomcat Executor maxThreads 参数，默认值为500"),n("br")]),t._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[t._v("● OpenJDK8的默认值:")]),t._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/no-jdk/files/fs-jvm-options.sh\n              ")]),t._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[t._v("● DragonWell8的默认值:")]),t._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/ali-dragonwell8/files/fs-jvm-options.sh\n              ")]),t._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[t._v("● OpenJDK17的默认值:")]),t._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/openjdk17/files/fs-jvm-options.sh\n              ")]),t._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[t._v("● OpenJDK21的默认值:")]),t._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/openjdk21/files/fs-jvm-options.sh\n              ")])])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"关闭前回调地址",prop:"preStopWebhook"}},[n("el-input",{model:{value:t.form.preStopWebhook,callback:function(e){t.$set(t.form,"preStopWebhook",e)},expression:"form.preStopWebhook"}}),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n              Pod在关闭之前的回调的地址。localhost代表Pod本身，比如： http://localhost/fs-app/shutdown"),n("br"),t._v("\n              Pod关闭流程： 摘除HTTP/Dubbo/MQ流量 → "),n("b",[t._v("call回调地址")]),t._v(" → 等待一段时间 → 关闭\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"关闭前保留时间"}},[n("el-input-number",{attrs:{max:3600,min:10,step:10},model:{value:t.form.preStopRetainSeconds,callback:function(e){t.$set(t.form,"preStopRetainSeconds",e)},expression:"form.preStopRetainSeconds"}}),t._v(" "),n("el-tooltip",{staticStyle:{float:"unset"},attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n              Pod在关闭之前的保留时间（单位：秒）。主要用在需要对实例进行优雅关闭的场景"),n("br"),t._v("\n              Pod关闭流程： 摘除HTTP/Dubbo/MQ流量 → call回调地址 → "),n("b",[t._v("等待当前配置的时间")]),t._v(" → 关闭\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"伙伴应用"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",multiple:"",placeholder:"请选择应用"},model:{value:t.form.partnerApps,callback:function(e){t.$set(t.form,"partnerApps",e)},expression:"form.partnerApps"}},t._l(t.appOptions,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n              调度时，会尽量选择伙伴应用实例所在的宿主机\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"排斥应用"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",multiple:"",placeholder:"请选择应用"},model:{value:t.form.exclusiveApps,callback:function(e){t.$set(t.form,"exclusiveApps",e)},expression:"form.exclusiveApps"}},t._l(t.appOptions,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n              调度时，会尽量避开排斥应用实例所在的宿主机"),n("br"),t._v("\n              默认会添加自己，这样可以让应用的多个实例尽量放到不同的宿主机上\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"扩展容器"}},[n("el-input",{model:{value:t.form.extInitContainer,callback:function(e){t.$set(t.form,"extInitContainer",e)},expression:"form.extInitContainer"}}),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n              额外追加自定义的initContainer，输入镜像全称，多个英文逗号分割\n            ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"备注"}},[n("el-input",{model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("b",{staticClass:"box-card-title"},[t._v("一些选项开关")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[n("div",[t._v("\n                    【内核参数修改】包含："),n("br"),t._v("\n                    net.ipv4.tcp_keepalive_time=600 "),n("br"),t._v("\n                    net.ipv4.tcp_keepalive_intvl=20 "),n("br"),t._v("\n                    net.ipv4.tcp_keepalive_probes=3\n                  ")]),t._v(" "),n("div",[t._v("\n                    【只允许发布Tag】: 发版时将只能选择tag, 不能选择branch。线上业务应用建议开启该选项，当发布代码出现问题是能够更好地回滚版本\n                  ")])])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1)],1),t._v(" "),n("div",[n("el-row",{attrs:{gutter:5}},[n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:t.form.options.onlyDeployTag,callback:function(e){t.$set(t.form.options,"onlyDeployTag",e)},expression:"form.options.onlyDeployTag"}},[t._v(t._s(t._f("optionDesc")("onlyDeployTag")))]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"发布时只允许发tag，不允许发分支。线上建议开启，便于服务版本回滚"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:t.form.options.jvmGcLog,callback:function(e){t.$set(t.form.options,"jvmGcLog",e)},expression:"form.options.jvmGcLog"}},[t._v(t._s(t._f("optionDesc")("jvmGcLog")))]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"开启jvm的gc日志并保存到日志文件里"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{attrs:{disabled:""},model:{value:t.form.options.isCoreApp,callback:function(e){t.$set(t.form.options,"isCoreApp",e)},expression:"form.options.isCoreApp"}},[t._v(t._s(t._f("optionDesc")("isCoreApp")))]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"目前只做服务标记"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:t.form.options.appLogToKafka,callback:function(e){t.$set(t.form.options,"appLogToKafka",e)},expression:"form.options.appLogToKafka"}},[t._v(t._s(t._f("optionDesc")("appLogToKafka")))]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"业务日志上报到ClickHouse日志中心"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:t.form.options.buildUseRuntimeJDK,callback:function(e){t.$set(t.form.options,"buildUseRuntimeJDK",e)},expression:"form.options.buildUseRuntimeJDK"}},[t._v(t._s(t._f("optionDesc")("buildUseRuntimeJDK")))]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                    默认使用jdk8编译源码。勾选后，则使用运行环境（基础镜像）的jdk版本去编译源码\n                    "),n("div",{staticStyle:{"padding-top":"5px"}},[n("b",[t._v("jdk升级参考资料")]),n("br"),t._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://blogs.oracle.com/javamagazine/post/its-time-to-move-your-applications-to-java-17-heres-why-and-heres-how",target:"_blank"}},[t._v("\n                        It’s time to move your applications to Java 17. Here’s why—and how\n                      ")]),n("br"),t._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://javaalmanac.io/",target:"_blank"}},[t._v("\n                        The Java Version Almanac\n                      ")]),n("br")]),t._v(" "),n("div",{staticStyle:{"padding-top":"5px"}},[n("b",[t._v("Java交叉编译参考资料")]),n("br"),t._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://www.sunmoonblog.com/2018/08/27/javac-source/",target:"_blank"}},[t._v("\n                        如何使用Javac的source参数\n                      ")]),n("br"),t._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://stackoverflow.com/questions/38882080/specifying-java-version-in-maven-differences-between-properties-and-compiler-p",target:"_blank"}},[t._v("\n                        Specifying Java version in maven\n                      ")]),n("br")])])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),t._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:t.form.options.addSysctlKeepalive,callback:function(e){t.$set(t.form.options,"addSysctlKeepalive",e)},expression:"form.options.addSysctlKeepalive"}},[t._v(t._s(t._f("optionDesc")("addSysctlKeepalive")))]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                    【内核参数修改】包含："),n("br"),t._v("\n                    net.ipv4.tcp_keepalive_time=600 "),n("br"),t._v("\n                    net.ipv4.tcp_keepalive_intvl=20 "),n("br"),t._v("\n                    net.ipv4.tcp_keepalive_probes=3\n                  ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1)],1)],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[t._v("健康检查")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                在Kubernetes中，健康检查是确保应用程序正常运行的关键。通过不同类型的健康检查，您可以有效地管理应用程序的启动、运行和可用性\n              ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2),t._v(" "),n("div",{staticStyle:{display:"inline-block","margin-left":"10px","font-size":"12px"}},[n("span",{staticStyle:{color:"#666"}},[t._v(" （如果没有特殊需求，保持系统默认配置即可） ")]),t._v(" "),n("a",{staticStyle:{color:"#409EFF",display:"inline-block","margin-left":"10px"},attrs:{href:"https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/",target:"_blank"}},[t._v("\n              查看k8s官方文档\n            ")])])],1),t._v(" "),n("div",[n("el-row",[n("el-col",{staticStyle:{padding:"0 5px"},attrs:{span:8}},[n("el-card",{staticClass:"health-check-card",attrs:{shadow:"never"}},[n("div",{staticClass:"clearfix",staticStyle:{padding:"5px"},attrs:{slot:"header"},slot:"header"},[n("span",[t._v("就绪检查")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"pod运行期间，定期检查pod是否处于健康的运行状态。如果检查失败时，则k8s会将pod从服务负载均衡中剔除(摘除HTTP流量），直到pod再次就绪。建议配置该检查。"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1),t._v(" "),n("div",{staticStyle:{float:"right"}},[n("span",{staticStyle:{"font-size":"14px"}},[t._v("启用")]),t._v(" "),n("el-switch",{model:{value:t.form.readinessProbe&&t.form.readinessProbe.enable,callback:function(e){t.$set(t.form.readinessProbe&&t.form.readinessProbe,"enable",e)},expression:"form.readinessProbe && form.readinessProbe.enable"}})],1)],1),t._v(" "),n("div",[n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"10",max:"300"},model:{value:t.form.readinessProbe.initialDelaySeconds,callback:function(e){t.$set(t.form.readinessProbe,"initialDelaySeconds",t._n(e))},expression:"form.readinessProbe.initialDelaySeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("初始延迟")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"20",max:"300"},model:{value:t.form.readinessProbe.periodSeconds,callback:function(e){t.$set(t.form.readinessProbe,"periodSeconds",t._n(e))},expression:"form.readinessProbe.periodSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("检查间隔")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"30"},model:{value:t.form.readinessProbe.timeoutSeconds,callback:function(e){t.$set(t.form.readinessProbe,"timeoutSeconds",t._n(e))},expression:"form.readinessProbe.timeoutSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("超时时间")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"60"},model:{value:t.form.readinessProbe.failureThreshold,callback:function(e){t.$set(t.form.readinessProbe,"failureThreshold",t._n(e))},expression:"form.readinessProbe.failureThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("失败阈值")])]),t._v(" "),n("template",{slot:"append"},[t._v("次")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"2",disabled:""},model:{value:t.form.readinessProbe.successThreshold,callback:function(e){t.$set(t.form.readinessProbe,"successThreshold",t._n(e))},expression:"form.readinessProbe.successThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("成功阈值")])]),t._v(" "),n("template",{slot:"append"},[t._v("次")])],2)],1)])],1),t._v(" "),n("el-col",{staticStyle:{padding:"0 5px"},attrs:{span:8}},[n("el-card",{staticClass:"health-check-card",attrs:{shadow:"never"}},[n("div",{staticClass:"clearfix",staticStyle:{padding:"5px"},attrs:{slot:"header"},slot:"header"},[n("span",[t._v("存活检查")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"pod运行期间，定期检查pod是否处于健康的运行状态。如果检查失败，则k8s会对pod进行重启"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1),t._v(" "),n("div",{staticStyle:{float:"right"}},[n("span",{staticStyle:{"font-size":"14px"}},[t._v("启用")]),t._v(" "),n("el-switch",{model:{value:t.form.livenessProbe&&t.form.livenessProbe.enable,callback:function(e){t.$set(t.form.livenessProbe&&t.form.livenessProbe,"enable",e)},expression:"form.livenessProbe && form.livenessProbe.enable"}})],1)],1),t._v(" "),n("div",[n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1200",max:"3600"},model:{value:t.form.livenessProbe.initialDelaySeconds,callback:function(e){t.$set(t.form.livenessProbe,"initialDelaySeconds",t._n(e))},expression:"form.livenessProbe.initialDelaySeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("初始延迟")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"20",max:"300"},model:{value:t.form.livenessProbe.periodSeconds,callback:function(e){t.$set(t.form.livenessProbe,"periodSeconds",t._n(e))},expression:"form.livenessProbe.periodSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("检查间隔")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"30"},model:{value:t.form.livenessProbe.timeoutSeconds,callback:function(e){t.$set(t.form.livenessProbe,"timeoutSeconds",t._n(e))},expression:"form.livenessProbe.timeoutSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("超时时间")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"60"},model:{value:t.form.livenessProbe.failureThreshold,callback:function(e){t.$set(t.form.livenessProbe,"failureThreshold",t._n(e))},expression:"form.livenessProbe.failureThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("失败阈值")])]),t._v(" "),n("template",{slot:"append"},[t._v("次")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"5",disabled:""},model:{value:t.form.livenessProbe.successThreshold,callback:function(e){t.$set(t.form.livenessProbe,"successThreshold",t._n(e))},expression:"form.livenessProbe.successThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("成功阈值")])]),t._v(" "),n("template",{slot:"append"},[t._v("次")])],2)],1)])],1),t._v(" "),n("el-col",{staticStyle:{padding:"0 5px"},attrs:{span:8}},[n("el-card",{staticClass:"health-check-card",attrs:{shadow:"never"}},[n("div",{staticClass:"clearfix",staticStyle:{padding:"5px"},attrs:{slot:"header"},slot:"header"},[n("span",[t._v("启动检查")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"在pod启动时执行，检查pod是否已准备好接收流量(tomcat是否已经完成启动）。容器在尝试其他健康检查之前，将等待此检查成功。"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1),t._v(" "),n("div",{staticStyle:{float:"right"}},[n("span",{staticStyle:{"font-size":"14px"}},[t._v("启用")]),t._v(" "),n("el-switch",{model:{value:t.form.startupProbe&&t.form.startupProbe.enable,callback:function(e){t.$set(t.form.startupProbe&&t.form.startupProbe,"enable",e)},expression:"form.startupProbe && form.startupProbe.enable"}})],1)],1),t._v(" "),n("div",[n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"10",max:"60"},model:{value:t.form.startupProbe.initialDelaySeconds,callback:function(e){t.$set(t.form.startupProbe,"initialDelaySeconds",t._n(e))},expression:"form.startupProbe.initialDelaySeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("初始延迟")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"5",max:"30"},model:{value:t.form.startupProbe.periodSeconds,callback:function(e){t.$set(t.form.startupProbe,"periodSeconds",t._n(e))},expression:"form.startupProbe.periodSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("检查间隔")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"30"},model:{value:t.form.startupProbe.timeoutSeconds,callback:function(e){t.$set(t.form.startupProbe,"timeoutSeconds",t._n(e))},expression:"form.startupProbe.timeoutSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("超时时间")])]),t._v(" "),n("template",{slot:"append"},[t._v("秒")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"300",max:"1000"},model:{value:t.form.startupProbe.failureThreshold,callback:function(e){t.$set(t.form.startupProbe,"failureThreshold",t._n(e))},expression:"form.startupProbe.failureThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("失败阈值")])]),t._v(" "),n("template",{slot:"append"},[t._v("次")])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"5",disabled:""},model:{value:t.form.startupProbe.successThreshold,callback:function(e){t.$set(t.form.startupProbe,"successThreshold",t._n(e))},expression:"form.startupProbe.successThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[t._v("成功阈值")])]),t._v(" "),n("template",{slot:"append"},[t._v("次")])],2)],1)])],1)],1)],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[t._v("资源池调度")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                调度策略：服务实例在调度时选择资源池的策略\n                资源池：服务实例运行的宿主机集合\n              ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),t._v(" "),n("div",[n("el-form-item",{attrs:{label:"调度策略：","label-width":"100"}},[n("el-select",{staticStyle:{width:"600px"},model:{value:t.form.schedule.strategy,callback:function(e){t.$set(t.form.schedule,"strategy",e)},expression:"form.schedule.strategy"}},t._l(t.scheduleStrategyOptions,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                  【必须调度到选择的资源池】: 当选择的资源池资源不足时会调度失败，这时需要联系管理员对资源池做扩容 "),n("br"),t._v("\n                  【优先调度到选择的资源池】: 当选择的资源池资源不足时会把实例调度到【通用】宿主机上运行\n                ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"资源池：","label-width":"100"}},t._l(t.nodeOptions,(function(e){return n("div",{staticStyle:{"padding-left":"85px"}},[n("el-radio",{attrs:{label:e.value},model:{value:t.form.schedule.node,callback:function(e){t.$set(t.form.schedule,"node","string"===typeof e?e.trim():e)},expression:"form.schedule.node"}},[t._v(t._s(e.name))])],1)})),0)],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[t._v("持久存储")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[t._v("\n                    给应用容器挂载磁盘，该磁盘下的文件不会随着应用的重新发版或重启而删除掉。一般在以下场景才需要开启持久存储"),n("br"),t._v("\n                    1. 应用需要比较大的存储空间"),n("br"),t._v("\n                    2. 应用需要永久存储一些过程中产生的文件(重新发版/重启应用实例不会对这些文件进行删除)"),n("br"),t._v("\n                    3. 应用的多个实例需要共享读写一个存储盘\n                  ")])]),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),t._v(" "),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[n("el-switch",{attrs:{"active-text":"使用","inactive-text":"未使用"},model:{value:t.form.pvc&&t.form.pvc.enable,callback:function(e){t.$set(t.form.pvc&&t.form.pvc,"enable",e)},expression:"form.pvc && form.pvc.enable"}})],1)],1)],1),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.form.pvc&&t.form.pvc.enable,expression:"form.pvc && form.pvc.enable"}]},[n("el-input",{staticClass:"input-list-item",model:{value:t.form.pvc.name,callback:function(e){t.$set(t.form.pvc,"name","string"===typeof e?e.trim():e)},expression:"form.pvc.name"}},[n("template",{slot:"prepend"},[n("div",{staticStyle:{width:"80px"}},[t._v("PVC名称")])])],2),t._v(" "),n("el-input",{staticClass:"input-list-item",model:{value:t.form.pvc.mountPath,callback:function(e){t.$set(t.form.pvc,"mountPath","string"===typeof e?e.trim():e)},expression:"form.pvc.mountPath"}},[n("template",{slot:"prepend"},[n("div",{staticStyle:{width:"80px"}},[t._v("挂载目录路径")])])],2)],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[t._v("性能监控")]),t._v(" "),t.currentCluster&&t.currentCluster.apm.enable&&t.currentCluster.apm.skyWalkingUI?n("a",{staticStyle:{"padding-left":"10px",color:"#409EFF","font-size":"14px"},attrs:{href:t.currentCluster.apm.skyWalkingUI,target:"_blank"}},[t._v("UI页面")]):t._e()]),t._v(" "),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[t.currentCluster&&t.currentCluster.apm.enable?n("div",[n("el-switch",{attrs:{"active-text":"开启","inactive-text":"关闭"},model:{value:t.form.options.skyWalkingAgent,callback:function(e){t.$set(t.form.options,"skyWalkingAgent",e)},expression:"form.options.skyWalkingAgent"}})],1):n("div",[n("small",{staticStyle:{"padding-left":"10px",color:"#777"}},[t._v("（暂未开放）")])])])],1)],1),t._v(" "),n("div",{staticStyle:{"font-size":"14px",color:"#777"}},[t._v("\n          性能监控管理(APM)：使用Skywalking搭建，开启后会使用Skywalking Agent来收集应用的一些性能指标信息（如：请求耗时，分布式追踪等）"),n("br")])]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[t._v("接口测试（Eolinker）")])])],1)],1),t._v(" "),n("div",[n("div",{staticStyle:{"font-size":"14px",color:"#777","padding-bottom":"10px"}},[t._v("\n            可以选择一个Eolinker任务，那么应用发布成功后会调用执行该任务进行测试，格式： 项目名 / 任务名\n          ")]),t._v(" "),n("el-cascader",{key:t.eolinkerFlushCascader,staticStyle:{width:"100%"},attrs:{clearable:"",options:t.eolinkerOptions,props:t.eolinkerProps,separator:" / "},model:{value:t.eolinkerModel,callback:function(e){t.eolinkerModel=e},expression:"eolinkerModel"}})],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[t._v("发布后回调（Webhook）")])]),t._v(" "),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}})],1)],1),t._v(" "),n("div",[n("div",{staticStyle:{"font-size":"14px",color:"#777","padding-bottom":"10px"}},[t._v("\n            发布成功后，会调用一次Webhook地址\n          ")]),t._v(" "),n("el-input",{staticClass:"input-list-item",model:{value:t.form.webhook.url,callback:function(e){t.$set(t.form.webhook,"url","string"===typeof e?e.trim():e)},expression:"form.webhook.url"}},[n("template",{slot:"prepend"},[n("div",{staticStyle:{width:"80px"}},[t._v("URL")])])],2)],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[t._v("环境变量")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"系统环境变量，发布系统维护的变量不允许修改"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",[t._l(t.form.envs,(function(e){return n("div",{staticStyle:{margin:"5px 0"}},[n("el-input",{staticStyle:{display:"inline-block",width:"260px"},attrs:{placeholder:"变量名",clearable:"",disabled:"SYSTEM"===e.type},model:{value:e.name,callback:function(n){t.$set(e,"name","string"===typeof n?n.trim():n)},expression:"item.name"}}),t._v("\n            =\n            "),n("el-input",{staticStyle:{display:"inline-block",width:"400px"},attrs:{disabled:"SYSTEM"===e.type},model:{value:e.value,callback:function(n){t.$set(e,"value","string"===typeof n?n.trim():n)},expression:"item.value"}}),t._v(" "),n("el-button",{directives:[{name:"show",rawName:"v-show",value:"SYSTEM"!==e.type,expression:"item.type !== 'SYSTEM'"}],attrs:{icon:"el-icon-delete"},on:{click:function(n){return n.preventDefault(),t.removeEnv(e)}}},[t._v("删除\n            ")])],1)})),t._v(" "),n("div",{staticStyle:{"padding-top":"10px","padding-left":"310px"}},[n("el-button",{attrs:{icon:"el-icon-plus"},on:{click:t.addEnv}},[t._v("添加变量")]),t._v(" "),n("br"),t._v(" "),n("el-popover",{attrs:{placement:"top",title:"一些特殊用途的环境变量",width:"600",trigger:"click"}},[n("div",[n("div",[t._v("JEMALLOC_ENABLE=true")]),t._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("启用jemalloc内存分配器")]),t._v(" "),n("el-divider",{staticClass:"env-divider"}),t._v(" "),n("div",[t._v("SPRING_BOOT_JAR_APP=true")]),t._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("如果需要使用Jar包类型的SpringBoot应用，请开启这个变量")]),t._v(" "),n("el-divider",{staticClass:"env-divider"}),t._v(" "),n("div",[t._v("CMS_STARTER_PRIVATE_KEY=[字符串内容]")]),t._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("使用自定义的秘钥对配置中心内容加解密")]),t._v(" "),n("el-divider",{staticClass:"env-divider"}),t._v(" "),n("div",[t._v("SPRING_BOOT_METRICS_ENABLE=true")]),t._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("开启Prometheus PodMonitor")]),t._v(" "),n("el-divider",{staticClass:"env-divider"}),t._v(" "),n("div",[t._v("SPRING_PROFILER_AGENT_ENABLE=true")]),t._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[t._v("开启应用启动时间分析，详情请查阅spring-startup-analyzer资料")])],1),t._v(" "),n("el-button",{attrs:{slot:"reference",icon:"el-icon-search",type:"text"},slot:"reference"},[t._v("查看特殊环境变量")])],1)],1)],2)]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[t._v("端口")]),t._v(" "),n("el-tooltip",{attrs:{effect:t.tooltip.effect,placement:t.tooltip.placement,content:"应用需要暴露的端口，如FCP服务可添加：fcp = 5432 。发布系统维护的端口不允许修改"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",[t._l(t.form.ports,(function(e){return n("div",{staticStyle:{margin:"5px 0"}},[n("el-input",{staticStyle:{display:"inline-block",width:"260px"},attrs:{placeholder:"端口名",clearable:"",disabled:"SYSTEM"===e.type},model:{value:e.name,callback:function(n){t.$set(e,"name","string"===typeof n?n.trim():n)},expression:"item.name"}}),t._v("\n            =\n            "),n("el-input-number",{staticStyle:{display:"inline-block",width:"260px"},attrs:{placeholder:"端口值","controls-position":"right",min:0,max:65535,step:1e3,disabled:"SYSTEM"===e.type},model:{value:e.value,callback:function(n){t.$set(e,"value",n)},expression:"item.value"}}),t._v(" "),n("el-button",{directives:[{name:"show",rawName:"v-show",value:"SYSTEM"!==e.type,expression:"item.type !== 'SYSTEM'"}],attrs:{icon:"el-icon-delete"},on:{click:function(n){return n.preventDefault(),t.removePort(e)}}},[t._v("删除\n            ")])],1)})),t._v(" "),n("div",{staticStyle:{"padding-top":"10px","padding-left":"310px"}},[n("el-button",{attrs:{icon:"el-icon-plus"},on:{click:t.addPort}},[t._v("添加端口")])],1)],2)]),t._v(" "),n("div",{staticStyle:{padding:"20px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("div",[n("el-button",{attrs:{type:"warning"},on:{click:t.onAbort}},[t._v("取消")])],1)]),t._v(" "),n("el-col",{attrs:{span:12}},[n("div",{staticStyle:{"text-align":"right"}},[n("el-button",{staticStyle:{"padding-right":"30px","padding-left":"30px"},attrs:{type:"primary",loading:t.submitLoading},on:{click:function(e){return t.onSubmit("form")}}},[t._v("提交\n              ")])],1)])],1)],1)],1),t._v(" "),n("el-dialog",{attrs:{title:"添加部署模块",visible:t.dialogAppModuleVisible,width:"840px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogAppModuleVisible=e}}},[n("el-form",{attrs:{model:t.dialogAppModuleForm,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"部署模块"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择部署模块。格式：Git地址 --- 子模块"},on:{change:t.deployModuleSelChanged},model:{value:t.dialogAppModuleForm.module,callback:function(e){t.$set(t.dialogAppModuleForm,"module",e)},expression:"dialogAppModuleForm.module"}},t._l(t.artifactOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.gitUrl+" --- "+t.module,value:t.gitUrl+"@@"+t.module}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"ContextPath"}},[n("el-input",{model:{value:t.dialogAppModuleForm.contextPath,callback:function(e){t.$set(t.dialogAppModuleForm,"contextPath","string"===typeof e?e.trim():e)},expression:"dialogAppModuleForm.contextPath"}})],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogAppModuleVisible=!1}}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addAppModule()}}},[t._v("确 定")])],1)],1)],1)])},a=[],s=(n("6b54"),n("28a5"),n("a481"),n("20d6"),n("96cf"),n("3b8d")),r=(n("f559"),n("7f7f"),n("2d63")),i=n("51a9"),l=n("530d"),c=n("b562"),p=n("b775");function u(){return Object(p["a"])({url:"/v1/eolinker/project",method:"get"})}function d(t){return Object(p["a"])({url:"/v1/eolinker/timed-task",method:"get",params:t})}var f=n("c24f"),m=n("b144"),v={data:function(){var t=this,e=function(t,e,n){var o,a=Object(r["a"])(e);try{for(a.s();!(o=a.n()).done;){var s=o.value;if(console.log(s),!s.name||!s.value)return void n(new Error("请输入所有端口的名称和值"))}}catch(i){a.e(i)}finally{a.f()}n()},n=function(t,e,n){var o,a=Object(r["a"])(e);try{for(a.s();!(o=a.n()).done;){var s=o.value;if(console.log(s),!s.name)return void n(new Error("部分环境的变量名未填写"))}}catch(i){a.e(i)}finally{a.f()}n()},o=function(t,e,n){!e||e.startsWith("http://")||e.startsWith("https://")?n():n(new Error("Pod关闭前回调地址必须使用 http:// 或者 https:// 开头"))};return{loading:!0,submitLoading:!1,userIsAdmin:!1,appOptions:[],artifactOptions:[],artifactSelected:{},tooltip:{effect:"light",placement:"right-start"},formOrigin:{},form:{appModules:[],resources:{},livenessProbe:{},readinessProbe:{},startupProbe:{},schedule:{},pvc:{},envs:[],ports:[],options:{},eolinkerIDs:[],partnerApps:[],exclusiveApps:[],webhook:{},preStopWebhook:"",preStopRetainSeconds:20},rules:{app:[{required:!0,message:"请输入应用名称"},{min:4,message:"最少4个字符"}],cluster:[{required:!0,message:"请选择集群"}],namespace:[{required:!0,message:"请选择运行环境"}],baseImage:[{required:!0,message:"请选择基础镜像"}],envs:[{validator:n,message:"部分环境的变量名未填写"}],ports:[{validator:e,message:"请输入所有端口的名称和值"}],preStopWebhook:[{validator:o,message:"Pod关闭前回调地址必须使用 http:// 或 https:// 开头"}]},dialogAppModuleVisible:!1,dialogAppModuleForm:{module:"",contextPath:"/"},eolinkerProps:{multiple:!0,lazy:!0,lazyLoad:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(n,o){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=n.level,e.t0=a,e.next=0===e.t0?4:1===e.t0?5:11;break;case 4:return e.abrupt("break",13);case 5:return e.t1=o,e.next=8,t.listEolinkerTimedTask(n.data.value);case 8:return e.t2=e.sent,(0,e.t1)(e.t2),e.abrupt("break",13);case 11:return o([]),e.abrupt("return");case 13:case"end":return e.stop()}}),e)})));function n(t,n){return e.apply(this,arguments)}return n}()},eolinkerOptions:[],eolinkerFlushCascader:Math.random()}},filters:{optionDesc:function(t,e){return Object(m["b"])(t)}},mounted:function(){var t=this,e=this.$route.query.app,n=this.$route.query.pipelineId;n?this.loadPipeline(n):this.initPipeline(e),this.loadArtifacts(),this.loadApps(),Object(f["b"])().then((function(e){t.userIsAdmin=e.data})).catch((function(t){console.log(t)}))},computed:{isEdit:function(){return this.form.id>0},isClone:function(){return"clone"===this.$route.query.operate},isAudit:function(){return"audit"===this.$route.query.operate},clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.form.cluster){var t,e=Object(r["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var n=t.value;if(this.form.cluster===n.name)return n.namespaces}}catch(o){e.e(o)}finally{e.f()}}return[]},currentCluster:function(){var t=this.form.cluster;if(t){var e,n=Object(r["a"])(this.$settings.clusters);try{for(n.s();!(e=n.n()).done;){var o=e.value;if(o.name===t)return o}}catch(a){n.e(a)}finally{n.f()}}},nodeOptions:function(){if(this.form.cluster)for(var t in this.$settings.clusters){var e=this.$settings.clusters[t];if(this.form.cluster===e.name)return e.nodes}return[]},scheduleStrategyOptions:function(){return[{value:"PREFERRED",label:"优先调度到选择的资源池"},{value:"REQUIRED",label:"必须调度到选择的资源池"}]},baseImageOptions:function(){var t=this.currentCluster;return t?t.baseImages:[]},armBaseImageOptions:function(){return this.$settings.armBaseImages},deployStrategyOptions:function(){return this.$settings.deployStrategies},eolinkerModel:{get:function(){for(var t=[],e=0;e<this.form.eolinkerIDs.length;e++){var n=[];n.push(this.form.eolinkerIDs[e].projectID),n.push(this.form.eolinkerIDs[e].timedTaskID),t.push(n)}return t},set:function(t){for(var e=[],n=0;n<t.length;n++)t[n]&&2===t[n].length&&e.push({projectID:t[n][0],timedTaskID:t[n][1]});this.form.eolinkerIDs=e}}},methods:{loadApps:function(){var t=this;Object(c["l"])().then((function(e){t.appOptions=e.data})).catch((function(e){t.$message.warning("加载应用数据出错！ "+e.message)}))},loadPipeline:function(t){var e=this;Object(i["e"])(t).then((function(t){if(e.form=t.data,e.formOrigin=Object(m["a"])(e.form),e.isAudit)console.log("pipeline audit, set status to enabled"),e.form.status="enabled";else if(e.isClone){console.log("pipeline clone, set cluster and namespace");var n=e.form.namespace;e.form.id=0,e.form.status="audit",e.form.cluster=e.$route.query.targetCluster,e.form.namespace=e.$route.query.targetNamespace,0===e.nodeOptions.filter((function(t){return t.value===e.form.schedule.node})).length&&(e.form.schedule.node=""),n!==e.form.namespace&&e.namespaceChange(e.form.namespace)}e.loadEolinkerOptions()})).catch((function(t){e.$message.error("加载数据出错！ "+t.message)})).finally((function(){e.loading=!1}))},loadArtifacts:function(){var t=this;Object(l["d"])().then((function(e){t.artifactOptions=e.data})).catch((function(e){t.$message.error("加载数据出错！ "+e.message)}))},initPipeline:function(t){var e=this;console.log(t),Object(i["i"])(t).then((function(t){e.form=t.data,e.loadEolinkerOptions()})).catch((function(t){e.$message.error("加载数据出错！ "+t.message)})).finally((function(){e.loading=!1}))},deleteAppModule:function(t,e){var n=this.form.appModules;n.splice(n.findIndex((function(n){return n.gitUrl===t&&n.module===e})),1)},clusterChange:function(){this.form.namespace="",this.form.baseImage=""},namespaceChange:function(t){if(!(t.indexOf("-")<1)){this.form.jvmOpts=this.form.jvmOpts.replace(/-Dprocess.profile.candidates=\S+/gi,"").trim();var e="";e=t.startsWith("fstest-")?"fstest":t.startsWith("firstshare-")?"firstshare":t.startsWith("foneshare-")||t.startsWith("foneshare")&&"foneshare"!==t?"foneshare":"cloud,foneshare",this.form.jvmOpts=(this.form.jvmOpts+" -Dprocess.profile.candidates="+e).trim()}},replicaChange:function(t,e){var n=this;!this.userIsAdmin&&t>this.formOrigin.replicas&&(this.$message.warning("如果需要增加实例数，请发审批"),this.$nextTick((function(){n.form.replicas=e})))},limitCPUChange:function(t){if(!this.userIsAdmin&&t>this.formOrigin.resources.limitCPU)return this.$message.warning("如果需要增加CPU，请发审批"),void(this.form.resources.limitCPU=this.formOrigin.resources.limitCPU);var e=parseFloat((t/4).toFixed(1));this.form.resources.requestCPU=e<=.1?.1:e},limitMemChange:function(t){if(!this.userIsAdmin&&t>this.formOrigin.resources.limitMemory)return this.$message.warning("如果需要增加内存，请发审批"),void(this.form.resources.limitMemory=this.formOrigin.resources.limitMemory);var e=parseInt(t);this.form.resources.requestMemory=e<64?64:e},deployModuleSelChanged:function(){var t=this.dialogAppModuleForm,e=t.module.split("@@");2===e.length&&(t.contextPath="/"+e[1])},addAppModule:function(){var t=this.dialogAppModuleForm;if(t.module&&t.contextPath)if(t.contextPath.startsWith("/")){var e=t.module.split("@@");if(2!==e.length)return this.$message.error("无法解析模块数据"),!1;var n={gitUrl:e[0],module:e[1],contextPath:t.contextPath};this.form.appModules.filter((function(t){return t.gitUrl===n.gitUrl&&t.module===n.module})).length>0?this.$message.error("模块已经存在，不能重复添加"):this.form.appModules.filter((function(t){return t.contextPath===n.contextPath})).length>0?this.$message.error("ContextPath 出现重复"):(this.form.appModules.push(n),this.dialogAppModuleVisible=!1)}else this.$message.error("ContextPath 必须以斜线开头");else this.$message.error("请填写完整数据")},onSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(t)if(!e.form.appModules||e.form.appModules.length<1)e.$message.error("没有配置部署模块");else{var n=e.form.webhook.url;!n||n.startsWith("http://")||n.startsWith("https://")?e.$confirm("是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitLoading=!0,Object(i["j"])(e.form).then((function(t){e.$message.success("操作成功"),e.pipelinePage(),e.submitLoading=!1})).catch((function(t){e.$message({dangerouslyUseHTMLString:!0,message:t.message,type:"error"}),e.submitLoading=!1}))})):e.$message.error("WebHook Url地址必须以 http:// 或者 https:// 开头")}}))},onAbort:function(){var t=this;this.$confirm("确认取消吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pipelinePage()}))},pipelinePage:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.form.app}})},addPort:function(){this.form.ports.push({name:"",value:0,type:"USER"})},removePort:function(t){if("SYSTEM"!==t.type){var e=this.form.ports.indexOf(t);-1!==e&&this.form.ports.splice(e,1)}else this.$message.error("不允许删除该端口")},addEnv:function(){this.form.envs.push({name:"",value:"",type:"USER"})},removeEnv:function(t){if("SYSTEM"!==t.type){var e=this.form.envs.indexOf(t);-1!==e&&this.form.envs.splice(e,1)}else this.$message.error("不允许删除该变量")},loadEolinkerOptions:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,n,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.listEolinkerProject();case 2:this.eolinkerOptions=t.sent,e=0;case 4:if(!(e<this.form.eolinkerIDs.length)){t.next=20;break}if(n=this.form.eolinkerIDs[e].projectID,!n){t.next=17;break}o=0;case 8:if(!(o<this.eolinkerOptions.length)){t.next=16;break}if(this.eolinkerOptions[o].value!==n||this.eolinkerOptions[o].children){t.next=13;break}return t.next=12,this.listEolinkerTimedTask(n);case 12:this.eolinkerOptions[o].children=t.sent;case 13:o++,t.next=8;break;case 16:this.eolinkerFlushCascader=Math.random();case 17:e++,t.next=4;break;case 20:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),listEolinkerProject:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,n=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=[],t.next=3,u().then((function(t){e=t.data.map((function(t,e){return{value:t.project_id,label:t.project_name}}))})).catch((function(t){n.$message.error("加载测试接口（Eolinker）数据出错！ "+t.message)}));case 3:return t.abrupt("return",e);case 4:case"end":return t.stop()}}),t)})));function e(){return t.apply(this,arguments)}return e}(),listEolinkerTimedTask:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(e){var n,o,a,s=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=this.eolinkerOptions.filter((function(t){return t.value===e})),!n||!n[0].children){t.next=3;break}return t.abrupt("return");case 3:return o={},a=[],o["project_id"]=e,t.next=8,d(o).then((function(t){a=t.data.map((function(t,e){return{value:t.task_id.toString(),label:t.task_name,leaf:!0}}))})).catch((function(t){s.$message.error("加载测试接口（Eolinker）数据出错！ "+t.message)}));case 8:return t.abrupt("return",a);case 9:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()}},h=v,b=(n("fbd2"),n("2877")),g=Object(b["a"])(h,o,a,!1,null,null,null);e["default"]=g.exports},a481:function(t,e,n){"use strict";var o=n("cb7c"),a=n("4bf8"),s=n("9def"),r=n("4588"),i=n("0390"),l=n("5f1b"),c=Math.max,p=Math.min,u=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,f=/\$([$&`']|\d\d?)/g,m=function(t){return void 0===t?t:String(t)};n("214f")("replace",2,(function(t,e,n,v){return[function(o,a){var s=t(this),r=void 0==o?void 0:o[e];return void 0!==r?r.call(o,s,a):n.call(String(s),o,a)},function(t,e){var a=v(n,t,this,e);if(a.done)return a.value;var u=o(t),d=String(this),f="function"===typeof e;f||(e=String(e));var b=u.global;if(b){var g=u.unicode;u.lastIndex=0}var _=[];while(1){var x=l(u,d);if(null===x)break;if(_.push(x),!b)break;var y=String(x[0]);""===y&&(u.lastIndex=i(d,s(u.lastIndex),g))}for(var k="",S=0,C=0;C<_.length;C++){x=_[C];for(var w=String(x[0]),P=c(p(r(x.index),d.length),0),O=[],$=1;$<x.length;$++)O.push(m(x[$]));var j=x.groups;if(f){var q=[w].concat(O,P,d);void 0!==j&&q.push(j);var E=String(e.apply(void 0,q))}else E=h(w,d,P,O,j,e);P>=S&&(k+=d.slice(S,P)+E,S=P+w.length)}return k+d.slice(S)}];function h(t,e,o,s,r,i){var l=o+t.length,c=s.length,p=f;return void 0!==r&&(r=a(r),p=d),n.call(i,p,(function(n,a){var i;switch(a.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,o);case"'":return e.slice(l);case"<":i=r[a.slice(1,-1)];break;default:var p=+a;if(0===p)return n;if(p>c){var d=u(p/10);return 0===d?n:d<=c?void 0===s[d-1]?a.charAt(1):s[d-1]+a.charAt(1):n}i=s[p-1]}return void 0===i?"":i}))}}))},aae3:function(t,e,n){var o=n("d3f4"),a=n("2d95"),s=n("2b4c")("match");t.exports=function(t){var e;return o(t)&&(void 0!==(e=t[s])?!!e:"RegExp"==a(t))}},b0c5:function(t,e,n){"use strict";var o=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:o!==/./.exec},{exec:o})},b144:function(t,e,n){"use strict";function o(t){return JSON.parse(JSON.stringify(t))}function a(t){if(!t||!(t instanceof Date))return"";var e=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();return e}function s(t){return"isCoreApp"===t?"核心服务":"onlyDeployTag"===t?"只允许部署Tag":"addSysctlKeepalive"===t?"调整内核参数":"skyWalkingAgent"===t?"性能跟踪":"appLogToKafka"===t?"接入ClickHouse日志":"buildUseRuntimeJDK"===t?"镜像JDK版本编译代码":"jvmGcLog"===t?"GC日志":t}n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"b",(function(){return s}))},b562:function(t,e,n){"use strict";n.d(e,"p",(function(){return a})),n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return r})),n.d(e,"l",(function(){return i})),n.d(e,"j",(function(){return l})),n.d(e,"d",(function(){return c})),n.d(e,"i",(function(){return p})),n.d(e,"h",(function(){return u})),n.d(e,"m",(function(){return d})),n.d(e,"o",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"e",(function(){return v})),n.d(e,"c",(function(){return h})),n.d(e,"k",(function(){return b})),n.d(e,"q",(function(){return g})),n.d(e,"n",(function(){return _})),n.d(e,"g",(function(){return x}));var o=n("b775");function a(t){return Object(o["a"])({url:"/v1/app/search",method:"get",params:t})}function s(){return Object(o["a"])({url:"/v1/app/apps-with-env",method:"get"})}function r(){return Object(o["a"])({url:"/v1/app/all",method:"get"})}function i(){return Object(o["a"])({url:"/v1/app/names",method:"get"})}function l(t){return Object(o["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function c(t){return Object(o["a"])({url:"/v1/app",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/v1/app",method:"put",data:t})}function u(t){return Object(o["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(o["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function f(t){return Object(o["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(o["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function v(t){return Object(o["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function h(t){return Object(o["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function b(t,e){return Object(o["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function g(t,e){return Object(o["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(o["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function x(t){return Object(o["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:t}})}},cd1c:function(t,e,n){var o=n("e853");t.exports=function(t,e){return new(o(t))(e)}},d2c8:function(t,e,n){var o=n("aae3"),a=n("be13");t.exports=function(t,e,n){if(o(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(a(t))}},e853:function(t,e,n){var o=n("d3f4"),a=n("1169"),s=n("2b4c")("species");t.exports=function(t){var e;return a(t)&&(e=t.constructor,"function"!=typeof e||e!==Array&&!a(e.prototype)||(e=void 0),o(e)&&(e=e[s],null===e&&(e=void 0))),void 0===e?Array:e}},f559:function(t,e,n){"use strict";var o=n("5ca1"),a=n("9def"),s=n("d2c8"),r="startsWith",i=""[r];o(o.P+o.F*n("5147")(r),"String",{startsWith:function(t){var e=s(this,t,r),n=a(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),o=String(t);return i?i.call(e,o,n):e.slice(n,n+o.length)===o}})},fbd2:function(t,e,n){"use strict";n("26ef")}}]);
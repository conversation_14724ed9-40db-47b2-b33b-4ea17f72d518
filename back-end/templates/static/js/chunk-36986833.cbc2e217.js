(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-36986833"],{"3edd":function(e,a,t){"use strict";t.r(a);var p=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container"},[t("app-manage-tab",{attrs:{"active-name":"app-gc"}}),e._v("\n  todo\n")],1)},l=[],n=t("a68b"),o={components:{appManageTab:n["a"]},mounted:function(){},computed:{},data:function(){return{}},methods:{}},r=o,s=t("2877"),i=Object(s["a"])(r,p,l,!1,null,null,null);a["default"]=i.exports},a68b:function(e,a,t){"use strict";var p=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(a){e.activeTab=a},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},l=[],n=(t("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,a){var t=e.name;"app-restart"===t?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===t?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===t?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===t?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===t?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===t?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===t?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===t?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===t?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===t?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===t?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===t?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===t?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===t?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),o=n,r=(t("d54f"),t("2877")),s=Object(r["a"])(o,p,l,!1,null,null,null);a["a"]=s.exports},d54f:function(e,a,t){"use strict";t("eba1")},eba1:function(e,a,t){}}]);
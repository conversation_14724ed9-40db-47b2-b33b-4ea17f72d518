(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-24e44ee8"],{"11e9":function(e,t,a){var n=a("52a7"),r=a("4630"),l=a("6821"),i=a("6a99"),o=a("69a8"),s=a("c69a"),c=Object.getOwnPropertyDescriptor;t.f=a("9e1e")?c:function(e,t){if(e=l(e),t=i(t,!0),s)try{return c(e,t)}catch(a){}if(o(e,t))return r(!n.f.call(e,t),e[t])}},"1e42":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},r=[],l=(a("a481"),a("25ca")),i=a("21a6"),o=a.n(i),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=l["a"].table_to_book(e,{raw:!0}),a=l["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var n="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";o.a.saveAs(new Blob([a],{type:"application/octet-stream"}),n)}catch(r){this.$message.error("导出失败, err: "+r.message),console.error(r)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=a("2877"),p=Object(u["a"])(c,n,r,!1,null,null,null);t["a"]=p.exports},"2fdb":function(e,t,a){"use strict";var n=a("5ca1"),r=a("d2c8"),l="includes";n(n.P+n.F*a("5147")(l),"String",{includes:function(e){return!!~r(this,e,l).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},"4f7f":function(e,t,a){"use strict";var n=a("c26b"),r=a("b39a"),l="Set";e.exports=a("e0b8")(l,(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return n.def(r(this,l),e=0===e?0:e,e)}},n)},5147:function(e,t,a){var n=a("2b4c")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,!"/./"[e](t)}catch(r){}}return!0}},"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"e",(function(){return l})),a.d(t,"d",(function(){return i})),a.d(t,"l",(function(){return o})),a.d(t,"m",(function(){return s})),a.d(t,"a",(function(){return c})),a.d(t,"f",(function(){return u})),a.d(t,"i",(function(){return p})),a.d(t,"j",(function(){return f})),a.d(t,"k",(function(){return d})),a.d(t,"n",(function(){return m})),a.d(t,"g",(function(){return b})),a.d(t,"b",(function(){return h})),a.d(t,"h",(function(){return v})),a.d(t,"o",(function(){return g}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function i(e,t,a){return Object(n["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e){return Object(n["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(n["a"])({url:"/v1/pipeline/all",method:"get"})}function u(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(n["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function f(e){return Object(n["a"])({url:"/v1/pipeline",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function m(e){return Object(n["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function b(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function h(e){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function v(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function g(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"530d":function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"e",(function(){return l})),a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return s}));var n=a("b775");function r(){return Object(n["a"])({url:"/v1/artifact/all",method:"get"})}function l(e){return Object(n["a"])({url:"/v1/artifact/search",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/v1/artifact/analysis",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/v1/artifact",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/v1/artifact",method:"delete",params:{id:e}})}},"55d7":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"env-selector-wrapper"},[e.showCluster?a("el-form-item",{attrs:{label:e.clusterLabel,prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.cluster,callback:function(t){e.cluster=t},expression:"cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1):e._e(),e._v(" "),e.showNamespace?a("el-form-item",{attrs:{label:e.namespaceLabel,prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择namespace"},model:{value:e.namespace,callback:function(t){e.namespace=t},expression:"namespace"}},[e.showAllNamespaces?a("el-option",{key:"*",attrs:{label:"所有",value:""}}):e._e(),e._v(" "),e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1):e._e()],1)},r=[],l=a("2d63"),i=(a("7f7f"),{name:"EnvSelector",props:{showAllNamespaces:{type:Boolean,default:!1},showCluster:{type:Boolean,default:!0},showNamespace:{type:Boolean,default:!0},clusterLabel:{type:String,default:"k8s集群"},namespaceLabel:{type:String,default:"运行环境"}},data:function(){return{cluster:"",namespace:""}},mounted:function(){!this.cluster&&this.clusterOptions&&this.clusterOptions.length&&(this.cluster=this.clusterOptions[0].name),this.cluster&&!this.namespace&&this.namespaceOptions&&this.namespaceOptions.length&&(this.namespace=this.namespaceOptions[0])},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.cluster){var e,t=Object(l["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},methods:{clusterChange:function(){this.namespace=""}}}),o=i,s=(a("e0fe"),a("2877")),c=Object(s["a"])(o,n,r,!1,null,null,null);t["a"]=c.exports},"5dbc":function(e,t,a){var n=a("d3f4"),r=a("8b97").set;e.exports=function(e,t,a){var l,i=t.constructor;return i!==a&&"function"==typeof i&&(l=i.prototype)!==a.prototype&&n(l)&&r&&r(e,l),e}},"5df3":function(e,t,a){"use strict";var n=a("02f4")(!0);a("01f9")(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,a=this._i;return a>=t.length?{value:void 0,done:!0}:(e=n(t,a),this._i+=e.length,{value:e,done:!1})}))},6738:function(e,t,a){},6762:function(e,t,a){"use strict";var n=a("5ca1"),r=a("c366")(!0);n(n.P,"Array",{includes:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"67ab":function(e,t,a){var n=a("ca5a")("meta"),r=a("d3f4"),l=a("69a8"),i=a("86cc").f,o=0,s=Object.isExtensible||function(){return!0},c=!a("79e5")((function(){return s(Object.preventExtensions({}))})),u=function(e){i(e,n,{value:{i:"O"+ ++o,w:{}}})},p=function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!l(e,n)){if(!s(e))return"F";if(!t)return"E";u(e)}return e[n].i},f=function(e,t){if(!l(e,n)){if(!s(e))return!0;if(!t)return!1;u(e)}return e[n].w},d=function(e){return c&&m.NEED&&s(e)&&!l(e,n)&&u(e),e},m=e.exports={KEY:n,NEED:!1,fastKey:p,getWeak:f,onFreeze:d}},"8b97":function(e,t,a){var n=a("d3f4"),r=a("cb7c"),l=function(e,t){if(r(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{n=a("9b43")(Function.call,a("11e9").f(Object.prototype,"__proto__").set,2),n(e,[]),t=!(e instanceof Array)}catch(r){t=!0}return function(e,a){return l(e,a),t?e.__proto__=a:n(e,a),e}}({},!1):void 0),check:l}},aae3:function(e,t,a){var n=a("d3f4"),r=a("2d95"),l=a("2b4c")("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[l])?!!t:"RegExp"==r(e))}},b39a:function(e,t,a){var n=a("d3f4");e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},b562:function(e,t,a){"use strict";a.d(t,"p",(function(){return r})),a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return i})),a.d(t,"l",(function(){return o})),a.d(t,"j",(function(){return s})),a.d(t,"d",(function(){return c})),a.d(t,"i",(function(){return u})),a.d(t,"h",(function(){return p})),a.d(t,"m",(function(){return f})),a.d(t,"o",(function(){return d})),a.d(t,"f",(function(){return m})),a.d(t,"e",(function(){return b})),a.d(t,"c",(function(){return h})),a.d(t,"k",(function(){return v})),a.d(t,"q",(function(){return g})),a.d(t,"n",(function(){return y})),a.d(t,"g",(function(){return _}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/app/search",method:"get",params:e})}function l(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function o(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function s(e){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(n["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function f(e,t,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:a}})}function d(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function m(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function v(e,t){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function g(e,t){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function y(e,t){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function _(e){return Object(n["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}},c26b:function(e,t,a){"use strict";var n=a("86cc").f,r=a("2aeb"),l=a("dcbc"),i=a("9b43"),o=a("f605"),s=a("4a59"),c=a("01f9"),u=a("d53b"),p=a("7a56"),f=a("9e1e"),d=a("67ab").fastKey,m=a("b39a"),b=f?"_s":"size",h=function(e,t){var a,n=d(t);if("F"!==n)return e._i[n];for(a=e._f;a;a=a.n)if(a.k==t)return a};e.exports={getConstructor:function(e,t,a,c){var u=e((function(e,n){o(e,u,t,"_i"),e._t=t,e._i=r(null),e._f=void 0,e._l=void 0,e[b]=0,void 0!=n&&s(n,a,e[c],e)}));return l(u.prototype,{clear:function(){for(var e=m(this,t),a=e._i,n=e._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete a[n.i];e._f=e._l=void 0,e[b]=0},delete:function(e){var a=m(this,t),n=h(a,e);if(n){var r=n.n,l=n.p;delete a._i[n.i],n.r=!0,l&&(l.n=r),r&&(r.p=l),a._f==n&&(a._f=r),a._l==n&&(a._l=l),a[b]--}return!!n},forEach:function(e){m(this,t);var a,n=i(e,arguments.length>1?arguments[1]:void 0,3);while(a=a?a.n:this._f){n(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function(e){return!!h(m(this,t),e)}}),f&&n(u.prototype,"size",{get:function(){return m(this,t)[b]}}),u},def:function(e,t,a){var n,r,l=h(e,t);return l?l.v=a:(e._l=l={i:r=d(t,!0),k:t,v:a,p:n=e._l,n:void 0,r:!1},e._f||(e._f=l),n&&(n.n=l),e[b]++,"F"!==r&&(e._i[r]=l)),e},getEntry:h,setStrong:function(e,t,a){c(e,t,(function(e,a){this._t=m(e,t),this._k=a,this._l=void 0}),(function(){var e=this,t=e._k,a=e._l;while(a&&a.r)a=a.p;return e._t&&(e._l=a=a?a.n:e._t._f)?u(0,"keys"==t?a.k:"values"==t?a.v:[a.k,a.v]):(e._t=void 0,u(1))}),a?"entries":"values",!a,!0),p(t)}}},d2c8:function(e,t,a){var n=a("aae3"),r=a("be13");e.exports=function(e,t,a){if(n(t))throw TypeError("String#"+a+" doesn't accept regex!");return String(r(e))}},e0b8:function(e,t,a){"use strict";var n=a("7726"),r=a("5ca1"),l=a("2aba"),i=a("dcbc"),o=a("67ab"),s=a("4a59"),c=a("f605"),u=a("d3f4"),p=a("79e5"),f=a("5cc5"),d=a("7f20"),m=a("5dbc");e.exports=function(e,t,a,b,h,v){var g=n[e],y=g,_=h?"set":"add",O=y&&y.prototype,x={},w=function(e){var t=O[e];l(O,e,"delete"==e||"has"==e?function(e){return!(v&&!u(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return v&&!u(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,a){return t.call(this,0===e?0:e,a),this})};if("function"==typeof y&&(v||O.forEach&&!p((function(){(new y).entries().next()})))){var k=new y,j=k[_](v?{}:-0,1)!=k,F=p((function(){k.has(1)})),S=f((function(e){new y(e)})),D=!v&&p((function(){var e=new y,t=5;while(t--)e[_](t,t);return!e.has(-0)}));S||(y=t((function(t,a){c(t,y,e);var n=m(new g,t,y);return void 0!=a&&s(a,h,n[_],n),n})),y.prototype=O,O.constructor=y),(F||D)&&(w("delete"),w("has"),h&&w("get")),(D||j)&&w(_),v&&O.clear&&delete O.clear}else y=b.getConstructor(t,e,h,_),i(y.prototype,a),o.NEED=!0;return d(y,e),x[e]=y,r(r.G+r.W+r.F*(y!=g),x),v||b.setStrong(y,e,h),y}},e0fe:function(e,t,a){"use strict";a("6738")},fb32:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"5px 10px"}},[a("el-tabs",{model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"发布流程分析",name:"analysis-pipeline",lazy:!0}},[a("pipeline-analysis")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程实例数（所有云）",name:"analysis-pipeline-replicas",lazy:!0}},[a("pipeline-replicas")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"部署模块分析",name:"analysis-artifact",lazy:!0}},[a("artifact-analysis")],1)],1)],1)},r=[],l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{filterable:"",size:"small"},model:{value:e.searchForm.status,callback:function(t){e.$set(e.searchForm,"status",t)},expression:"searchForm.status"}},[a("el-option",{attrs:{label:"所有",value:""}}),e._v(" "),e._l(this.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"集群"}},[a("el-select",{attrs:{filterable:"",size:"small"},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},[a("el-option",{attrs:{label:"所有",value:""}}),e._v(" "),e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}))],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{attrs:{filterable:"",size:"small"},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},[a("el-option",{attrs:{label:"所有",value:""}}),e._v(" "),e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{attrs:{placeholder:"支持模糊匹配",size:"small",clearable:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app","string"===typeof t?t.trim():t)},expression:"searchForm.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"镜像"}},[a("el-input",{attrs:{placeholder:"支持模糊匹配",size:"small",clearable:""},model:{value:e.searchForm.image,callback:function(t){e.$set(e.searchForm,"image","string"===typeof t?t.trim():t)},expression:"searchForm.image"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"JVM参数"}},[a("el-input",{attrs:{placeholder:"支持模糊匹配",size:"small",clearable:""},model:{value:e.searchForm.javaOpts,callback:function(t){e.$set(e.searchForm,"javaOpts","string"===typeof t?t.trim():t)},expression:"searchForm.javaOpts"}})],1),e._v(" "),a("el-form-item",{staticStyle:{display:"none"},attrs:{label:""}},[a("el-checkbox-group",{on:{change:e.filterByOptions},model:{value:e.filterOptions,callback:function(t){e.filterOptions=t},expression:"filterOptions"}},[a("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"isCoreApp"}},[e._v("核心服务")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"appLogToKafka"}},[e._v("开启了日志上报")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"jvmGcLog"}},[e._v("开启了JmvGC日志")])],1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),e._v(" "),a("el-pagination",{staticStyle:{display:"inline-block"},attrs:{"current-page":e.searchForm.page,"page-sizes":[20,50,100,200,400,1e3,2e3,5e3],"page-size":e.searchForm.limit,layout:"total,prev,pager,next,sizes",total:e.tableDataByFilter.count},on:{"size-change":e.pageSizeChange,"current-change":e.pageChange}}),e._v(" "),a("div",{staticStyle:{display:"inline-block","margin-left":"50px","font-size":"12px",color:"orangered"}},[e._v("\n    资源统计： "+e._s(this.resourceTotal)+"\n  ")]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:e.tableDataByFilter.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"等级",sortable:"",prop:"appLevel",width:"80px"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群",sortable:"",prop:"cluster",width:"100px"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",sortable:"",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"镜像",sortable:"",prop:"baseImage"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.baseImage.substring(t.row.baseImage.lastIndexOf("/")+1)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"资源池",sortable:"",prop:"schedule.node"}}),e._v(" "),a("el-table-column",{attrs:{label:"开关"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.options,(function(t,n){return a("div",[t?a("el-tag",{staticStyle:{padding:"1px",margin:"1px 0","line-height":"normal",height:"unset","font-size":"10px"}},[e._v(e._s(n))]):e._e()],1)}))}}])}),e._v(" "),a("el-table-column",{attrs:{label:"副本",sortable:"",width:"80px",prop:"replicas",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{label:"JVM参数",width:"200px",prop:"jvmOpts","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{label:"CPU",width:"90px",sortable:"",prop:"resources.limitCPU"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.resources?[e._v("\n        "+e._s(t.row.resources.requestCPU.toFixed(1))+" - "+e._s(t.row.resources.limitCPU.toFixed(1))+"\n      ")]:void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{label:"内存 (MB)",width:"120px",sortable:"",prop:"resources.limitMemory"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.resources?[e._v("\n        "+e._s(t.row.resources.requestMemory)+" - "+e._s(t.row.resources.limitMemory)+"\n      ")]:void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{label:"负责人",sortable:"",prop:"appOwners"}}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("router-link",{attrs:{to:{name:"app-pipeline-edit",query:{pipelineId:t.row.id}},target:"_blank"}},[a("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("编辑")])]),e._v(" "),a("router-link",{staticStyle:{"margin-left":"2px"},attrs:{to:{name:"cicd-app-deploy",query:{app:t.row.app}},target:"_blank"}},[a("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("发布流程")])]),e._v(" "),a("router-link",{staticStyle:{"margin-left":"2px"},attrs:{to:{name:"pod-index",query:{cluster:t.row.cluster,namespace:t.row.namespace,app:t.row.app}},target:"_blank"}},[a("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("实例管理")])])]}}])})],1)],1)},i=[],o=(a("7f7f"),a("2fdb"),a("6762"),a("2d63")),s=a("51a9"),c=a("1e42"),u=a("b562"),p={name:"pipelineAnalysis",data:function(){return{appOwners:{},appLevels:{},tableData:{count:0,data:[]},statusOptions:[{label:"可用",value:"enabled"},{label:"禁用",value:"disabled"},{label:"已迁移",value:"migrated"}],tableDataByFilter:{count:0,data:[]},filterOptions:[],tableLoading:!1,searchForm:{cluster:"",namespace:"",app:"",image:"",status:"",javaOpts:"",options:"",page:1,limit:20}}},components:{ExportButton:c["a"]},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){var e,t=[],a=Object(o["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n,r=e.value,l=Object(o["a"])(r.namespaces);try{for(l.s();!(n=l.n()).done;){var i=n.value;t.includes(i)||t.push(i)}}catch(s){l.e(s)}finally{l.f()}}}catch(s){a.e(s)}finally{a.f()}return t},resourceTotal:function(){if(!this.tableDataByFilter||!this.tableDataByFilter.data||0===this.tableDataByFilter.data.length)return"--";var e,t=0,a=0,n=0,r=0,l=Object(o["a"])(this.tableDataByFilter.data);try{for(l.s();!(e=l.n()).done;){var i=e.value;i.resources&&(t+=i.resources.requestCPU*i.replicas,a+=i.resources.limitCPU*i.replicas,n+=i.resources.requestMemory*i.replicas,r+=i.resources.limitMemory*i.replicas)}}catch(s){l.e(s)}finally{l.f()}return"CPU: ".concat(t.toFixed(1)+" - "+a.toFixed(1)," | 内存: ").concat(n+" - "+r)}},mounted:function(){var e=this.$route.query.namespace;e&&(this.searchForm.namespace=e);var t=this.$route.query.keyword;t&&(this.searchForm.keyword=t),this.loadApps(this.loadTableData)},methods:{loadTableData:function(){var e=this;this.searchForm.limit>=200&&this.$message.warning("查询数据比较多时，页面渲染时间比较慢，请耐心等候..."),this.tableLoading=!0,Object(s["l"])(this.searchForm).then((function(t){var a,n=t.data.data,r=Object(o["a"])(n);try{for(r.s();!(a=r.n()).done;){var l=a.value;l.appOwners=e.appOwners[l.app]||"-",l.appLevel=e.appLevels[l.app]||"-"}}catch(i){r.e(i)}finally{r.f()}e.tableData=t.data,e.filterByOptions()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},pageChange:function(e){this.searchForm.page=e,this.loadTableData()},pageSizeChange:function(e){this.searchForm.limit=e,this.loadTableData()},pipelinePage:function(e){this.$router.push({name:"cicd-app-deploy",query:{app:e.app}})},filterByOptions:function(){var e=this;if(!this.filterOptions||0===this.filterOptions.length)return this.tableDataByFilter.data=this.tableData.data,void(this.tableDataByFilter.count=this.tableData.count);this.tableDataByFilter.data=this.tableData.data.filter((function(t){for(var a in t.options)if(e.filterOptions.includes(a)&&t.options[a])return!0;return!1})),this.tableDataByFilter.count=this.tableData.count},loadApps:function(e){var t=this;this.tableLoading=!0,Object(u["p"])({keyword:"",page:1,limit:2e3}).then((function(e){var a,n=Object(o["a"])(e.data.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;t.appOwners[r.name]=r.owner,t.appLevels[r.name]=r.level}}catch(l){n.e(l)}finally{n.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1,e&&e()}))},podPage:function(e){var t={cluster:e.cluster,namespace:e.namespace,app:e.app};this.$router.push({name:"pod-index",query:t})}}},f=p,d=a("2877"),m=Object(d["a"])(f,l,i,!1,null,null,null),b=m.exports,h=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("env-selector",{ref:"nsSelector",attrs:{"show-all-namespaces":!0}}),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"Git地址",sortable:"",prop:"gitUrl"}}),e._v(" "),a("el-table-column",{attrs:{label:"模块",sortable:"",prop:"module"}}),e._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),e._v(" "),a("el-table-column",{attrs:{label:"创建人",prop:"author"}}),e._v(" "),a("el-table-column",{attrs:{label:"被引用数",prop:"extraAttr.refCount",sortable:"",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{label:"引用发布流程",width:"560"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.extraAttr.ref,(function(t){return a("div",[e._v("\n            "+e._s(t)+"\n          ")])}))}}])})],1)],1)},v=[],g=a("530d"),y=a("55d7"),_={name:"artifactAnalysis",data:function(){return{tableData:[],tableLoading:!1,searchForm:{cluster:"",namespace:""}}},components:{ExportButton:c["a"],EnvSelector:y["a"]},computed:{},mounted:function(){},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,this.searchForm.cluster=this.$refs.nsSelector.cluster,this.searchForm.namespace=this.$refs.nsSelector.namespace,Object(g["a"])(this.searchForm).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},pipelinePage:function(e){this.$router.push({name:"cicd-app-deploy",query:{app:e.app}})},podPage:function(e){var t={cluster:e.cluster,namespace:e.namespace,app:e.app};this.$router.push({name:"pod-index",query:t})}}},O=_,x=Object(d["a"])(O,h,v,!1,null,null,null),w=x.exports,k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.search}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{"max-height":"700",data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app",width:"240px;"}}),e._v(" "),e._l(this.tableColumns,(function(t,n){return a("el-table-column",{attrs:{label:t,sortable:"",prop:t},scopedSlots:e._u([{key:"default",fn:function(n){return[n.row[t]>0?a("div",{staticStyle:{color:"dodgerblue","font-weight":"bold"}},[e._v(e._s(n.row[t]))]):e._e()]}}],null,!0)})})),e._v(" "),a("el-table-column",{attrs:{label:"",fixed:"right",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.pipelinePage(t.row)}}},[e._v("发布流程页\n            ")])]}}])})],2)],1)])],1)},j=[],F=(a("ac6a"),a("5df3"),a("4f7f"),{name:"pipelineReplicas",data:function(){return{tableColumns:new Set,tableData:[],tableLoading:!1,envType:""}},components:{ExportButton:c["a"]},mounted:function(){},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(s["a"])().then((function(t){e.tableData=e.tableDataHandler(t.data)})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},pipelinePage:function(e){var t=this.$router.resolve({name:"/share",query:{app:e.app}});window.open(t.href,"_blank")},search:function(){this.loadTableData()},tableDataHandler:function(e){this.tableColumns=new Set;var t,a=[],n=Object(o["a"])(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;a.push(r),this.tableColumns.add(r.namespace)}}catch(h){n.e(h)}finally{n.f()}for(var l=[],i=function(){var e=c[s],t=l.filter((function(t){return t["app"]===e.app})),a={app:e.app};t&&t.length>0?a=t[0]:l.push(a),a[e.namespace]=e.replicas},s=0,c=a;s<c.length;s++)i();for(var u=0,p=l;u<p.length;u++){var f,d=p[u],m=Object(o["a"])(this.tableColumns);try{for(m.s();!(f=m.n()).done;){var b=f.value;d[b]||(d[b]=0)}}catch(h){m.e(h)}finally{m.f()}}return l}}}),S=F,D=Object(d["a"])(S,k,j,!1,null,null,null),C=D.exports,$={components:{PipelineReplicas:C,ArtifactAnalysis:w,PipelineAnalysis:b},mounted:function(){},computed:{},data:function(){return{activeTab:"k8s-clusters"}},methods:{}},E=$,L=Object(d["a"])(E,n,r,!1,null,null,null);t["default"]=L.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-75c0e4d8"],{"250b":function(t,e,n){},"2fdb":function(t,e,n){"use strict";var i=n("5ca1"),a=n("d2c8"),s="includes";i(i.P+i.F*n("5147")(s),"String",{includes:function(t){return!!~a(this,t,s).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},5147:function(t,e,n){var i=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[i]=!1,!"/./"[t](e)}catch(a){}}return!0}},"662b":function(t,e,n){"use strict";n("250b")},6762:function(t,e,n){"use strict";var i=n("5ca1"),a=n("c366")(!0);i(i.P,"Array",{includes:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")("includes")},"76fe":function(t,e,n){"use strict";n.d(e,"k",(function(){return a})),n.d(e,"i",(function(){return s})),n.d(e,"a",(function(){return o})),n.d(e,"e",(function(){return r})),n.d(e,"b",(function(){return c})),n.d(e,"f",(function(){return l})),n.d(e,"c",(function(){return u})),n.d(e,"g",(function(){return d})),n.d(e,"h",(function(){return p})),n.d(e,"d",(function(){return b})),n.d(e,"l",(function(){return m})),n.d(e,"j",(function(){return f}));var i=n("b775");function a(t){return Object(i["a"])({url:"/v1/job/search",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function o(t){return Object(i["a"])({url:"/v1/job/build-image",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function l(t,e,n,a){return Object(i["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:a}})}function u(t,e,n,a){return Object(i["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:a}})}function d(t){return Object(i["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function p(t){return Object(i["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function b(t){return Object(i["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function m(t,e){return Object(i["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function f(t){return Object(i["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},aae3:function(t,e,n){var i=n("d3f4"),a=n("2d95"),s=n("2b4c")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[s])?!!e:"RegExp"==a(t))}},d2c8:function(t,e,n){var i=n("aae3"),a=n("be13");t.exports=function(t,e,n){if(i(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(a(t))}},d9dd:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container image-build-detail",staticStyle:{"min-height":"1200px"}},[n("div",{staticStyle:{width:"960px",margin:"0 auto"}},[n("el-steps",{attrs:{active:2,"align-center":""}},t._l(t.tasks,(function(e,i){return n("el-step",{staticStyle:{cursor:"pointer"},attrs:{title:e.title,description:"",status:e.status,icon:e.icon},nativeOn:{click:function(e){return t.stepClick(i)}}},[n("template",{slot:"description"},[t._v("\n          "+t._s(e.statusDesc)+"\n          "),"WAIT"===t.job.status.toUpperCase()?n("div",{staticStyle:{display:"inline-block"}},[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"前置任务完成后，才会启动当前任务",placement:"top"}},[n("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(e){return t.jobDetailPage(t.job.beforeJobId)}}},[t._v("\n                (查看前置任务)\n              ")])],1)],1):t._e()])],2)})),1)],1),t._v(" "),n("div",{staticStyle:{position:"relative",height:"20px"}},[n("div",{staticStyle:{float:"right","padding-bottom":"5px"}},[["RUNNING","WAIT"].includes(this.job.status)?n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-switch-button"},on:{click:function(e){return t.cancel()}}},[t._v("取消任务\n      ")]):n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-refresh-left"},on:{click:function(e){return t.redo()}}},[t._v("重新构建\n      ")]),t._v(" "),n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-search"},on:{click:function(e){return t.imagePage()}}},[t._v("查看镜像\n      ")])],1),t._v(" "),n("div",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)",float:"left"}},[t._v("\n      信息\n      "),n("div",{staticStyle:{"margin-left":"60px",display:"inline-block"}},[n("el-button",{staticStyle:{padding:"0",color:"#ccc","font-size":"10px"},attrs:{type:"text"},on:{click:function(e){return t.setJobFailed()}}},[t._v("设置为失败\n        ")]),t._v(" "),n("el-button",{staticStyle:{padding:"0",color:"#ccc","font-size":"10px"},attrs:{type:"text"},on:{click:function(e){return t.setJobSuccess()}}},[t._v("设置为成功\n        ")])],1)])]),t._v(" "),n("el-card",{staticClass:"box-card",staticStyle:{clear:"both"}},[n("el-descriptions",{attrs:{column:3,labelClassName:"desc-label",contentClassName:"desc-content"}},[n("el-descriptions-item",{attrs:{label:"Git地址"}},[t._v(t._s(this.job.params.gitUrl))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"Git模块"}},[t._v(t._s(this.job.params.gitModule))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"Git分支或Tag"}},[t._v(t._s(this.job.params.gitTag))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"CommitId"}},[t._v(t._s(this.job.params.commitId||"-"))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"Maven镜像"}},[t._v(t._s(this.job.params.mavenImage))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"父POM"}},[t._v(t._s(this.job.params.parentPom))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"镜像版本"}},[t._v(t._s(this.job.params.artifactImage?this.job.params.artifactImage.split(":").pop():"-"))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"依赖包版本检测"}},[t._v(t._s(this.job.params.dependencyCheck))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"强制编译"}},[t._v(t._s(this.job.params.forceCodeCompile))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"单元测试"}},[t._v(t._s(this.job.params.unitTest))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"备注"}},[t._v(t._s(this.job.remark))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"操作人"}},[t._v(t._s(this.job.author))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"操作时间"}},[t._v(t._s(this.job.createdAt))])],1)],1),t._v(" "),n("div",{staticStyle:{"margin-top":"20px","font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)","padding-bottom":"3px"}},[t._v("输出日志")]),t._v(" "),n("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"10px"}},[n("el-tabs",{attrs:{"tab-position":"top"}},t._l(t.tasks,(function(e,i){return n("el-tab-pane",{attrs:{label:e.title}},["BUILD"===e.type?n("div",[n("el-tabs",{staticStyle:{"margin-top":"10px"},attrs:{type:"border-card",value:"jenkinsTab2"}},[n("el-tab-pane",{attrs:{name:"jenkinsTab1",label:"纯文本日志"}},[n("pre",{staticClass:"el-collapse-item__content",staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(e.output))])]),t._v(" "),n("el-tab-pane",{attrs:{name:"jenkinsTab2"}},[e.attributes.jenkinsBuildId?[n("span",{attrs:{slot:"label"},slot:"label"},[t._v("Jenkins页面 "),n("small",{staticStyle:{color:"#aaa"}},[t._v("\n                  (如果没自动刷新，点击"),n("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return t.jenkinsPageReload()}}},[t._v("手动刷新")]),t._v(")\n                  ("),n("el-button",{staticStyle:{"margin-left":"0"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.jenkinsPage(e.attributes.jenkinsBuildId,e.attributes.jenkinsJob)}}},[t._v("新窗口打开")]),t._v(")")],1)]),t._v(" "),n("iframe",{staticStyle:{width:"100%","min-height":"800px",border:"solid 1px #eee"},attrs:{id:"jenkins_iframe",src:t.jenkinsUrl(e.attributes.jenkinsBuildId,e.attributes.jenkinsJob)}})]:[n("span",{attrs:{slot:"label"},slot:"label"},[t._v("Jenkins页面")]),t._v(" "),n("div")]],2)],1)],1):n("div",[n("pre",{staticClass:"el-collapse-item__content",staticStyle:{"white-space":"pre-wrap","padding-left":"10px"}},[t._v(t._s(e.output))])])])})),1)],1),t._v(" "),n("div",[n("el-backtop")],1)],1)},a=[],s=n("2d63"),o=(n("6762"),n("2fdb"),n("76fe")),r={data:function(){return{timerId:null,job:{params:{}},tasks:[],activeNames:[0],autoChangeActiveNames:!0,runningExecutions:[],jenkinsTable:"jenkinsTab2"}},components:{},computed:{},beforeDestroy:function(){this.timerId&&(clearTimeout(this.timerId),console.log("clear timer, id:"+this.timerId))},mounted:function(){var t=this.$route.query.jobId;t&&this.loadJob(t)},methods:{loadJob:function(t){var e=this;Object(o["g"])(t).then((function(n){e.job=n.data,e.loadTasks(t);var i=e.job.status.toUpperCase(),a=e;"RUNNING"===i&&(e.timerId=setTimeout((function(){a.loadJob(t)}),5e3))})).catch((function(t){e.$message.error(t.message)}))},loadTasks:function(t){var e=this;Object(o["h"])(t).then((function(t){e.tasks=t.data,e.modifyStages()})).catch((function(t){e.$message.error(t.message)}))},stepClick:function(t){this.activeNames.includes(t)?this.activeNames=[]:this.activeNames=[t]},modifyStages:function(){var t,e=Object(s["a"])(this.tasks);try{for(e.s();!(t=e.n()).done;){var n=t.value;n.status=this.toStepStatus(n.status),"process"===n.status?n.icon="el-icon-loading":n.icon=""}}catch(i){e.e(i)}finally{e.f()}},cancel:function(){var t=this;this.$confirm("是否继续取消？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["d"])(t.job.id).then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))})).catch((function(t){console.error(t)}))},setJobFailed:function(){var t=this;Object(o["l"])(this.job.id,"fail").then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))},setJobSuccess:function(){var t=this;Object(o["l"])(this.job.id,"success").then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))},redo:function(){var t=this;this.$confirm("确认使用当前参数重新构建吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["j"])(t.job.id).then((function(e){var n=e.data.id,i=t.$router.resolve({query:{jobId:n}});window.location.href=i.href,window.location.reload()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(t){console.error(t)}))},jobDetail:function(t){this.$router.push({name:"image-build-detail",query:{jobId:t}}),self.location.reload()},jenkinsPage:function(t,e){window.open(this.jenkinsUrl(t,e))},jenkinsUrl:function(t,e){var n="/api/page/redirect?type=jenkins&buildId=".concat(t);return e&&(n=n+"&job="+e),n},imagePage:function(){var t=this.$router.resolve({name:"cicd-image-list",query:{gitUrl:this.job.params.gitUrl,gitModule:this.job.params.gitModule}});window.open(t.href,"_blank")},jenkinsPageReload:function(){document.getElementById("jenkins_iframe").src+=""},jobDetailPage:function(t){var e=this;Object(o["g"])(t).then((function(n){var i=n.data,a="CD"===i.type?"cicd-app-deploy-detail":"cicd-image-build-detail",s=e.$router.resolve({name:a,query:{jobId:t}});window.open(s.href,"_blank")})).catch((function(t){e.$message.error(t.message)}))},toStepStatus:function(t){var e="";switch(t){case"WAIT":case"SKIP":case"CANCEL":e="wait";break;case"RUNNING":e="process";break;case"FAIL":e="error";break;case"SUCCESS":e="success";break;default:e="finish"}return e}}},c=r,l=(n("662b"),n("2877")),u=Object(l["a"])(c,i,a,!1,null,null,null);e["default"]=u.exports}}]);
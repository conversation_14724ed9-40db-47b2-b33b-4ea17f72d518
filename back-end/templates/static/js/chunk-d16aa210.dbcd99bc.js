(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d16aa210"],{"02f4":function(t,e,n){var a=n("4588"),r=n("be13");t.exports=function(t){return function(e,n){var o,i,l=String(r(e)),c=a(n),s=l.length;return c<0||c>=s?t?"":void 0:(o=l.charCodeAt(c),o<55296||o>56319||c+1===s||(i=l.charCodeAt(c+1))<56320||i>57343?t?l.charAt(c):o:t?l.slice(c,c+2):i-56320+(o-55296<<10)+65536)}}},"0390":function(t,e,n){"use strict";var a=n("02f4")(!0);t.exports=function(t,e,n){return e+(n?a(t,e).length:1)}},"0bfb":function(t,e,n){"use strict";var a=n("cb7c");t.exports=function(){var t=a(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"214f":function(t,e,n){"use strict";n("b0c5");var a=n("2aba"),r=n("32e9"),o=n("79e5"),i=n("be13"),l=n("2b4c"),c=n("520a"),s=l("species"),u=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),p=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();t.exports=function(t,e,n){var d=l(t),m=!o((function(){var e={};return e[d]=function(){return 7},7!=""[t](e)})),f=m?!o((function(){var e=!1,n=/a/;return n.exec=function(){return e=!0,null},"split"===t&&(n.constructor={},n.constructor[s]=function(){return n}),n[d](""),!e})):void 0;if(!m||!f||"replace"===t&&!u||"split"===t&&!p){var h=/./[d],b=n(i,d,""[t],(function(t,e,n,a,r){return e.exec===c?m&&!r?{done:!0,value:h.call(e,n,a)}:{done:!0,value:t.call(n,e,a)}:{done:!1}})),v=b[0],g=b[1];a(String.prototype,t,v),r(RegExp.prototype,d,2==e?function(t,e){return g.call(t,this,e)}:function(t){return g.call(t,this)})}}},"251c":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container",attrs:{"element-loading-text":"操作比较重，耗时比较长，请耐心等待...","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[n("el-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[n("el-tab-pane",{attrs:{label:"快照-创建",name:"create",lazy:!0}},[n("app-version-snapshot-create")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"快照-查看",name:"list",lazy:!0}},[n("app-version-snapshot-list")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"快照-发布",name:"deploy",lazy:!0}},[n("app-version-snapshot-batch-deploy")],1)],1)],1)},r=[],o=n("a68b"),i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[n("div",[n("el-table",{attrs:{data:t.tableData,"element-loading-text":"数据加载中...",border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{label:"快照名称",prop:"Column01"}}),t._v(" "),n("el-table-column",{attrs:{label:"快照ID",width:"100",prop:"ID"}}),t._v(" "),n("el-table-column",{attrs:{label:"备注",prop:"Column04"}}),t._v(" "),n("el-table-column",{attrs:{label:"快照集群",prop:"Column02"}}),t._v(" "),n("el-table-column",{attrs:{label:"快照环境",prop:"Column03"}}),t._v(" "),n("el-table-column",{attrs:{label:"创建人",prop:"Column05"}}),t._v(" "),n("el-table-column",{attrs:{label:"创建时间",sortable:"",prop:"CreatedAt"}}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"280px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{type:"text"},on:{click:function(n){return t.contentShow(e.row)}}},[t._v("详细内容\n          ")]),t._v(" "),n("el-button",{attrs:{type:"text"},on:{click:function(n){return t.deleteData(e.row)}}},[t._v("删除\n          ")]),t._v(" "),n("el-button",{attrs:{type:"text",icon:"el-icon-position"},on:{click:function(n){return t.deployPage(e.row)}}},[t._v("发布到复制云\n          ")])]}}])})],1)],1),t._v(" "),n("el-dialog",{attrs:{title:t.contentDialog.title,visible:t.contentDialog.visible,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.contentDialog,"visible",e)}}},[n("div",{staticStyle:{"margin-top":"-50px",overflow:"auto"}},[n("div",{staticStyle:{"text-align":"center"}},[n("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-document-copy"},on:{click:t.copyToClipboard}},[t._v("一键复制内容")])],1),t._v(" "),n("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[t._v(t._s(this.contentDialog.content))])])])],1)},l=[],c=n("c1ab"),s=n("b144"),u={name:"AppVersionSnapshotList",components:{},mounted:function(){this.pageLoading=!0,this.loadTableData()},beforeDestroy:function(){},computed:{},data:function(){return{tableData:[],pageLoading:!1,contentDialog:{visible:!1,title:"",content:""}}},methods:{loadTableData:function(){var t=this;this.pageLoading=!0,this.tableData=[],Object(c["A"])().then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))},deleteData:function(t){var e=this;this.$confirm("此操作将删除数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pageLoading=!0,Object(c["h"])(t.ID).then((function(t){})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1,e.loadTableData()}))}))},deployPage:function(t){var e=Object(s["a"])(this.$route.query);e["tab"]="deploy",e["snapshotId"]=t.ID,this.$router.push({query:e}),window.location.reload()},contentShow:function(t){this.contentDialog.content=t.Content,this.contentDialog.title="快照名称："+t.Column01,this.contentDialog.visible=!0},copyToClipboard:function(){var t=this,e=this.contentDialog.content;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},p=u,d=n("2877"),m=Object(d["a"])(p,i,l,!1,null,null,null),f=m.exports,h=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container",attrs:{"element-loading-text":"操作比较重，耗时比较长，请耐心等待...","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[n("div",[n("div",{staticStyle:{"margin-bottom":"10px"}},[n("el-alert",{staticStyle:{width:"670px"},attrs:{title:"应用版本快照",type:"info",closable:!1,description:"对选择环境下所有应用当前运行版本进行快照保留（包括git和镜像），便于后续私有云环境的发布","show-icon":""}})],1),t._v(" "),n("el-form",{ref:"searchForm",attrs:{model:t.searchForm}},[n("el-form-item",{attrs:{label:"选择集群",prop:"cluster"}},[n("el-select",{staticStyle:{width:"600px"},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(this.clusterOptions,(function(t){return n("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"选择环境",prop:"namespace"}},[n("el-select",{staticStyle:{width:"600px"},attrs:{filterable:""},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(this.namespaceOptions,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"快照名称",prop:"version"}},[n("el-input",{staticStyle:{width:"600px"},model:{value:t.searchForm.version,callback:function(e){t.$set(t.searchForm,"version",e)},expression:"searchForm.version"}},[n("template",{slot:"append"},[t._v(t._s(this.currentDate))])],2)],1),t._v(" "),n("el-form-item",{attrs:{label:"备注信息",prop:"version"}},[n("el-input",{staticStyle:{width:"600px"},model:{value:t.searchForm.remark,callback:function(e){t.$set(t.searchForm,"remark",e)},expression:"searchForm.remark"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"模拟执行",prop:"dryRun"}},[n("el-select",{staticStyle:{width:"600px"},model:{value:t.searchForm.dryRun,callback:function(e){t.$set(t.searchForm,"dryRun",e)},expression:"searchForm.dryRun"}},[n("el-option",{attrs:{value:"true",label:"true"}}),t._v(" "),n("el-option",{attrs:{value:"false",label:"false"}})],1)],1),t._v(" "),n("el-form-item",[n("div",{staticStyle:{"margin-top":"20px",width:"600px","text-align":"right"}},[n("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("生成快照")])],1)])],1)],1),t._v(" "),t._m(0),t._v(" "),null!==t.output?n("div",{staticStyle:{"margin-top":"20px",border:"1px solid #ccc","background-color":"#eee",padding:"5px","font-size":"12px"}},[n("pre",{staticStyle:{"word-wrap":"break-word","white-space":"pre-wrap"}},[t._v(t._s(t.jsonOutput))])]):t._e()])},b=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("pre")])}],v=(n("f576"),n("7f7f"),n("2d63")),g={name:"appVersionSnapshotCreate",components:{appManageTab:o["a"]},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var t,e=Object(v["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var n=t.value;if(this.searchForm.cluster===n.name)return n.namespaces}}catch(a){e.e(a)}finally{e.f()}}return[]},currentDate:function(){var t=new Date,e=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0");return"-".concat(e).concat(n).concat(a)},jsonOutput:function(){return JSON.stringify(this.output,null,2)}},data:function(){return{pageLoading:!1,searchForm:{cluster:"",namespace:"",version:"",remark:"",dryRun:!0},output:null}},methods:{submit:function(){var t=this;this.pageLoading=!0,this.output=null,Object(c["e"])(this.searchForm.cluster,this.searchForm.namespace,this.searchForm.version+this.currentDate,this.searchForm.remark,this.searchForm.dryRun).then((function(e){t.output=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))}}},y=g,x=Object(d["a"])(y,h,b,!1,null,null,null),_=x.exports,j=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[t.pageAlert?n("div",{staticStyle:{margin:"20px"}},[n("el-alert",{attrs:{title:t.pageAlert,closable:!1,type:"error","show-icon":""}})],1):t._e(),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",[n("div",{staticStyle:{"font-weight":"bold",color:"#3a8ee6"}},[t._v("快照信息：")]),t._v(" "),n("el-descriptions",{attrs:{direction:"vertical",column:4,border:""}},[n("el-descriptions-item",{attrs:{label:"快照名称"}},[t._v(t._s(t.appSnapshot.Column01))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"快照ID"}},[t._v(t._s(t.appSnapshot.ID))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"快照集群"}},[t._v(t._s(t.appSnapshot.Column02))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"快照环境"}},[t._v(t._s(t.appSnapshot.Column03))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"创建人"}},[t._v(t._s(t.appSnapshot.Column05))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"备注"}},[t._v(t._s(t.appSnapshot.Column04))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"创建时间"}},[t._v(t._s(t.appSnapshot.CreatedAt))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"详细内容"}},[n("el-button",{attrs:{type:"text",size:"mini"},on:{click:t.contentShow}},[t._v("查看详情")])],1)],1)],1)]),t._v(" "),n("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"10px"}},[n("div",[n("div",{staticStyle:{"font-weight":"bold",color:"#3a8ee6"}},[t._v("发布流程：")]),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData,"row-class-name":t.tableRowClassName,"element-loading-text":"数据加载中...",border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"60",align:"center"}}),t._v(" "),n("el-table-column",{attrs:{label:"",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{},[n("router-link",{attrs:{to:{name:"app-pipeline-execution-history",query:{keyword:e.row.app,namespace:e.row.namespace}},target:"_blank"}},[n("span",{staticStyle:{color:"#409EFF","font-size":"12px"}},[t._v("发布历史")])]),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("span",{staticStyle:{color:"#409EFF","font-size":"12px"}},[t._v("发布流程")])]),t._v(" "),n("el-button",{staticStyle:{margin:"0",padding:"5px 10px"},attrs:{type:"primary",size:"mini"},on:{click:function(n){return t.deploySubmit(e.row)}}},[t._v("发布\n              ")])],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境",prop:"namespace"}}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",align:"center",prop:"status"}}),t._v(" "),n("el-table-column",{attrs:{label:"实例数(运行/配置)",align:"center",prop:"replicas"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",[t._v("\n              "+t._s(e.row.extraAttr.runningPodNum)+" / "+t._s(e.row.replicas)+"\n            ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"快照版本"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.extraAttr.snapshotVersion.indexOf("?")>-1?n("span",{staticStyle:{color:"red","font-weight":"bold"}},[t._v("\n            "+t._s(e.row.extraAttr.snapshotVersion)+"\n          ")]):n("span",[t._v("\n            "+t._s(e.row.extraAttr.snapshotVersion)+"\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"当前运行版本"},scopedSlots:t._u([{key:"default",fn:function(e){return["?"===e.row.extraAttr.deployTag?n("div",[n("i",{staticClass:"el-icon-loading",staticStyle:{"font-size":"20px",color:"red","font-weight":"bold"}})]):e.row.extraAttr.deployTag!==e.row.extraAttr.snapshotVersion?n("div",{staticStyle:{color:"red"}},[t._v("\n              "+t._s(e.row.extraAttr.deployTag)+"\n\n            ")]):n("div",[t._v("\n              "+t._s(e.row.extraAttr.deployTag)+"\n            ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"应用负责人",property:"extraAttr.owner"}})],1)],1)]),t._v(" "),n("el-dialog",{attrs:{title:t.contentDialog.title,visible:t.contentDialog.visible,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.contentDialog,"visible",e)}}},[n("div",{staticStyle:{"margin-top":"-30px",overflow:"auto"}},[n("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[t._v(t._s(this.contentDialog.content))])])])],1)},O=[],w=(n("28a5"),n("8504")),S=n("51a9"),k=n("76fe"),D={name:"AppVersionSnapshotBatchDeploy",components:{},data:function(){return{cluster:"forceecrm-k8s1",namespace:"forceecrm-public-prod",tableData:[],pageAlert:"",tableLoading:!1,appSnapshotId:"",appSnapshot:{ID:""},contentDialog:{visible:!1,title:"",content:""}}},computed:{},mounted:function(){this.appSnapshotId=this.$route.query.snapshotId,this.appSnapshotId?(this.$route.query.cluster&&(this.cluster=this.$route.query.cluster),this.$route.query.namespace&&(this.namespace=this.$route.query.namespace),this.loadAppSnapshot()):this.pageAlert="请选择一个版本快照"},methods:{loadTableData:function(){var t=this;this.tableLoading=!0;var e={cluster:this.cluster,namespace:this.namespace,page:1,limit:1e3};Object(S["l"])(e).then((function(e){t.tableData=e.data.data;var n,a=Object(v["a"])(t.tableData);try{for(a.s();!(n=a.n()).done;){var r=n.value;r.extraAttr.deployTag="?",r.extraAttr.runningPodNum="?",r.extraAttr.owner="?",r.extraAttr.deployParams=t.parseDeployParam(r),r.extraAttr.snapshotVersion=t.parseAppSnapshotVersion(r)}}catch(o){a.e(o)}finally{a.f()}t.loadDeployments(),t.loadAppOwners()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},contentShow:function(){this.contentDialog.title="快照名称："+this.appSnapshot.Column01,this.contentDialog.content=this.appSnapshot.Content,this.contentDialog.visible=!0},parseDeployParam:function(t){var e,n={pipelineIds:t.id,maxSurge:"50%",remark:"",deployModuleImages:[]},a=Object(v["a"])(t.appModules);try{for(a.s();!(e=a.n()).done;){var r,o=e.value,i={gitUrl:o.gitUrl,gitModule:o.module,image:""},l=Object(v["a"])(this.appSnapshot.contentItems);try{for(l.s();!(r=l.n()).done;){var c=r.value;if(o.gitUrl===c.deployModule.gitUrl&&o.module===c.deployModule.module&&c.artifactImageSnapshot){i.image=c.artifactImageSnapshot.replaceAll("app-snapshot/","");break}}}catch(s){l.e(s)}finally{l.f()}n.deployModuleImages.push(i)}}catch(s){a.e(s)}finally{a.f()}return{items:[].push(n)}},parseAppSnapshotVersion:function(t){var e,n=[],a=Object(v["a"])(t.extraAttr.deployParams.deployModuleImages);try{for(a.s();!(e=a.n()).done;){var r=e.value;r.image?n.push(r.image.split(":")[1].replaceAll("---","/")):n.push("?")}}catch(o){a.e(o)}finally{a.f()}return n.join("|")},loadAppSnapshot:function(){var t=this;this.tableLoading=!0,Object(c["j"])(this.appSnapshotId).then((function(e){t.appSnapshot=e.data,t.appSnapshot.contentItems=JSON.parse(t.appSnapshot.Content),t.loadTableData()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},tableRowClassName:function(t){var e=t.row;t.rowIndex;return e.cluster+"-row"},loadAppOwners:function(){var t=this;this.tableLoading=!0,Object(c["a"])().then((function(e){var n,a=Object(v["a"])(t.tableData);try{for(a.s();!(n=a.n()).done;){var r,o=n.value,i=Object(v["a"])(e.data);try{for(i.s();!(r=i.n()).done;){var l=r.value;if(l.service===o.app){l.owners&&(o.extraAttr.owner=l.owners.join(","));break}}}catch(c){i.e(c)}finally{i.f()}}}catch(c){a.e(c)}finally{a.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},loadDeployments:function(){var t=this;this.tableLoading=!0,Object(w["g"])(this.cluster,this.namespace).then((function(e){var n,a=Object(v["a"])(t.tableData);try{for(a.s();!(n=a.n()).done;){var r,o=n.value,i=Object(v["a"])(e.data);try{for(i.s();!(r=i.n()).done;){var l=r.value;if(l.name===o.app&&l.namespace===o.namespace){o.extraAttr.deployTag=l.deployTag,o.extraAttr.runningPodNum=l.replicas;break}}}catch(c){i.e(c)}finally{i.f()}}}catch(c){a.e(c)}finally{a.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},deploySubmit:function(t){var e=this,n=t.extraAttr.deployParams;if(n&&n.deployModuleImages){var a,r=Object(v["a"])(n.deployModuleImages);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(!o.image)return void this.$message.error("应用".concat(t.app,"的模块").concat(o.gitModule,"没有在版本快照里找到镜像版本"))}}catch(i){r.e(i)}finally{r.f()}console.log(JSON.stringify(n)),this.tableLoading=!0,Object(k["e"])(n).then((function(t){e.$message.success("操作成功，请到发布历史页查看详情")})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))}else this.$message.error("没有在版本快照里找到镜像版本")}}},$=D,C=Object(d["a"])($,j,O,!1,null,null,null),A=C.exports,T={components:{AppVersionSnapshotBatchDeploy:A,AppVersionSnapshotCreate:_,AppVersionSnapshotList:f,appManageTab:o["a"]},mounted:function(){var t=this.$route.query.tab;t||(t="create"),this.activeTab=t},beforeDestroy:function(){},computed:{},data:function(){return{activeTab:""}},methods:{}},F=T,I=Object(d["a"])(F,a,r,!1,null,null,null);e["default"]=I.exports},"28a5":function(t,e,n){"use strict";var a=n("aae3"),r=n("cb7c"),o=n("ebd6"),i=n("0390"),l=n("9def"),c=n("5f1b"),s=n("520a"),u=n("79e5"),p=Math.min,d=[].push,m="split",f="length",h="lastIndex",b=4294967295,v=!u((function(){RegExp(b,"y")}));n("214f")("split",2,(function(t,e,n,u){var g;return g="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[f]||2!="ab"[m](/(?:ab)*/)[f]||4!="."[m](/(.?)(.?)/)[f]||"."[m](/()()/)[f]>1||""[m](/.?/)[f]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!a(t))return n.call(r,t,e);var o,i,l,c=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,m=void 0===e?b:e>>>0,v=new RegExp(t.source,u+"g");while(o=s.call(v,r)){if(i=v[h],i>p&&(c.push(r.slice(p,o.index)),o[f]>1&&o.index<r[f]&&d.apply(c,o.slice(1)),l=o[0][f],p=i,c[f]>=m))break;v[h]===o.index&&v[h]++}return p===r[f]?!l&&v.test("")||c.push(""):c.push(r.slice(p)),c[f]>m?c.slice(0,m):c}:"0"[m](void 0,0)[f]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,a){var r=t(this),o=void 0==n?void 0:n[e];return void 0!==o?o.call(n,r,a):g.call(String(r),n,a)},function(t,e){var a=u(g,t,this,e,g!==n);if(a.done)return a.value;var s=r(t),d=String(this),m=o(s,RegExp),f=s.unicode,h=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(v?"y":"g"),y=new m(v?s:"^(?:"+s.source+")",h),x=void 0===e?b:e>>>0;if(0===x)return[];if(0===d.length)return null===c(y,d)?[d]:[];var _=0,j=0,O=[];while(j<d.length){y.lastIndex=v?j:0;var w,S=c(y,v?d:d.slice(j));if(null===S||(w=p(l(y.lastIndex+(v?0:j)),d.length))===_)j=i(d,j,f);else{if(O.push(d.slice(_,j)),O.length===x)return O;for(var k=1;k<=S.length-1;k++)if(O.push(S[k]),O.length===x)return O;j=_=w}}return O.push(d.slice(_)),O}]}))},"2e08":function(t,e,n){var a=n("9def"),r=n("9744"),o=n("be13");t.exports=function(t,e,n,i){var l=String(o(t)),c=l.length,s=void 0===n?" ":String(n),u=a(e);if(u<=c||""==s)return l;var p=u-c,d=r.call(s,Math.ceil(p/s.length));return d.length>p&&(d=d.slice(0,p)),i?d+l:l+d}},"51a9":function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"e",(function(){return o})),n.d(e,"d",(function(){return i})),n.d(e,"l",(function(){return l})),n.d(e,"m",(function(){return c})),n.d(e,"a",(function(){return s})),n.d(e,"f",(function(){return u})),n.d(e,"i",(function(){return p})),n.d(e,"j",(function(){return d})),n.d(e,"k",(function(){return m})),n.d(e,"n",(function(){return f})),n.d(e,"g",(function(){return h})),n.d(e,"b",(function(){return b})),n.d(e,"h",(function(){return v})),n.d(e,"o",(function(){return g}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(a["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function i(t,e,n){return Object(a["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:n}})}function l(t){return Object(a["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function s(){return Object(a["a"])({url:"/v1/pipeline/all",method:"get"})}function u(t){return Object(a["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function p(t){return Object(a["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(a["a"])({url:"/v1/pipeline",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function f(t){return Object(a["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function h(t,e,n,r){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:r}})}function b(t){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function v(t,e,n,r){return Object(a["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:r}})}function g(t){return Object(a["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"520a":function(t,e,n){"use strict";var a=n("0bfb"),r=RegExp.prototype.exec,o=String.prototype.replace,i=r,l="lastIndex",c=function(){var t=/a/,e=/b*/g;return r.call(t,"a"),r.call(e,"a"),0!==t[l]||0!==e[l]}(),s=void 0!==/()??/.exec("")[1],u=c||s;u&&(i=function(t){var e,n,i,u,p=this;return s&&(n=new RegExp("^"+p.source+"$(?!\\s)",a.call(p))),c&&(e=p[l]),i=r.call(p,t),c&&i&&(p[l]=p.global?i.index+i[0].length:e),s&&i&&i.length>1&&o.call(i[0],n,(function(){for(u=1;u<arguments.length-2;u++)void 0===arguments[u]&&(i[u]=void 0)})),i}),t.exports=i},"5f1b":function(t,e,n){"use strict";var a=n("23c6"),r=RegExp.prototype.exec;t.exports=function(t,e){var n=t.exec;if("function"===typeof n){var o=n.call(t,e);if("object"!==typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==a(t))throw new TypeError("RegExp#exec called on incompatible receiver");return r.call(t,e)}},"76fe":function(t,e,n){"use strict";n.d(e,"k",(function(){return r})),n.d(e,"i",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"e",(function(){return l})),n.d(e,"b",(function(){return c})),n.d(e,"f",(function(){return s})),n.d(e,"c",(function(){return u})),n.d(e,"g",(function(){return p})),n.d(e,"h",(function(){return d})),n.d(e,"d",(function(){return m})),n.d(e,"l",(function(){return f})),n.d(e,"j",(function(){return h}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/job/search",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function i(t){return Object(a["a"])({url:"/v1/job/build-image",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function s(t,e,n,r){return Object(a["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:r}})}function u(t,e,n,r){return Object(a["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:r}})}function p(t){return Object(a["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(a["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(a["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function f(t,e){return Object(a["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function h(t){return Object(a["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return r})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return i})),n.d(e,"c",(function(){return l})),n.d(e,"b",(function(){return c})),n.d(e,"i",(function(){return s})),n.d(e,"d",(function(){return u})),n.d(e,"f",(function(){return p})),n.d(e,"e",(function(){return d}));var a=n("b775");function r(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function l(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function s(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function u(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function p(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,r,o){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:r,deployTag:o||""}})}},9744:function(t,e,n){"use strict";var a=n("4588"),r=n("be13");t.exports=function(t){var e=String(r(this)),n="",o=a(t);if(o<0||o==1/0)throw RangeError("Count can't be negative");for(;o>0;(o>>>=1)&&(e+=e))1&o&&(n+=e);return n}},a68b:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[n("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[n("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),n("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},r=[],o=(n("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var n=t.name;"app-restart"===n?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===n?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===n?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===n?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===n?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===n?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===n?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===n?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===n?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===n?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===n?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===n?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===n?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===n?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),i=o,l=(n("d54f"),n("2877")),c=Object(l["a"])(i,a,r,!1,null,null,null);e["a"]=c.exports},aae3:function(t,e,n){var a=n("d3f4"),r=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return a(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},b0c5:function(t,e,n){"use strict";var a=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:a!==/./.exec},{exec:a})},b144:function(t,e,n){"use strict";function a(t){return JSON.parse(JSON.stringify(t))}function r(t){if(!t||!(t instanceof Date))return"";var e=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();return e}function o(t){return"isCoreApp"===t?"核心服务":"onlyDeployTag"===t?"只允许部署Tag":"addSysctlKeepalive"===t?"调整内核参数":"skyWalkingAgent"===t?"性能跟踪":"appLogToKafka"===t?"接入ClickHouse日志":"buildUseRuntimeJDK"===t?"镜像JDK版本编译代码":"jvmGcLog"===t?"GC日志":t}n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return o}))},c1ab:function(t,e,n){"use strict";n.d(e,"i",(function(){return r})),n.d(e,"k",(function(){return o})),n.d(e,"y",(function(){return i})),n.d(e,"z",(function(){return l})),n.d(e,"d",(function(){return c})),n.d(e,"g",(function(){return s})),n.d(e,"C",(function(){return u})),n.d(e,"E",(function(){return p})),n.d(e,"x",(function(){return d})),n.d(e,"b",(function(){return m})),n.d(e,"f",(function(){return f})),n.d(e,"B",(function(){return h})),n.d(e,"D",(function(){return b})),n.d(e,"w",(function(){return v})),n.d(e,"F",(function(){return g})),n.d(e,"l",(function(){return y})),n.d(e,"e",(function(){return x})),n.d(e,"a",(function(){return _})),n.d(e,"A",(function(){return j})),n.d(e,"j",(function(){return O})),n.d(e,"h",(function(){return w})),n.d(e,"r",(function(){return S})),n.d(e,"u",(function(){return k})),n.d(e,"v",(function(){return D})),n.d(e,"n",(function(){return $})),n.d(e,"o",(function(){return C})),n.d(e,"s",(function(){return A})),n.d(e,"t",(function(){return T})),n.d(e,"c",(function(){return F})),n.d(e,"p",(function(){return I})),n.d(e,"q",(function(){return L})),n.d(e,"m",(function(){return E}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(a["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function i(t){return Object(a["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function c(){return Object(a["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(a["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(a["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,n,r,o,i,l,c){return Object(a["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(i,"&parentPom=").concat(l),method:"post",data:c})}function h(t){return Object(a["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,n,r){return Object(a["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function x(t,e,n,r,o){return Object(a["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function _(){return Object(a["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function j(){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function O(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function w(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function S(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function k(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function D(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function $(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function A(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function T(t,e){return Object(a["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function F(t){return Object(a["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function I(t){return Object(a["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function L(t){return Object(a["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function E(t,e,n){return Object(a["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}},d54f:function(t,e,n){"use strict";n("eba1")},eba1:function(t,e,n){},f576:function(t,e,n){"use strict";var a=n("5ca1"),r=n("2e08"),o=n("a25f"),i=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(o);a(a.P+a.F*i,"String",{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0bdfa6"],{"2dfd":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container "},[t.cluster&&t.namespace&&t.pod&&t.path?a("pod-file",{attrs:{cluster:t.cluster,namespace:t.namespace,pod:t.pod,path:t.path,"from-page":!0}}):t._e()],1)},p=[],n=a("67e1"),o={components:{PodFile:n["a"]},data:function(){return{cluster:"",namespace:"",pod:"",path:""}},mounted:function(){this.$route.query.path?this.path=this.$route.query.path:this.path="/opt/tomcat/logs";var t=this.$route.query;t.cluster&&t.namespace&&t.pod?(this.cluster=t.cluster,this.namespace=t.namespace,this.pod=t.pod):this.$message.error("请输入完整参数")},methods:{}},r=o,c=a("2877"),u=Object(c["a"])(r,s,p,!1,null,null,null);e["default"]=u.exports}}]);
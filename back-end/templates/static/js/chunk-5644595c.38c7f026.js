(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5644595c"],{"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"e",(function(){return l})),a.d(t,"d",(function(){return i})),a.d(t,"l",(function(){return s})),a.d(t,"m",(function(){return o})),a.d(t,"a",(function(){return u})),a.d(t,"f",(function(){return c})),a.d(t,"i",(function(){return p})),a.d(t,"j",(function(){return m})),a.d(t,"k",(function(){return d})),a.d(t,"n",(function(){return f})),a.d(t,"g",(function(){return h})),a.d(t,"b",(function(){return b})),a.d(t,"h",(function(){return v})),a.d(t,"o",(function(){return g}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function i(e,t,a){return Object(r["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function s(e){return Object(r["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function u(){return Object(r["a"])({url:"/v1/pipeline/all",method:"get"})}function c(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(r["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function m(e){return Object(r["a"])({url:"/v1/pipeline",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function f(e){return Object(r["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function h(e,t,a,n){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:n}})}function b(e){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function v(e,t,a,n){return Object(r["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:n}})}function g(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"6a0a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"pipeline-clone"}}),e._v(" "),a("div",{staticStyle:{width:"600px",margin:"0 auto"}},[a("el-steps",{attrs:{active:e.active,"finish-status":"success"}},[a("el-step",{attrs:{title:"选择集群参数","process-status":"error"}}),e._v(" "),a("el-step",{attrs:{title:"开始克隆"}})],1),e._v(" "),a("el-button",{staticStyle:{"margin-top":"12px"},attrs:{loading:e.loading},on:{click:e.next}},[e._v(e._s(e.nextBtnText))])],1),e._v(" "),a("el-divider"),e._v(" "),"query-pipelines"===e.active?a("div",{staticClass:"app-container"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",attrs:{inline:!0,rules:e.rules,model:e.form}},[a("el-form-item",{attrs:{label:"从",prop:"sourceCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"源集群",filterable:""},model:{value:e.form.sourceCluster,callback:function(t){e.$set(e.form,"sourceCluster",t)},expression:"form.sourceCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"克隆到",prop:"targetCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"目标集群",filterable:""},model:{value:e.form.targetCluster,callback:function(t){e.$set(e.form,"targetCluster",t)},expression:"form.targetCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1)],1)],1):e._e(),e._v(" "),"clone-pipelines"===e.active?a("div",[a("el-alert",{attrs:{title:e.form.sourceCluster.cluster+"/"+e.form.sourceCluster.namespace+"--\x3e"+e.form.targetCluster.cluster+"/"+e.form.targetCluster.namespace,type:"warning",center:""}}),e._v(" "),a("el-table",{ref:"multipleTable",attrs:{data:e.clonePipelineTableData,size:"mini"},on:{"selection-change":e.handleClonePipelineTableSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{property:"app",label:"所属应用"}}),e._v(" "),a("el-table-column",{attrs:{property:"status",label:"发布流程状态"}})],1)],1):e._e()],1)},n=[],l=(a("7f7f"),a("2d63")),i=a("51a9"),s=a("a68b"),o={name:"clone",components:{appManageTab:s["a"]},data:function(){var e=function(e,t,a){null===t.cluster&&null===t.namespace?a(new Error("请选择集群")):a()};return{active:"query-pipelines",form:{sourceCluster:{cluster:null,namespace:null},targetCluster:{cluster:null,namespace:null}},rules:{sourceCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}],targetCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}]},clonePipelineTableData:[],clonePipelineTableMultipleSelection:[],loading:!1}},computed:{nextBtnText:function(){return"query-pipelines"===this.active?"查询发布流程":"clone-pipelines"===this.active?"克隆发布流程":"未知操作"},clusterOptions:function(){var e,t=[],a=0,r=Object(l["a"])(this.$settings.clusters);try{for(r.s();!(e=r.n()).done;){var n,i=e.value,s=Object(l["a"])(i.namespaces);try{for(s.s();!(n=s.n()).done;){var o=n.value,u={};u.cluster=i.name,u.namespace=o,u.id=a,t.push(u),a++}}catch(c){s.e(c)}finally{s.f()}}}catch(c){r.e(c)}finally{r.f()}return t}},methods:{next:function(){var e=this;switch(this.active){case"query-pipelines":this.$refs["ruleForm"].validate((function(t){if(!t)return!1;e.loading=!0,e.loadClonePipeline()}));break;case"clone-pipelines":if(this.clonePipelineTableMultipleSelection.length<=0)return void this.$message({message:"未选择克隆的应用",type:"warning"});this.loading=!0,this.clonePipeline()}},loadClonePipeline:function(){var e=this;Object(i["g"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.targetCluster.cluster,this.form.targetCluster.namespace).then((function(t){e.active="clone-pipelines",e.clonePipelineTableData=t.data,e.$nextTick((function(){e.$refs.multipleTable.toggleAllSelection()}))})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},handleClonePipelineTableSelectionChange:function(e){this.clonePipelineTableMultipleSelection=e},clonePipeline:function(){var e,t=this,a=[],r=Object(l["a"])(this.clonePipelineTableMultipleSelection);try{for(r.s();!(e=r.n()).done;){var n=e.value;a.push(n.id)}}catch(o){r.e(o)}finally{r.f()}var s={};s.ids=a,s.sourceCluster=this.form.sourceCluster.cluster,s.sourceNamespace=this.form.sourceCluster.namespace,s.targetCluster=this.form.targetCluster.cluster,s.targetNamespace=this.form.targetCluster.namespace,Object(i["b"])(s).then((function(e){t.$message.success("操作成功"),t.$alert('<pre style="overflow: auto;font-size: 10px;line-height:10px;">'+JSON.stringify(e.data,null,2)+"</pre>","操作成功，结果:",{dangerouslyUseHTMLString:!0})})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))}}},u=o,c=a("2877"),p=Object(c["a"])(u,r,n,!1,null,"240bfd51",null);t["default"]=p.exports},a68b:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},n=[],l=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),i=l,s=(a("d54f"),a("2877")),o=Object(s["a"])(i,r,n,!1,null,null,null);t["a"]=o.exports},d54f:function(e,t,a){"use strict";a("eba1")},eba1:function(e,t,a){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0939a8a5"],{"018c":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"老集群（k8s1)",name:"old-k8s"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"新集群（k8s0)",name:"new-k8s"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用迁移处理",name:"migrate-operation"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程处理",name:"pipeline-batch-operation"}})],1)],1)},o=[],r=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"old-k8s"===a?this.$router.push({name:"pipeline-migrate-old"}):"new-k8s"===a?this.$router.push({name:"pipeline-migrate-new"}):"migrate-operation"===a?this.$router.push({name:"pipeline-migrate-operation"}):"pipeline-batch-operation"===a?this.$router.push({name:"pipeline-batch-operation"}):this.$message.error("未知操作")}}}),i=r,l=a("2877"),u=Object(l["a"])(i,n,o,!1,null,null,null);e["a"]=u.exports},"13fe":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"pipeline-resource-update"}}),t._v(" "),a("el-row",{staticStyle:{"max-width":"1480px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form",[a("el-form-item",{attrs:{label:"请输入参数"}},[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:12,maxRows:12}},model:{value:t.form,callback:function(e){t.form=e},expression:"form"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("提 交")])],1)],1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form",[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"参数格式模版（CPU单位：m，内存单位：Mi)"}}),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:""}},[a("div",{staticStyle:{"background-color":"#eee",overflow:"auto",height:"270px"}},[a("pre",{staticStyle:{"line-height":"normal"}},[t._v('[\n  {\n    "cluster": "k8s0",\n    "namespace": "fstest",\n    "app": "fs-k8s-tomcat-test",\n    "requestCPU": 200,\n    "limitCPU": 1000,\n    "requestMemory": -1,\n    "limitMemory": -1,\n    "replicas": -1\n  },\n  {\n    "cluster": "k8s0",\n    "namespace": "fstest",\n    "app": "fs-app-test",\n    "requestCPU": 100,\n    "limitCPU": 500,\n    "requestMemory": 128,\n    "limitMemory": 512,\n    "replicas": -1\n  },\n\n  }\n]\n            ')])])])],1)],1)],1),t._v(" "),t.result?a("div",{staticStyle:{"margin-top":"20px","max-width":"1480px"}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("操作结果")])]),t._v(" "),a("div",[a("pre",{staticStyle:{"font-size":"12px"}},[t._v(t._s(this.result))])])])],1):t._e()],1)},o=[],r=a("018c"),i=a("a68b"),l=a("c1ab"),u={components:{appManageTab:i["a"],PipelineMigrateTab:r["a"]},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{form:"",result:"",pageLoading:!1}},methods:{submit:function(){var t=this;this.$confirm("确定要继续修改发布流程资源吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pageLoading=!0,Object(l["s"])(t.form).then((function(e){t.result=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))})).catch((function(){}))}}},c=u,p=a("2877"),s=Object(p["a"])(c,n,o,!1,null,null,null);e["default"]=s.exports},a68b:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},o=[],r=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),i=r,l=(a("d54f"),a("2877")),u=Object(l["a"])(i,n,o,!1,null,null,null);e["a"]=u.exports},c1ab:function(t,e,a){"use strict";a.d(e,"i",(function(){return o})),a.d(e,"k",(function(){return r})),a.d(e,"y",(function(){return i})),a.d(e,"z",(function(){return l})),a.d(e,"d",(function(){return u})),a.d(e,"g",(function(){return c})),a.d(e,"C",(function(){return p})),a.d(e,"E",(function(){return s})),a.d(e,"x",(function(){return d})),a.d(e,"b",(function(){return m})),a.d(e,"f",(function(){return f})),a.d(e,"B",(function(){return b})),a.d(e,"D",(function(){return h})),a.d(e,"w",(function(){return v})),a.d(e,"F",(function(){return g})),a.d(e,"l",(function(){return y})),a.d(e,"e",(function(){return j})),a.d(e,"a",(function(){return O})),a.d(e,"A",(function(){return _})),a.d(e,"j",(function(){return x})),a.d(e,"h",(function(){return k})),a.d(e,"r",(function(){return $})),a.d(e,"u",(function(){return w})),a.d(e,"v",(function(){return C})),a.d(e,"n",(function(){return T})),a.d(e,"o",(function(){return S})),a.d(e,"s",(function(){return q})),a.d(e,"t",(function(){return P})),a.d(e,"c",(function(){return M})),a.d(e,"p",(function(){return N})),a.d(e,"q",(function(){return L})),a.d(e,"m",(function(){return U}));var n=a("b775");function o(t){return Object(n["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function r(t){return Object(n["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function i(t){return Object(n["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function l(t){return Object(n["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(n["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function c(t){return Object(n["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(n["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,a,o,r,i,l,u){return Object(n["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(r,"&fixVersion=").concat(e,"&suffixVersion=").concat(a,"&message=").concat(o,"&dependencyCheck=").concat(i,"&parentPom=").concat(l),method:"post",data:u})}function b(t){return Object(n["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function h(t){return Object(n["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function v(t){return Object(n["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,a,o){return Object(n["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&overrideNamespace=").concat(o),method:"post"})}function j(t,e,a,o,r){return Object(n["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(a,"&remark=").concat(o,"&dryRun=").concat(r),method:"post"})}function O(){return Object(n["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function _(){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function x(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function k(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function $(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function w(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function C(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function T(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function S(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function q(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function P(t,e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function M(t){return Object(n["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function N(t){return Object(n["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function L(t){return Object(n["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function U(t,e,a){return Object(n["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(a),method:"get"})}},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);
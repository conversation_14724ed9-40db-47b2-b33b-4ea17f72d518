(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6826591f"],{"018c":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"老集群（k8s1)",name:"old-k8s"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"新集群（k8s0)",name:"new-k8s"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用迁移处理",name:"migrate-operation"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程处理",name:"pipeline-batch-operation"}})],1)],1)},r=[],i=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;"old-k8s"===a?this.$router.push({name:"pipeline-migrate-old"}):"new-k8s"===a?this.$router.push({name:"pipeline-migrate-new"}):"migrate-operation"===a?this.$router.push({name:"pipeline-migrate-operation"}):"pipeline-batch-operation"===a?this.$router.push({name:"pipeline-batch-operation"}):this.$message.error("未知操作")}}}),o=i,u=a("2877"),p=Object(u["a"])(o,n,r,!1,null,null,null);t["a"]=p.exports},"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"e",(function(){return i})),a.d(t,"d",(function(){return o})),a.d(t,"l",(function(){return u})),a.d(t,"m",(function(){return p})),a.d(t,"a",(function(){return l})),a.d(t,"f",(function(){return c})),a.d(t,"i",(function(){return s})),a.d(t,"j",(function(){return d})),a.d(t,"k",(function(){return m})),a.d(t,"n",(function(){return f})),a.d(t,"g",(function(){return b})),a.d(t,"b",(function(){return v})),a.d(t,"h",(function(){return h})),a.d(t,"o",(function(){return g}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function o(e,t,a){return Object(n["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function u(e){return Object(n["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function l(){return Object(n["a"])({url:"/v1/pipeline/all",method:"get"})}function c(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function s(e){return Object(n["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(n["a"])({url:"/v1/pipeline",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function f(e){return Object(n["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function b(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function v(e){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function h(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function g(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"5ad3":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("pipeline-migrate-tab",{attrs:{"active-name":"old-k8s"}}),e._v(" "),a("el-dialog",{attrs:{title:"格式化内容",visible:e.dialogVisible,width:"50%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticStyle:{"margin-top":"-50px",overflow:"auto"}},[a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-document-copy"},on:{click:e.copyToClipboard}},[e._v("一键复制内容")])],1),e._v(" "),a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[e._v(e._s(e.exportData))])])]),e._v(" "),a("div",{staticStyle:{"text-align":"center","margin-top":"-10px"}},[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){e.dialogVisible=!0}}},[e._v("查看格式化内容")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"数据加载中...","default-sort":{prop:"status",order:"ascending"},border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",sortable:"",prop:"cluster"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.cluster)+" / "+e._s(t.row.namespace)+"\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"持久存储"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v("\n          "+e._s(t.row.pvc.enable?t.row.pvc.name:"-")+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"资源池",prop:"resourcePool"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用信息",prop:"appRemark",width:"320px;"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"12px","line-height":"14px"}},[t.row.appRemark?a("div",[e._v("\n            描述："+e._s(t.row.appRemark)+"\n          ")]):e._e(),e._v(" "),t.row.appOwner?a("div",[e._v("\n            Owner："+e._s(t.row.appOwner)+"\n          ")]):e._e()])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"状态",width:"100px",align:"center",filters:[{text:"正常",value:"enabled"},{text:"已迁移",value:"migrated"},{text:"待审核",value:"待审核"}],"filter-method":e.filterStatus},scopedSlots:e._u([{key:"default",fn:function(t){return["enabled"===t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("\n          "+e._s(e.convertStatus(t.row.status))+"\n        ")]):a("el-tag",{attrs:{type:"warning"}},[e._v("\n          "+e._s(e.convertStatus(t.row.status))+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"配置实例数",align:"center",prop:"replicas"}}),e._v(" "),a("el-table-column",{attrs:{label:"修改时间",prop:"updatedTime",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-position"},on:{click:function(a){return e.pipelinePage(t.row)}}},[e._v("发布流程页\n        ")])]}}])})],1)],1)},r=[],i=(a("7f7f"),a("2d63")),o=a("51a9"),u=a("b562"),p=a("018c"),l={components:{PipelineMigrateTab:p["a"]},data:function(){return{tableData:[],apps:{},tableLoading:!1,cmsUpdateVisible:!1,cmsUpdateOld:{cluster:"",namespace:"",app:"",text:""},cmsUpdateNew:{cluster:"",namespace:"",app:"",text:""},dialogVisible:!1}},computed:{exportData:function(){var e,t=[],a=Object(i["a"])(this.tableData);try{for(a.s();!(e=a.n()).done;){var n=e.value;t.push({cluster:n.cluster,namespace:n.namespace,app:n.app,appRemark:n.appRemark,appOwner:n.appOwner,status:n.status,replicas:n.replicas,pvc:n.pvc.enable?n.pvc.name:"-",resourcePool:n.resourcePool,updatedTime:n.updatedTime})}}catch(r){a.e(r)}finally{a.f()}return JSON.stringify(t,null,2)}},mounted:function(){this.loadAppAndPipeline()},methods:{loadAppAndPipeline:function(){var e=this;this.tableLoading=!0,Object(u["p"])({keyword:"",page:1,limit:1e4}).then((function(t){var a,n=Object(i["a"])(t.data.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;e.apps[r.name]=r}}catch(o){n.e(o)}finally{n.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1,e.loadTableData()}))},loadTableData:function(){var e=this;this.tableLoading=!0,Object(o["l"])({cluster:"k8s1",page:1,limit:1e4}).then((function(t){e.tableData=t.data.data,console.log(e.tableData);var a,n=Object(i["a"])(e.tableData);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.appRemark="",r.appOwner="";var o=e.apps[r.app];o&&(r.appRemark=o.remark,o.admins&&(r.appOwner=o.admins.join(",")),r.appOwner=r.appOwner+(r.appOwner&&o.owner?",":"")+o.owner),r.resourcePool=r.schedule.node?r.schedule.node:"-"}}catch(u){n.e(u)}finally{n.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},pipelinePage:function(e){this.$router.push({name:"cicd-app-deploy",query:{app:e.app}})},filterStatus:function(e,t){return t.status===e},convertStatus:function(e){switch(e){case"enabled":return"正常";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}},copyToClipboard:function(){var e=this,t=this.exportData;t?navigator.clipboard.writeText(t).then((function(){e.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",t),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),e.$message.success("复制成功")})):this.$message.warning("内容为空")}}},c=l,s=a("2877"),d=Object(s["a"])(c,n,r,!1,null,null,null);t["default"]=d.exports},b562:function(e,t,a){"use strict";a.d(t,"p",(function(){return r})),a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"l",(function(){return u})),a.d(t,"j",(function(){return p})),a.d(t,"d",(function(){return l})),a.d(t,"i",(function(){return c})),a.d(t,"h",(function(){return s})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"e",(function(){return b})),a.d(t,"c",(function(){return v})),a.d(t,"k",(function(){return h})),a.d(t,"q",(function(){return g})),a.d(t,"n",(function(){return w})),a.d(t,"g",(function(){return O}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/app/search",method:"get",params:e})}function i(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function o(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function u(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function p(e){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function l(e){return Object(n["a"])({url:"/v1/app",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/v1/app",method:"put",data:e})}function s(e){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:a}})}function m(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function f(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function h(e,t){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function g(e,t){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function w(e,t){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function O(e){return Object(n["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}}}]);
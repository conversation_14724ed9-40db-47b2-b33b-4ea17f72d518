(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a6c57e06"],{"1e42":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline"}},[n("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},o=[],r=(n("a481"),n("25ca")),i=n("21a6"),l=n.n(i),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=r["a"].table_to_book(t,{raw:!0}),n=r["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var a="export-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";l.a.saveAs(new Blob([n],{type:"application/octet-stream"}),a)}catch(o){this.$message.error("导出失败, err: "+o.message),console.error(o)}return n}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=n("2877"),p=Object(u["a"])(c,a,o,!1,null,null,null);e["a"]=p.exports},"4a95":function(t,e,n){"use strict";n("eb2f")},c002:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-alert",{staticStyle:{"font-size":"1.2em","background-color":"unset",border:"solid 1px #898282","margin-bottom":"10px","font-weight":"bold",color:"#333"},attrs:{title:"应用负责人管理页面",type:"info",closable:!1,description:"","show-icon":""}},[[n("div",{staticStyle:{"font-weight":"bold",color:"#333"}},[t._v("\n        发布系统、监控告警系统会使用这些负责人信息。\n      ")])]],2),t._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[t._v("应用负责人")]),n("span",{staticStyle:{"font-size":"14px"}},[t._v("(数据存储于配置中心的 cmdb-mark-v2.json 文件）")]),t._v(" "),n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.toEditPage}},[t._v("去修改")]),t._v(" "),n("export-button",{staticStyle:{float:"right"},attrs:{"table-ref":this.$refs.table001}})],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table001",staticStyle:{width:"100%"},attrs:{data:t.tableData}},[n("el-table-column",{attrs:{prop:"service",label:"应用名称"}}),t._v(" "),n("el-table-column",{attrs:{prop:"mainOwner",label:"主负责人"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.mainOwner?e.row.mainOwner.join(", "):"")+"\n        ")]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"owners",label:"负责人"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.owners?e.row.owners.join(", "):"")+"\n        ")]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"desc",label:"描述"}})],1)],1)],1)},o=[],r=n("c356"),i=n("1e42"),l={name:"AppOwnerManage",components:{ExportButton:i["a"]},data:function(){return{loading:!1,tableData:[]}},created:function(){this.loadTable()},methods:{loadTable:function(){var t=this;this.loading=!0,Object(r["b"])().then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},toEditPage:function(){window.open("https://oss.foneshare.cn/cms/edit/config/25292","_blank")}}},s=l,c=(n("4a95"),n("2877")),u=Object(c["a"])(s,a,o,!1,null,"346bf756",null);e["default"]=u.exports},c356:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return r}));var a=n("b775");function o(t){return Object(a["a"])({url:"/operation/app-version-history",method:"get",params:t})}function r(){return Object(a["a"])({url:"/operation/cmdb-owner",method:"get"})}},eb2f:function(t,e,n){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e31027f"],{"4f2b":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"app-build-v2"}}),t._v(" "),a("el-alert",{attrs:{title:"应用批量构建",type:"info",description:"使用当前运行的版本重新构建镜像。","show-icon":""}}),t._v(" "),a("el-form",{staticStyle:{"max-width":"800px"},attrs:{"label-width":"120px"}},[a("el-form-item",{staticStyle:{margin:"3px"},attrs:{label:"查询条件"}},[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"内容格式：集群/环境/应用名，多个之间用换行分割"},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1),t._v(" "),a("el-form-item",{staticStyle:{margin:"3px"},attrs:{label:"构建备注"}},[a("el-input",{model:{value:t.buildRemark,callback:function(e){t.buildRemark=e},expression:"buildRemark"}})],1),t._v(" "),a("el-form-item",{staticStyle:{margin:"3px","text-align":"right"}},[a("el-button",{staticStyle:{margin:"3px"},attrs:{type:"primary",size:"small"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1),t._v(" "),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("el-table",{attrs:{data:t.tableData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"构建过？",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.extraAttr.reDeployed)+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.reBuild(e.row)}}},[t._v("使用当前版本构建\n            ")]),t._v(" "),a("br"),t._v(" "),a("router-link",{attrs:{to:{name:"cicd-image-build-history",query:{}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n                历史\n              ")])],1),t._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n                发布流程\n              ")])],1)],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"app"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"10px"}},[a("div",[t._v(t._s(e.row.app))]),t._v(" "),a("div",{staticStyle:{size:"10px",color:"#999"}},[t._v(t._s(e.row.namespace)+" ("+t._s(e.row.cluster)+")")])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"100px"}}),t._v(" "),a("el-table-column",{attrs:{label:"模块数",prop:"status",width:"80px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v(t._s(e.row.appModules.length))]),t._v(" "),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.contentShow(e.row)}}},[t._v("查看\n            ")])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"模块镜像"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"10px"}},[a("div",[t._v(t._s(e.row.extraAttr.deployModuleImages))])])]}}])})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:t.contentDialog.title,visible:t.contentDialog.visible,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.contentDialog,"visible",e)}}},[a("div",[a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[t._v(t._s(this.contentDialog.content))])])])],1)},r=[],o=a("2d63"),i=a("a68b"),l=a("51a9"),u=a("8504"),p=a("76fe"),c={components:{appManageTab:i["a"]},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{pageLoading:!1,searchForm:"",buildRemark:"",tableData:[],contentDialog:{visible:!1,title:"",content:""}}},methods:{loadTableData:function(){var t=this;this.pageLoading=!0,this.tableData=[],Object(l["m"])(this.searchForm).then((function(e){var a,n=Object(o["a"])(e.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.extraAttr.deployModuleImages="?",r.extraAttr.reDeployed="NO",r.extraAttr.deployModules="?",t.findDeployment(r)}}catch(i){n.e(i)}finally{n.f()}t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))},findDeployment:function(t){null!==t?Object(u["c"])(t.cluster,t.namespace,t.app).then((function(e){if(e.data.spec.template.spec.initContainers.length>0){var a,n=[],r=Object(o["a"])(e.data.spec.template.spec.initContainers);try{for(r.s();!(a=r.n()).done;){var i=a.value;n.push(i.image)}}catch(l){r.e(l)}finally{r.f()}t.extraAttr.deployModuleImages=n.join("<br/>"),t.extraAttr.deployModules=e.data.spec.template.spec.initContainers}})).catch((function(t){console.log(t.message)})):this.$message.warning("找不到发布流程，应用：".concat(cluster," / ").concat(namespace," / ").concat(app))},reBuild:function(t){var e=this;Object(p["c"])(t.cluster,t.namespace,t.app,this.buildRemark).then((function(a){e.$message.success("操作成功"),t.extraAttr.reDeployed="YES"})).catch((function(t){e.$message.error(t.message)}))},contentShow:function(t){this.contentDialog.content=t.extraAttr.deployModules,this.contentDialog.title="应用："+t.app,this.contentDialog.visible=!0}}},s=c,d=a("2877"),m=Object(d["a"])(s,n,r,!1,null,null,null);e["default"]=m.exports},"51a9":function(t,e,a){"use strict";a.d(e,"c",(function(){return r})),a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return i})),a.d(e,"l",(function(){return l})),a.d(e,"m",(function(){return u})),a.d(e,"a",(function(){return p})),a.d(e,"f",(function(){return c})),a.d(e,"i",(function(){return s})),a.d(e,"j",(function(){return d})),a.d(e,"k",(function(){return m})),a.d(e,"n",(function(){return b})),a.d(e,"g",(function(){return f})),a.d(e,"b",(function(){return h})),a.d(e,"h",(function(){return v})),a.d(e,"o",(function(){return g}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(n["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function i(t,e,a){return Object(n["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:a}})}function l(t){return Object(n["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function p(){return Object(n["a"])({url:"/v1/pipeline/all",method:"get"})}function c(t){return Object(n["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function s(t){return Object(n["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(n["a"])({url:"/v1/pipeline",method:"post",data:t})}function m(t){return Object(n["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function b(t){return Object(n["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function f(t,e,a,r){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:r}})}function h(t){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function v(t,e,a,r){return Object(n["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:r}})}function g(t){return Object(n["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"76fe":function(t,e,a){"use strict";a.d(e,"k",(function(){return r})),a.d(e,"i",(function(){return o})),a.d(e,"a",(function(){return i})),a.d(e,"e",(function(){return l})),a.d(e,"b",(function(){return u})),a.d(e,"f",(function(){return p})),a.d(e,"c",(function(){return c})),a.d(e,"g",(function(){return s})),a.d(e,"h",(function(){return d})),a.d(e,"d",(function(){return m})),a.d(e,"l",(function(){return b})),a.d(e,"j",(function(){return f}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/job/search",method:"post",data:t})}function o(t){return Object(n["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function i(t){return Object(n["a"])({url:"/v1/job/build-image",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function p(t,e,a,r){return Object(n["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:r}})}function c(t,e,a,r){return Object(n["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:r}})}function s(t){return Object(n["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(n["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(n["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function b(t,e){return Object(n["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function f(t){return Object(n["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},8504:function(t,e,a){"use strict";a.d(e,"g",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"h",(function(){return i})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return u})),a.d(e,"i",(function(){return p})),a.d(e,"d",(function(){return c})),a.d(e,"f",(function(){return s})),a.d(e,"e",(function(){return d}));var n=a("b775");function r(t,e){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:a}})}function i(t){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function l(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:a}})}function u(t){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function p(t){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function c(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:a}})}function s(t){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,a,r,o){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:a,revision:r,deployTag:o||""}})}},a68b:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},r=[],o=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var a=t.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),i=o,l=(a("d54f"),a("2877")),u=Object(l["a"])(i,n,r,!1,null,null,null);e["a"]=u.exports},d54f:function(t,e,a){"use strict";a("eba1")},eba1:function(t,e,a){}}]);
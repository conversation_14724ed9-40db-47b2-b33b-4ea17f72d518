(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-68e39c2d"],{a68b:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},l=[],o=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),n=o,i=(a("d54f"),a("2877")),s=Object(i["a"])(n,r,l,!1,null,null,null);t["a"]=s.exports},d54f:function(e,t,a){"use strict";a("eba1")},eba1:function(e,t,a){},f0df:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"app-cron-reboot"}}),e._v(" "),a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"260px"},model:{value:e.searchForm.keyword,callback:function(t){e.$set(e.searchForm,"keyword","string"===typeof t?t.trim():t)},expression:"searchForm.keyword"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.showEditDialog()}}},[e._v("新建\n      ")])],1)],1),e._v(" "),e._m(0),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"所在集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"重启时间点",align:"center",prop:"rebootHour",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        每天的 "),a("b",[e._v(e._s(t.row.rebootHour))]),e._v(" 点\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建人",align:"center",prop:"author"}}),e._v(" "),a("el-table-column",{attrs:{label:"备注",prop:"remark","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(a){return e.deleteReboot(t.row)}}},[a("el-button",{staticStyle:{"margin-right":"5px"},attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[e._v("删除\n          ")])],1)]}}])})],1),e._v(" "),a("el-dialog",{attrs:{title:"创建服务重启任务",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:e.editForm,"label-width":"120px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-input",{model:{value:e.editForm.app,callback:function(t){e.$set(e.editForm,"app","string"===typeof t?t.trim():t)},expression:"editForm.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"k8s集群"}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.clusterChange},model:{value:e.editForm.cluster,callback:function(t){e.$set(e.editForm,"cluster",t)},expression:"editForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.namespace,callback:function(t){e.$set(e.editForm,"namespace",t)},expression:"editForm.namespace"}},e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"重启时间点"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.rebootHour,callback:function(t){e.$set(e.editForm,"rebootHour",t)},expression:"editForm.rebootHour"}},e._l([23,0,1,2,3,4,5,6],(function(e){return a("el-option",{key:e,attrs:{label:e+"点",value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{model:{value:e.editForm.remark,callback:function(t){e.$set(e.editForm,"remark","string"===typeof t?t.trim():t)},expression:"editForm.remark"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.submitLoading,expression:"submitLoading"}],attrs:{type:"primary"},on:{click:function(t){return e.create()}}},[e._v("确 定")])],1)],1)],1)},l=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[a("tr",[a("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("定时重启")]),e._v(" "),a("td",{staticStyle:{width:"1000px"}},[e._v("\n        服务会在配置的时间点上进行重启，按批重启所有实例，每批启动25%的实例\n      ")])])])}],o=(a("7f7f"),a("2d63")),n=a("b775");function i(e){return Object(n["a"])({url:"/v1/reboot?keyword="+e,method:"get"})}function s(e){return Object(n["a"])({url:"/v1/reboot",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/v1/reboot",method:"delete",params:{id:e}})}var p=a("a68b"),u={components:{appManageTab:p["a"]},mounted:function(){this.loadTableData()},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.editForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.editForm.cluster===a.name)return a.namespaces}}catch(r){t.e(r)}finally{t.f()}}return[]}},data:function(){return{searchForm:{keyword:""},editForm:{},editFormRules:{app:[{required:!0,message:"值不能为空",trigger:"blur"}],cluster:[{required:!0,message:"值不能为空",trigger:"blur"}],namespace:[{required:!0,message:"值不能为空",trigger:"blur"}],rebootHour:[{required:!0,message:"值不能为空",trigger:"blur"}]},tableData:[],tableLoading:!1,dialogVisible:!1,submitLoading:!1,appOptions:[]}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,i(this.searchForm.keyword).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},editFormReset:function(){this.editForm={app:"",cluster:"",namespace:"",rebootHour:23,remark:""}},clusterChange:function(){this.editForm.namespace=""},showEditDialog:function(){this.editFormReset(),this.dialogVisible=!0},deleteReboot:function(e){var t=this;c(e.id).then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))},create:function(){var e=this;this.$refs["dialogEditForm"].validate((function(t){if(!t)return!1;e.submitLoading=!0,s(e.editForm).then((function(t){e.dialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.submitLoading=!1}))}))}}},m=u,d=a("2877"),b=Object(d["a"])(m,r,l,!1,null,null,null);t["default"]=b.exports}}]);
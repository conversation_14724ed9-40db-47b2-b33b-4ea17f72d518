(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f6384076"],{"02f4":function(t,e,a){var o=a("4588"),n=a("be13");t.exports=function(t){return function(e,a){var i,s,r=String(n(e)),l=o(a),c=r.length;return l<0||l>=c?t?"":void 0:(i=r.charCodeAt(l),i<55296||i>56319||l+1===c||(s=r.charCodeAt(l+1))<56320||s>57343?t?r.charAt(l):i:t?r.slice(l,l+2):s-56320+(i-55296<<10)+65536)}}},"0390":function(t,e,a){"use strict";var o=a("02f4")(!0);t.exports=function(t,e,a){return e+(a?o(t,e).length:1)}},"0bfb":function(t,e,a){"use strict";var o=a("cb7c");t.exports=function(){var t=o(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},1262:function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.eventsLoading,expression:"eventsLoading"}],staticClass:"pod-event",attrs:{"element-loading-text":"事件加载中..."}},[t.events&&t.events.length>0?a("div",[a("el-timeline",t._l(t.events,(function(e,o){return a("el-timeline-item",{key:o,attrs:{placement:"top","hide-timestamp":!0,timestamp:e.lastTime?e.lastTime:e.createTime}},[a("div",[a("span",[t._v(t._s(e.lastTime?e.lastTime:e.createTime))]),t._v(" "),a("el-tag",{staticStyle:{margin:"0 10px"},attrs:{size:"small",type:"Warning"===e.type?"warning":"info"}},[t._v(t._s(e.type))]),t._v(" "),a("span",{staticStyle:{padding:"0 10px"}},[t._v(" ( x"+t._s(e.count)+" )")]),t._v(" "),a("span",{staticStyle:{"padding-right":"10px"}},[t._v(t._s(e.reason)+":")]),t._v(" "+t._s(e.message)+"\n        ")],1)])})),1)],1):t.events&&0===t.events.length?a("div",[a("el-empty",{attrs:{description:"暂无事件"}})],1):t._e()])},n=[],i=(a("6762"),a("2fdb"),a("a527")),s={props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!0},hiddenStartUpEvent:{type:Boolean,default:!0}},created:function(){var t=this;this.$parent.$el&&this.$parent.$el.offsetParent&&this.$parent.$el.offsetParent.className&&this.$parent.$el.offsetParent.className.includes("fixed")||this.$nextTick((function(){t.loadEvents()}))},computed:{},data:function(){return{events:null,eventsLoading:!1}},methods:{loadEvents:function(){var t=this;this.eventsLoading=!0,Object(i["n"])(this.cluster,this.namespace,this.pod).then((function(e){var a=e.data;t.hiddenStartUpEvent&&(a=a.filter((function(t){return!t.message.includes("Startup probe failed")}))),t.events=a})).catch((function(e){t.$message.error("pod事件加载失败: "+e.message)})).finally((function(){t.eventsLoading=!1}))}}},r=s,l=a("2877"),c=Object(l["a"])(r,o,n,!1,null,null,null);e["a"]=c.exports},"209a":function(t,e,a){},"214f":function(t,e,a){"use strict";a("b0c5");var o=a("2aba"),n=a("32e9"),i=a("79e5"),s=a("be13"),r=a("2b4c"),l=a("520a"),c=r("species"),p=!i((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),d=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var a="ab".split(t);return 2===a.length&&"a"===a[0]&&"b"===a[1]}();t.exports=function(t,e,a){var u=r(t),m=!i((function(){var e={};return e[u]=function(){return 7},7!=""[t](e)})),h=m?!i((function(){var e=!1,a=/a/;return a.exec=function(){return e=!0,null},"split"===t&&(a.constructor={},a.constructor[c]=function(){return a}),a[u](""),!e})):void 0;if(!m||!h||"replace"===t&&!p||"split"===t&&!d){var v=/./[u],f=a(s,u,""[t],(function(t,e,a,o,n){return e.exec===l?m&&!n?{done:!0,value:v.call(e,a,o)}:{done:!0,value:t.call(a,e,o)}:{done:!1}})),g=f[0],b=f[1];o(String.prototype,t,g),n(RegExp.prototype,u,2==e?function(t,e){return b.call(t,this,e)}:function(t){return b.call(t,this)})}}},"28a5":function(t,e,a){"use strict";var o=a("aae3"),n=a("cb7c"),i=a("ebd6"),s=a("0390"),r=a("9def"),l=a("5f1b"),c=a("520a"),p=a("79e5"),d=Math.min,u=[].push,m="split",h="length",v="lastIndex",f=4294967295,g=!p((function(){RegExp(f,"y")}));a("214f")("split",2,(function(t,e,a,p){var b;return b="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[h]||2!="ab"[m](/(?:ab)*/)[h]||4!="."[m](/(.?)(.?)/)[h]||"."[m](/()()/)[h]>1||""[m](/.?/)[h]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!o(t))return a.call(n,t,e);var i,s,r,l=[],p=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,m=void 0===e?f:e>>>0,g=new RegExp(t.source,p+"g");while(i=c.call(g,n)){if(s=g[v],s>d&&(l.push(n.slice(d,i.index)),i[h]>1&&i.index<n[h]&&u.apply(l,i.slice(1)),r=i[0][h],d=s,l[h]>=m))break;g[v]===i.index&&g[v]++}return d===n[h]?!r&&g.test("")||l.push(""):l.push(n.slice(d)),l[h]>m?l.slice(0,m):l}:"0"[m](void 0,0)[h]?function(t,e){return void 0===t&&0===e?[]:a.call(this,t,e)}:a,[function(a,o){var n=t(this),i=void 0==a?void 0:a[e];return void 0!==i?i.call(a,n,o):b.call(String(n),a,o)},function(t,e){var o=p(b,t,this,e,b!==a);if(o.done)return o.value;var c=n(t),u=String(this),m=i(c,RegExp),h=c.unicode,v=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(g?"y":"g"),_=new m(g?c:"^(?:"+c.source+")",v),y=void 0===e?f:e>>>0;if(0===y)return[];if(0===u.length)return null===l(_,u)?[u]:[];var x=0,w=0,S=[];while(w<u.length){_.lastIndex=g?w:0;var C,k=l(_,g?u:u.slice(w));if(null===k||(C=d(r(_.lastIndex+(g?0:w)),u.length))===x)w=s(u,w,h);else{if(S.push(u.slice(x,w)),S.length===y)return S;for(var L=1;L<=k.length-1;L++)if(S.push(k[L]),S.length===y)return S;w=x=C}}return S.push(u.slice(x)),S}]}))},46305:function(t,e,a){},"4ad4":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-address-wrapper"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("访问地址")]),t._v(" "),a("span",{staticStyle:{display:"inline-block","margin-left":"20px","font-size":"13px"}},[t._v("\n        ( 应用："+t._s(this.app)+" | 环境："+t._s(this.namespace)+" | 集群："+t._s(this.cluster)+" )\n      ")])]),t._v(" "),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.address}},[a("el-table-column",{attrs:{prop:"name",width:"180",label:"端口名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"port",width:"180",label:"端口号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"protocol",width:"100",label:"协议"}}),t._v(" "),a("el-table-column",{attrs:{label:"集群内访问地址"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.clusterInnerAddress,(function(e,o){return a("p",[a("b",{staticStyle:{"padding-right":"10px"}},[t._v("地址"+t._s(o+1)+":")]),t._v(t._s(e)+"\n            ")])}))}}])}),t._v(" "),a("el-table-column",{attrs:{label:"集群外访问地址"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.clusterOuterAddress,(function(o,n){return a("p",[a("b",{staticStyle:{"padding-right":"10px"}},[t._v("地址"+t._s(n+1)+":")]),t._v(t._s(o)+"\n              "),e.row.clusterOuterAddress&&e.row.clusterOuterAddress.length>1&&0===n?a("span",{staticStyle:{"padding-left":"20px"}},[t._v("\n                  (推荐)\n                ")]):t._e()])}))}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{domProps:{innerHTML:t._s(e.row.remark)}})]}}])})],1)],1)])],1)},n=[],i=a("b562"),s={name:"AppAddress",props:{cluster:{type:String,require:!0},namespace:{type:String,require:!0},app:{type:String,require:!0}},data:function(){return{loading:!1,address:[]}},watch:{cluster:function(t){this.showAddress()},namespace:function(t){this.showAddress()},app:function(t){this.showAddress()}},computed:{},mounted:function(){this.showAddress()},methods:{showAddress:function(){var t=this;this.cluster?this.namespace?this.app?(this.loading=!0,Object(i["m"])(this.cluster,this.namespace,this.app).then((function(e){t.address=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.warning("缺少参数 app"):this.$message.warning("缺少参数 namespace"):this.$message.warning("缺少参数 cluster")}}},r=s,l=(a("de9b"),a("2877")),c=Object(l["a"])(r,o,n,!1,null,null,null);e["a"]=c.exports},"4db0":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container pod-index"},[a("el-card",{staticClass:"box-card",attrs:{"body-style":{padding:"4px",overflow:"auto",maxHeight:"600px"}}},[a("div",[a("app-selector2",{attrs:{"show-detail":!1,"update-history":!0},on:{change:t.currAppChange}})],1)]),t._v(" "),a("div",[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.envPageLoading,expression:"envPageLoading"}],staticClass:"env-selector",staticStyle:{position:"absolute",width:"180px"}},[a("el-card",{staticClass:"box-card",attrs:{"body-style":{padding:"4px",overflow:"auto",height:"560px"}}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-size":"14px"}},[t._v("\n            运行环境\n              "),a("el-tooltip",{attrs:{placement:"right",effect:"dark",width:"480",content:" hover的内容格式为：环境 | 集群 | 集群描述 | 配置副本数 | 发布流程状态"}},[a("i",{staticClass:"el-icon-warning-outline",staticStyle:{"margin-left":"5px"}})])],1)]),t._v(" "),a("div",{staticStyle:{"margin-top":"10px"}},[t.envSelectorDesc?a("div",[a("el-alert",{staticStyle:{margin:"10px 0",padding:"5px 0"},attrs:{title:this.envSelectorDesc,type:"error",closable:!1}})],1):t._e(),t._v(" "),a("el-radio-group",{on:{input:t.envSelectedChange},model:{value:t.envSelected,callback:function(e){t.envSelected=e},expression:"envSelected"}},t._l(t.currAppEnvs,(function(e){return a("el-tooltip",{attrs:{effect:"dark",content:e.namespace+" | "+e.cluster+" | "+e.clusterDesc+" | "+e.replicas+" | "+e.pipelineStatus,placement:"right"}},[a("div",[a("el-radio",{attrs:{label:e.cluster+"/"+e.namespace}},[a("span",{domProps:{innerHTML:t._s(e.envDesc)}}),t._v(" "),e.pipelineStatus&&"正常"!==e.pipelineStatus?a("span",{staticStyle:{color:"orangered","font-size":"10px"}},[t._v("("+t._s(e.pipelineStatus)+")")]):t._e()])],1)])})),1)],1)])],1),t._v(" "),a("div",{staticStyle:{"margin-left":"185px"}},[a("el-tabs",{staticClass:"pod-index-tabs",staticStyle:{"margin-top":"10px","min-height":"600px"},attrs:{type:"border-card","active-name":this.currTab},on:{"tab-click":t.tabChange}},[a("el-tab-pane",{attrs:{name:"pod",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"application"}}),t._v("实例管理")],1),t._v(" "),t.appIsSelected?a("div",{staticStyle:{padding:"10px"}},[a("pod-list",{attrs:{cluster:this.currCluster,namespace:this.currNamespace,app:this.currApp}})],1):a("div",[a("div",{staticStyle:{padding:"30px 20px",color:"#666","font-size":"14px"}},[t._v("请先选择运行环境")])])]),t._v(" "),a("el-tab-pane",{attrs:{name:"appEvent",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"event"}}),t._v("应用事件")],1),t._v(" "),t.appIsSelected?a("div",{staticStyle:{padding:"10px"}},[a("app-event",{attrs:{cluster:this.currCluster,namespace:this.currNamespace,app:this.currApp}})],1):a("div",[a("div",{staticStyle:{padding:"30px 20px",color:"#666","font-size":"14px"}},[t._v("请先选择运行环境")])])]),t._v(" "),a("el-tab-pane",{attrs:{name:"appLog",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("i",{staticClass:"el-icon-tickets",staticStyle:{"margin-right":"2px"}}),t._v("Logback日志")]),t._v(" "),t.appIsSelected?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tabIframeLoading,expression:"tabIframeLoading"}],staticStyle:{position:"relative",margin:"-24px"}},[a("iframe",{staticStyle:{"border-width":"0",padding:"0",margin:"0",width:"100%","min-height":"1200px"},attrs:{src:this.appLogUrl}}),t._v(" "),a("el-button",{staticStyle:{position:"absolute",right:"30px",top:"20px"},attrs:{type:"text",icon:"el-icon-link"},on:{click:function(e){return t.clickhouseLogPage("app_log","")}}},[t._v("新页面打开\n            ")])],1):a("div",[a("div",{staticStyle:{padding:"30px 20px",color:"#666","font-size":"14px"}},[t._v("请先选择运行环境")])])]),t._v(" "),a("el-tab-pane",{attrs:{name:"tomcatAccessLog",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"tomcat"}}),t._v("Tomcat访问日志")],1),t._v(" "),t.appIsSelected?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tabIframeLoading,expression:"tabIframeLoading"}],staticStyle:{position:"relative",margin:"-24px"}},[a("iframe",{staticStyle:{"border-width":"0",padding:"0",margin:"0",width:"100%","min-height":"1200px"},attrs:{src:this.tomcatAccessLogUrl}}),t._v(" "),a("el-button",{staticStyle:{position:"absolute",right:"30px",top:"20px"},attrs:{type:"text",icon:"el-icon-link"},on:{click:function(e){return t.clickhouseLogPage("tomcat_access","")}}},[t._v("新页面打开\n            ")])],1):a("div",[a("div",{staticStyle:{padding:"30px 20px",color:"#666","font-size":"14px"}},[t._v("请先选择运行环境")])])]),t._v(" "),a("el-tab-pane",{attrs:{name:"monitor",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"monitor"}}),t._v("资源监控")],1),t._v(" "),t.appIsSelected?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tabIframeLoading,expression:"tabIframeLoading"}],staticStyle:{position:"relative"}},[a("iframe",{staticStyle:{"border-width":"0",padding:"0",margin:"10px 0 0 0",width:"100%","min-height":"1200px"},attrs:{src:this.monitorUrl}}),t._v(" "),a("el-button",{staticStyle:{position:"absolute",right:"520px",top:"10px"},attrs:{type:"text",icon:"el-icon-link"},on:{click:function(e){return t.grafanaPage("")}}},[t._v("新页面打开\n            ")])],1):a("div",[a("div",{staticStyle:{padding:"30px 20px",color:"#666","font-size":"14px"}},[t._v("请先选择运行环境")])])]),t._v(" "),a("el-tab-pane",{attrs:{name:"doc",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),t._v("帮助文档")],1),t._v(" "),a("div",{staticStyle:{color:"#444","font-size":"14px","padding-left":"10px","line-height":"40px"}},[t._v("\n            ◉ 服务日志接入：\n            "),a("el-link",{attrs:{href:"https://u9icsn6fl7.feishu.cn/docx/FFVVdph88o7RPFxH32ecwkPCnEf",target:"_blank",type:"primary"}},[t._v("文档链接")])],1),t._v(" "),a("div",{staticStyle:{color:"#444","font-size":"14px","padding-left":"10px","line-height":"40px"}},[t._v("\n            ◉ 日志查询语法：日志查询条件使用的是 ClickHouse 原生 Where 子句语法。\n            "),a("el-link",{attrs:{href:"https://clickhouse.com/docs/zh/sql-reference/statements/select/where/",target:"_blank",type:"primary"}},[t._v("文档链接")])],1),t._v(" "),a("div",{staticStyle:{color:"#444","font-size":"14px","padding-left":"10px","line-height":"40px"}},[t._v("\n            ◉ 日志平台(ClickVisual)文档：\n            "),a("el-link",{attrs:{href:"https://clickvisual.gocn.vip/clickvisual/",target:"_blank",type:"primary"}},[t._v("文档链接")])],1)])],1)],1)])],1)},n=[],i=(a("7f7f"),a("28a5"),a("768b")),s=a("b562"),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.eventsLoading,expression:"eventsLoading"}],staticClass:"app-event"},[a("div",{staticStyle:{padding:"15px 0 30px","font-size":"14px",color:"#444","font-weight":"bold"}},[t._v("应用："+t._s(this.app)+" | 运行环境： "+t._s(this.namespace)+" | 集群： "+t._s(this.cluster))]),t._v(" "),this.events.length>0?a("el-timeline",t._l(t.events,(function(e,o){return a("el-timeline-item",{attrs:{timestamp:e.created+"（操作人："+e.author+")",placement:"top",color:"#409EFF"}},[a("p",[t._v(" "+t._s(e.content))])])})),1):a("div",{staticStyle:{color:"#888","font-size":"14px"}},[t._v("\n    无任何数据\n  ")])],1)},l=[],c=a("b775");function p(t,e,a,o,n){return Object(c["a"])({url:"/v1/event/query-by-app",method:"get",params:{cluster:t,namespace:e,app:a,lastId:o,limit:n}})}var d={props:{cluster:{type:String,default:""},namespace:{type:String,default:""},app:{type:String,default:""}},watch:{cluster:function(t){this.loadEvents()},namespace:function(t){this.loadEvents()},app:function(t){this.loadEvents()}},mounted:function(){this.loadEvents(this.cluster,this.namespace,this.app)},computed:{},data:function(){return{events:[],lastId:-1,eventsLoading:!1}},methods:{loadEvents:function(){var t=this;this.eventsLoading=!0,p(this.cluster,this.namespace,this.app,this.lastId,500).then((function(e){t.events=e.data})).catch((function(e){t.$message.error("加载事件信息失败: "+e.message)})).finally((function(){t.eventsLoading=!1}))}}},u=d,m=(a("db3d"),a("2877")),h=Object(m["a"])(u,r,l,!1,null,null,null),v=h.exports,f=a("6e36"),g=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"pod-list-container"},[a("pod-app",{attrs:{cluster:this.cluster,namespace:this.namespace,app:this.app,"app-high-light":!0}}),t._v(" "),a("el-card",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticClass:"box-card",staticStyle:{"margin-top":"10px"}},[a("div",[a("div",{staticClass:"app-btn-group",staticStyle:{float:"right","font-size":"12px",color:"#909399",height:"30px",position:"relative"}},["jacoco"===t.namespace?a("el-dropdown",{staticStyle:{"margin-right":"40px"},on:{command:t.jacocoDropdownCommand}},[a("span",{staticClass:"el-dropdown-link",staticStyle:{color:"#E6A23C"}},[t._v("\n            Jacoco覆盖率测试"),a("i",{staticClass:"el-icon-arrow-down"})]),t._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-orange",command:"reset"}},[t._v("重置覆盖率")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-orange",command:"reportTask"}},[t._v("生成覆盖率报告")])],1)],1):t._e(),t._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:this.app}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",icon:"el-icon-position"}},[a("span",{staticStyle:{"margin-left":"-5px"}},[t._v("去发布")])])],1),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:t.selectedPodShell}},[a("svg-icon",{attrs:{"icon-class":"console"}}),t._v("\n          进入所有容器\n        ")],1),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:t.appScaleDialog}},[a("i",{staticClass:"el-icon-coin"}),t._v("\n          扩缩容\n        ")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:t.showAddress}},[a("i",{staticClass:"el-icon-link"}),t._v("\n          访问地址\n        ")]),t._v(" "),a("el-dropdown",{staticStyle:{"margin-left":"10px"},on:{command:t.appBtnDropdownCommand}},[a("span",{staticClass:"el-dropdown-link"},[t._v("\n            更多操作"),a("i",{staticClass:"el-icon-arrow-down "})]),t._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{staticStyle:{display:"none"},attrs:{icon:"el-icon-coin",command:"scale"}},[t._v("扩缩容")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-refresh-right",command:"redeploy"}},[t._v("重启所有实例")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-refresh-left",command:"rollback"}},[t._v("回滚")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-cpu",command:"resourceUpdate"}},[t._v("临时调整资源")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-document-copy",command:"java_cms"}},[t._v("查看Java应用配置")])],1)],1),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.loadPod(!0)}}},[a("i",{staticClass:"el-icon-refresh"}),t._v("\n          刷新\n        ")])],1),t._v(" "),a("el-table",{attrs:{data:t.tableData,"element-loading-text":"Loading",fit:"","row-key":"name"},on:{"expand-change":t.tableExpandChange}},[a("div",{staticStyle:{"text-align":"center",color:"#e86e20"},attrs:{slot:"empty"},slot:"empty"},[t._v("\n          无任何Pod，应用可能没有部署到当前环境~\n        ")]),t._v(" "),a("el-table-column",{attrs:{type:"index",width:"40"}}),t._v(" "),a("el-table-column",{attrs:{type:"expand",label:"",width:"30"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("pod-expand",{attrs:{pod:t.row}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"实例名",prop:"name","min-width":"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{style:{color:"#214596"}},[t._v(t._s(e.row.name))]),t._v(" "),a("div",[e.row.deregister?a("el-tag",{attrs:{type:"warning",size:"mini",effect:"plain"}},[t._v("已摘除")]):t._e(),t._v(" "),e.row.versionRetain?a("el-tag",{attrs:{type:"info",size:"mini",effect:"plain"}},[t._v("版本保留")]):t._e()],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"110"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n            状态\n            "),a("el-tooltip",{attrs:{effect:"light",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("el-image",{staticStyle:{"max-width":"800px"},attrs:{src:"/images/pod-status.svg",alt:"pod状态"}})],1),t._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return[a("span",{class:t.podStatusClass(e.row.statusDesc)}),t._v("\n            "+t._s(e.row.statusDesc)+"\n          ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"运行版本",prop:"deployTag","show-overflow-tooltip":"","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.deployModules&&e.row.deployModules.length>1?a("div",{staticStyle:{display:"inline-block","margin-right":"5px"}},[a("el-popover",{attrs:{placement:"left",width:"860",trigger:"click"}},[a("el-table",{staticStyle:{"max-height":"480px","overflow-y":"auto"},attrs:{data:e.row.deployModules}},[a("el-table-column",{attrs:{type:"index",width:"40"}}),t._v(" "),a("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),t._v(" "),a("el-table-column",{attrs:{prop:"module",label:"子模块"}}),t._v(" "),a("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}}),t._v(" "),a("el-table-column",{attrs:{prop:"tag",label:"版本"}}),t._v(" "),a("el-table-column",{attrs:{prop:"commitIdShort",label:"提交ID"}})],1),t._v(" "),a("el-button",{staticStyle:{margin:"0",padding:"0","font-size":"12px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("详情")])],1)],1):t._e(),t._v("\n            "+t._s(e.row.deployTag)+"\n          ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"IP",width:"170",prop:"podIP"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"12px","line-height":"14px"}},[a("span",{staticStyle:{width:"50px",display:"inline-block"}},[t._v("pod:")]),t._v(t._s(e.row.podIP)+" "),a("br"),t._v(" "),a("span",{staticStyle:{width:"50px",display:"inline-block"}},[t._v("node:")]),t._v(t._s(e.row.hostIP)+" "),a("br"),t._v(" "),e.row.hostIP?a("span",{staticStyle:{width:"50px",display:"inline-block"}},[t._v("资源池:")]):t._e(),t._v(t._s(e.row.resourcePool?e.row.resourcePool:"Common")+"\n            ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{width:"120",align:"center",prop:"restartCount"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n            重启\n            "),a("el-tooltip",{attrs:{effect:"light",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v("\n                重启次数"),a("br"),t._v("重启原因（ExitCode）\n              ")]),t._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.restartCount))]),t._v(" "),e.row.restartCount>0?a("div",{staticStyle:{"font-size":"10px",color:"#888"}},[t._v(t._s(e.row.restartReason)+" ("+t._s(e.row.restartCode)+")")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"资源使用",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"padding-left":"36px",position:"relative"}},[a("div",{staticStyle:{position:"absolute",left:"0",top:"0","font-size":"13px","line-height":"13px"}},[t._v("CPU")]),t._v(" "),e.row.cpuUsage>0?a("el-progress",{attrs:{percentage:e.row.cpuUsagePercent,"stroke-width":8,"show-text":!0}}):a("span",[t._v("--")])],1),t._v(" "),a("div",{staticStyle:{"padding-left":"36px",position:"relative"}},[a("div",{staticStyle:{position:"absolute",left:"0",top:"0","font-size":"13px","line-height":"13px"}},[t._v("内存")]),t._v(" "),e.row.memoryUsage>0?a("el-progress",{attrs:{percentage:e.row.memoryUsagePercent,"stroke-width":8,"show-text":!0}}):a("span",[t._v("--")])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"进入容器",placement:"top"}},[a("el-button",{staticStyle:{padding:"5px","margin-left":"3px"},attrs:{circle:"",size:"small"},on:{click:function(a){return t.podShell(e.row)}}},[a("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"console"}})],1)],1),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"启动日志",placement:"top"}},[a("el-button",{staticStyle:{padding:"5px","margin-left":"3px"},attrs:{circle:"",size:"small"},on:{click:function(a){return t.podStdoutLog(e.row.name)}}},[a("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"log"}})],1)],1),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"日志目录",placement:"top"}},[a("el-button",{staticStyle:{padding:"5px","margin-left":"3px"},attrs:{circle:"",size:"small"},on:{click:function(a){return t.podLogFile(e.row.name)}}},[a("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"folder"}})],1)],1),t._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"资源监控",placement:"top"}},[a("el-button",{staticStyle:{padding:"5px","margin-left":"3px"},attrs:{circle:"",size:"small"},on:{click:function(a){return t.podMonitor(e.row.name)}}},[a("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"monitor"}})],1)],1),t._v(" "),a("el-dropdown",{staticStyle:{"margin-left":"10px"},on:{command:t.manageDropdownCommand}},[a("span",{staticClass:"el-dropdown-link"},[t._v("\n            更多"),a("i",{staticClass:"el-icon-arrow-down "})]),t._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",command:"delete##"+e.row.name}},[t._v("删除")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-scissors",command:"deregister##"+e.row.name}},[t._v("摘除\n                ")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-video-camera-solid",command:"openPyroscope##"+e.row.name}},[t._v("开启持续性能分析\n                ")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-video-camera",command:"viewPyroscope##"+e.row.name}},[t._v("查看持续性能分析\n                ")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-paperclip",command:"versionRetain##"+e.row.name}},[t._v("版本保留\n                ")]),t._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-setting",command:"detail##"+e.row.name}},[t._v("k8s部署配置")]),t._v(" "),a("el-dropdown-item",{staticStyle:{display:"none"},attrs:{icon:"el-icon-tickets",command:"chAppLog##"+e.row.name}},[t._v("\n                  Logback日志\n                ")]),t._v(" "),a("el-dropdown-item",{staticStyle:{display:"none"},attrs:{icon:"el-icon-tickets",command:"chTomcatAccess##"+e.row.name}},[t._v("\n                  Tomcat访问日志\n                ")])],1)],1)]}}])})],1)],1)]),t._v(" "),a("el-dialog",{attrs:{title:"容器启动日志（标准输出)",visible:t.podStdoutVisible,top:"5vh","close-on-click-modal":!1,width:"70%",center:""},on:{"update:visible":function(e){t.podStdoutVisible=e},close:function(e){t.podStdout.pod=null}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("pod-stdout",{attrs:{cluster:this.podStdout.cluster,namespace:this.podStdout.namespace,pod:this.podStdout.pod,containers:this.podStdout.containers}})],1)]),t._v(" "),a("el-dialog",{attrs:{title:"回滚历史列表 (应用："+this.app+")",visible:t.rollbackTableVisible},on:{"update:visible":function(e){t.rollbackTableVisible=e}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.rollbackTableLoading,expression:"rollbackTableLoading"}],attrs:{data:t.rollbackTableData,"default-sort":{prop:"createTime",order:"descending"},"element-loading-text":"Loading"}},[a("el-table-column",{attrs:{property:"deployTag",label:"版本号",sortable:""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.deployTag)+"\n          "),e.row.isCurrReplicaSet?a("span",{staticStyle:{color:"rgb(32 153 14)","font-weight":"bold","font-size":"12px"}},[t._v(" (当前版本)")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{property:"revision",label:"Revision编号",width:"110",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{property:"replicas",label:"运行副本",width:"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{property:"createTime",label:"发布时间",sortable:"",width:"160"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isCurrReplicaSet?t._e():a("el-button",{attrs:{icon:"el-icon-refresh-left",type:"text"},on:{click:function(a){return t.appRollback(e.row)}}},[t._v("回滚至该版本\n          ")])]}}])})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"应用扩缩容",visible:t.scaleVisible,width:"30%"},on:{"update:visible":function(e){t.scaleVisible=e}}},[a("div",[a("p",{staticStyle:{"line-height":"1.5em"}},[a("b",[t._v("提示：")]),t._v("\n        你将进行手动扩缩容操作。应用发版后会恢复到发布流程所配置到实例数。\n      ")]),t._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"120px"}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"应用"}},[a("b",[t._v(t._s(this.scaleForm.app))])]),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"环境"}},[a("b",[t._v(t._s(this.scaleForm.namespace)+" ("+t._s(this.scaleForm.cluster)+")")])]),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"副本数调整为"}},[a("el-input-number",{attrs:{min:0,max:50},model:{value:t.scaleForm.replicas,callback:function(e){t.$set(t.scaleForm,"replicas",e)},expression:"scaleForm.replicas"}})],1)],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.scaleVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:t.appScale}},[t._v("确定")])],1)]),t._v(" "),a("el-dialog",{attrs:{title:"重启应用实例",visible:t.redeployVisible,width:"30%"},on:{"update:visible":function(e){t.redeployVisible=e}}},[a("div",[a("p",{staticStyle:{"line-height":"1.5em"}},[a("b",[t._v("提示：")]),t._v("\n        启动一批新实例，等新实例达到健康后再删除对应数量的旧实例，如此循环直到所有旧实例被新实例替换掉。确认继续吗？\n      ")]),t._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"120px"}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"应用名"}},[a("b",[t._v(t._s(this.redeployForm.app))])]),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"环境"}},[a("b",[t._v(t._s(this.redeployForm.namespace)+" ("+t._s(this.redeployForm.cluster)+")")])]),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"每批启动实例数"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{type:"number",step:"25",min:"10",max:"100"},model:{value:t.redeployForm.maxSurge,callback:function(e){t.$set(t.redeployForm,"maxSurge",t._n(e))},expression:"redeployForm.maxSurge"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.redeployVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.redeployLoading,expression:"redeployLoading"}],attrs:{type:"warning"},on:{click:t.redeploy}},[t._v("继续操作")])],1)]),t._v(" "),a("el-dialog",{attrs:{title:t.podDetail.name,visible:t.podDetailVisible,top:"5vh"},on:{"update:visible":function(e){t.podDetailVisible=e}}},[a("vue-json-pretty",{directives:[{name:"loading",rawName:"v-loading",value:t.podDetailLoading,expression:"podDetailLoading"}],staticStyle:{"max-height":"600px","overflow-y":"auto"},attrs:{data:t.podDetail.json}})],1),t._v(" "),a("el-dialog",{attrs:{title:"选择需要覆盖测试的代码",visible:t.jacocoVisible,top:"5vh","close-on-click-modal":!1,width:"650px",center:""},on:{"update:visible":function(e){t.jacocoVisible=e}}},[a("el-form",{attrs:{model:t.jacocoForm,"label-width":"140"}},[a("el-form-item",{attrs:{label:"部署模块"}},[a("el-checkbox",{attrs:{disabled:""},model:{value:t.jacocoForm.module,callback:function(e){t.$set(t.jacocoForm,"module",e)},expression:"jacocoForm.module"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"依赖Jar"}},[a("el-select",{staticStyle:{width:"500px"},attrs:{filterable:"",clearable:"",multiple:"",placeholder:"选择需要进行代码覆盖测试的Jar包，不需要的话保持为空即可"},model:{value:t.jacocoForm.jars,callback:function(e){t.$set(t.jacocoForm,"jars",e)},expression:"jacocoForm.jars"}},t._l(this.jacocoJarOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:this.jacocoReportDisable,disabled:this.jacocoReportDisable},on:{click:t.jacocoReportTaskCreate}},[t._v("确 定\n      ")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"临时调整应用资源",visible:t.resourceUpdateVisible,width:"40%"},on:{"update:visible":function(e){t.resourceUpdateVisible=e}}},[a("div",[a("p",{staticStyle:{"line-height":"1.5em"}},[a("b",[t._v("提示：")]),t._v("\n        临时调整应用资源后，重发操作将使资源恢复为发布流程中配置的值。资源调整会触发所有 Pod 的批量重启。\n      ")]),t._v(" "),a("p",{staticStyle:{"line-height":"1.5em"}},[a("b",[t._v("应用：")]),t._v("\n        "+t._s(t.resourceUpdateForm.cluster)+" / "+t._s(t.resourceUpdateForm.namespace)+" / "),a("span",{staticStyle:{color:"orangered"}},[t._v(t._s(t.resourceUpdateForm.app))])]),t._v(" "),a("el-form",{ref:"resourceUpdateForm",attrs:{"label-width":"120px"}},[a("el-form-item",{attrs:{label:"资源（最大值）"}},[a("el-row",[a("el-col",{staticStyle:{"padding-right":"20px","max-width":"260px"},attrs:{span:12}},[a("el-input",{attrs:{type:"number",step:"0.1",min:"0.2",max:"15"},model:{value:t.resourceUpdateForm.limitCPU,callback:function(e){t.$set(t.resourceUpdateForm,"limitCPU",t._n(e))},expression:"resourceUpdateForm.limitCPU"}},[a("template",{slot:"prepend"},[t._v("CPU")])],2)],1),t._v(" "),a("el-col",{staticStyle:{"max-width":"260px"},attrs:{span:12}},[a("el-input",{attrs:{type:"number",step:"128",min:"128",max:"20480"},model:{value:t.resourceUpdateForm.limitMemory,callback:function(e){t.$set(t.resourceUpdateForm,"limitMemory",t._n(e))},expression:"resourceUpdateForm.limitMemory"}},[a("template",{slot:"prepend"},[t._v("内存")]),t._v(" "),a("template",{slot:"append"},[t._v("MB")])],2)],1)],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"资源（最小值）"}},[a("el-row",[a("el-col",{staticStyle:{"padding-right":"20px","max-width":"260px"},attrs:{span:12}},[a("el-input",{attrs:{type:"number",step:"0.1",min:"0.1",max:"15"},model:{value:t.resourceUpdateForm.requestCPU,callback:function(e){t.$set(t.resourceUpdateForm,"requestCPU",t._n(e))},expression:"resourceUpdateForm.requestCPU"}},[a("template",{slot:"prepend"},[t._v("CPU")])],2)],1),t._v(" "),a("el-col",{staticStyle:{"max-width":"260px"},attrs:{span:12}},[a("el-input",{attrs:{type:"number",step:"128",min:"128",max:"20480"},model:{value:t.resourceUpdateForm.requestMemory,callback:function(e){t.$set(t.resourceUpdateForm,"requestMemory",t._n(e))},expression:"resourceUpdateForm.requestMemory"}},[a("template",{slot:"prepend"},[t._v("内存")]),t._v(" "),a("template",{slot:"append"},[t._v("MB")])],2)],1)],1)],1)],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.resourceUpdateVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.resourceLoading,expression:"resourceLoading"}],attrs:{type:"warning"},on:{click:t.resourceUpdate}},[t._v("确定")])],1)]),t._v(" "),a("el-drawer",{attrs:{withHeader:!1,visible:t.javaCMSVisible,direction:"rtl",size:"40%"},on:{"update:visible":function(e){t.javaCMSVisible=e}}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("Java应用配置项列表 "),a("small",[t._v("[ "+t._s(t.javaCMS.podObj?t.javaCMS.podObj.name:"")+" ]")])]),t._v(" "),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:function(e){t.javaCMSVisible=!1}}},[t._v("关闭\n        ")])],1),t._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.javaCMSLoading,expression:"javaCMSLoading"}]},[t.javaCMS.files&&t.javaCMS.files.length>0?a("div",{staticClass:"javaCMSTotal"},[t._v("共 "+t._s(t.javaCMS.files.length)+"条")]):t._e(),t._v(" "),t._l(t.javaCMS.files,(function(e){return a("span",{key:e,staticClass:"cms-file-item"},[a("el-link",{attrs:{href:"",target:"_blank"}},[t._v("\n            "+t._s(e)+"\n        ")])],1)}))],2)])],1),t._v(" "),a("el-drawer",{attrs:{withHeader:!1,"destroy-on-close":!0,visible:t.addressVisible,direction:"btt",size:"400px"},on:{"update:visible":function(e){t.addressVisible=e}}},[a("app-address",{attrs:{cluster:t.address.cluster,namespace:t.address.namespace,app:t.address.app,icon:"el-icon-link"}})],1),t._v(" "),a("el-drawer",{staticClass:"file-drawer",attrs:{title:"实例文件",visible:t.podFileVisible,size:"84%",direction:"ltr"},on:{"update:visible":function(e){t.podFileVisible=e}}},[a("div",[a("pod-file",{attrs:{cluster:t.podFile.cluster,namespace:t.podFile.namespace,pod:t.podFile.pod}})],1)])],1)},b=[],_=a("75fc"),y=a("2d63"),x=(a("7514"),a("6762"),a("2fdb"),a("a527")),w=a("8504"),S=a("d538"),C=a.n(S),k=a("b144"),L=a("4ad4");function j(t,e,a){return Object(c["a"])({url:"/jacoco/reset",method:"POST",params:{cluster:t,namespace:e,pod:a}})}function T(t,e,a,o){return Object(c["a"])({url:"/jacoco/report/task",method:"POST",params:{cluster:t,namespace:e,pod:a,jars:o}})}function P(t,e,a){return Object(c["a"])({url:"/jacoco/list/jars",method:"GET",params:{cluster:t,namespace:e,pod:a}})}var F=a("cf89"),M=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticStyle:{"margin-bottom":"10px",height:"30px",overflow:"hidden"}},[a("label",{staticStyle:{display:"inline-block",width:"70px",color:"#999","font-size":"14px","padding-right":"12px","text-align":"right"}},[t._v("访问历史")]),t._v(" "),t._l(t.accessHistory,(function(e){return a("el-button",{staticStyle:{"font-size":"14px",padding:"7px","margin-bottom":"10px"},attrs:{size:"mini",type:"plain",plain:""},on:{click:function(a){return t.historyChange(e)}}},[a("span",{staticStyle:{color:"#666"}},[t._v(t._s(e.cluster)+"/"+t._s(e.namespace)+"/")]),a("b",{staticStyle:{color:"#409EFF"}},[t._v(t._s(e.app))])])}))],2)])},$=[],A=a("c24f"),D={name:"podHistory",props:{},mounted:function(){this.loadAccessHistory()},computed:{},data:function(){return{accessHistory:[]}},methods:{historyChange:function(t){this.$emit("changeHandler",t.cluster,t.namespace,t.app)},loadAccessHistory:function(){var t=this;Object(A["a"])().then((function(e){var a,o=[],n=Object(y["a"])(e.data.recentPods);try{for(n.s();!(a=n.n()).done;){var i=a.value,s=i.split("/");3===s.length&&o.push({cluster:s[0],namespace:s[1],app:s[2]})}}catch(r){n.e(r)}finally{n.f()}t.accessHistory=o})).catch((function(t){console.log(t)}))}}},O=D,U=Object(m["a"])(O,M,$,!1,null,null,null),E=U.exports,R=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-descriptions",{directives:[{name:"loading",rawName:"v-loading",value:t.loading1||t.loading2,expression:"loading1 || loading2"}],attrs:{column:t.columns,border:"",size:"small",labelClassName:"app-desc-label"}},[a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("应用名")])]),t._v(" "),a("b",{class:t.appHighLight?"app-high-light":""},[t._v(t._s(this.appMeta.name))]),t._v(" "),a("clipboard-icon",{attrs:{text:this.appMeta.name}})],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("环境")])]),t._v(" "),a("b",{class:t.appHighLight?"app-high-light":""},[t._v(t._s(this.namespace))]),t._v(" "),a("clipboard-icon",{attrs:{text:this.namespace}})],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("集群")])]),t._v(" "),a("b",{class:t.appHighLight?"app-high-light":""},[t._v(t._s(this.cluster)+" ")]),t._v(" "),a("clipboard-icon",{attrs:{text:this.cluster}})],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("实例数")]),t._v(" "),a("el-tooltip",{attrs:{content:"运行实例数 / 发布流程配置实例数",placement:"top"}},[a("el-icon",{staticClass:"el-icon-question",attrs:{size:16}})],1)],1),t._v(" "),a("b",{class:t.appHighLight?"app-high-light":""},[t._v("\n        "+t._s(this.deployMeta?this.deployMeta.replicas:"--")+"\n        "),this.deployMeta&&this.deployMeta.pipelineReplicas>=0?a("span",[t._v("\n         / "+t._s(this.deployMeta.pipelineReplicas)+"\n      ")]):t._e()])],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("运行版本")])]),t._v(" "),this.deployMeta?a("div",[t._v("\n        "+t._s(this.deployMeta.deployTag&&this.deployMeta.deployTag.length>50?this.deployMeta.deployTag.substring(1,50)+"...":this.deployMeta.deployTag)+"\n      ")]):a("div",[t._v("\n        --\n      ")])],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("CPU分配")])]),t._v(" "),this.deployMeta?a("div",[t._v("\n        "+t._s((this.deployMeta.requestCpu/1e3).toFixed(2))+"\n        -\n        "+t._s((this.deployMeta.limitCpu/1e3).toFixed(2))+"\n      ")]):a("div",[t._v("\n        --\n      ")])],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[a("b",[t._v("内存分配 (MB)")])]),t._v(" "),this.deployMeta?a("div",[t._v("\n        "+t._s(Math.floor(this.deployMeta.requestMemory/1024/1024))+"\n        -\n        "+t._s(Math.floor(this.deployMeta.limitMemory/1024/1024))+"\n      ")]):a("div",[t._v("\n        --\n      ")])],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[t._v("管理员\n      ")]),t._v("\n      "+t._s(t.appMeta.admins&&t.appMeta.admins.length>0?t.appMeta.admins.join(","):"--")+"\n    ")],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[t._v("负责人")]),t._v(" "),t.appMeta.owner?t.appMeta.owner.length>16?a("div",[a("el-tooltip",{attrs:{content:t.appMeta.owner,placement:"top"}},[a("span",[t._v(t._s(t.appMeta.owner.substring(0,16))+"...")])])],1):a("div",[t._v("\n        "+t._s(t.appMeta.owner)+"\n      ")]):a("div",[t._v("\n        --\n      ")])],2),t._v(" "),a("el-descriptions-item",[a("template",{slot:"label"},[t._v("应用描述")]),t._v("\n      "+t._s(t.appMeta.remark)+"\n    ")],2),t._v(" "),a("el-descriptions-item",{staticStyle:{padding:"0 10px"}},[a("template",{slot:"label"},[a("b",[t._v("部署信息")])]),t._v(" "),a("div",{staticStyle:{"line-height":"1.4em","font-size":"11px"}},[this.deployMeta?a("div",{staticStyle:{display:"inline-block"}},[t._v("最后发布："+t._s(this.deployMeta.deployTime))]):t._e(),t._v(" "),this.deployMeta?a("div",{staticStyle:{display:"inline-block","padding-left":"20px"}},[t._v("发布人："+t._s(this.deployMeta.deployUser))]):t._e()])],2)],1),t._v(" "),t._l(t.clusterLabels,(function(t){return a("el-alert",{staticStyle:{"margin-top":"10px","font-weight":"bold"},attrs:{title:"提示："+t,type:"warning",closable:!1,effect:"dark"}})}))],2)},V=[],z=(a("c5f6"),a("51a9")),N=a("da37"),q={components:{ClipboardIcon:N["a"]},props:{cluster:{type:String,default:"",required:!0},namespace:{type:String,default:"",required:!0},app:{type:String,default:"",required:!0},columns:{type:Number,default:4,required:!1},appHighLight:{type:Boolean,default:!1,required:!1}},mounted:function(){this.loadApp()},watch:{cluster:function(t){this.loadApp()},namespace:function(t){this.loadApp()},app:function(t){this.loadApp()}},computed:{clusterLabels:function(){var t,e=Object(y["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var a=t.value;if(a.name===this.cluster)return a.labels}}catch(o){e.e(o)}finally{e.f()}return[]}},data:function(){return{loading1:!0,loading2:!0,appMeta:{},deployMeta:null}},methods:{loadApp:function(){var t=this;this.loading1=!0,this.loading2=!0,Object(s["j"])(this.app).then((function(e){t.appMeta=e.data})).catch((function(e){t.$message.error(e.message),t.appMeta={}})).finally((function(){t.loading1=!1})),Object(w["a"])(this.cluster,this.namespace,this.app).then((function(e){e.data.pipelineReplicas=-1,t.deployMeta=e.data,t.loadPipeline()})).catch((function(e){t.deployMeta=null})).finally((function(){t.loading2=!1}))},loadPipeline:function(){var t=this;Object(z["d"])(this.cluster,this.namespace,this.app).then((function(e){t.deployMeta.pipelineReplicas=e.data.replicas})).catch((function(e){t.deployMeta.pipelineReplicas=-1}))}}},I=q,B=(a("71c8"),Object(m["a"])(I,R,V,!1,null,null,null)),H=B.exports,J=a("67e1"),Q={name:"podList",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0}},watch:{cluster:function(t){this.loadPod(!0)},namespace:function(t){this.loadPod(!0)},app:function(t){this.loadPod(!0)}},components:{PodFile:J["a"],PodApp:H,PodHistory:E,AppAddress:L["a"],VueJsonPretty:C.a,PodExpand:f["a"],PodStdout:F["a"],AppEvent:v},data:function(){return{pageLoading:!1,tableData:[],tableLoading:!1,tableLoadTime:"",javaCMSVisible:!1,javaCMSLoading:!1,javaCMS:{podObj:null,files:[]},podStdoutVisible:!1,podStdout:{cluster:"",namespace:"",pod:"",containers:[]},podDetailVisible:!1,podDetailLoading:!1,podDetail:{name:"",json:""},addressVisible:!1,address:{cluster:"",namespace:"",app:""},podFileVisible:!1,podFile:{cluster:"",namespace:"",pod:""},redeployVisible:!1,redeployLoading:!1,redeployForm:{cluster:"",namespace:"",app:"",maxSurge:25},scaleVisible:!1,scaleForm:{cluster:"",namespace:"",app:"",currReplicas:-1,replicas:2},jacocoVisible:!1,jacocoForm:{module:!0,jars:[]},jacocoReportDisable:!1,jacocoJarOptions:[],rollbackTableVisible:!1,rollbackTableLoading:!1,rollbackTableData:[],podEventsLoading:!1,cache:{},resourceUpdateVisible:!1,resourceLoading:!1,resourceUpdateForm:{cluster:"",namespace:"",app:"",limitCPU:0,limitMemory:0,requestCPU:0,requestMemory:0}}},computed:{},mounted:function(){this.loadPod(!0)},methods:{podStatusClass:function(t){if(t){if("运行中"===t)return"pod-status-green";if(["调度中","准备中","启动中"].includes(t))return"pod-status-orange"}return"pod-status-red"},tableExpandChange:function(t,e){},queryPod:function(){this.$refs["searchForm"].validate((function(t){}))},loadPod:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.loadPod2(this.cluster,this.namespace,this.app,t)},loadPod2:function(t,e,a){var o=this,n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this.tableLoading=n,Object(x["h"])(t,e,a).then((function(t){o.tableData=t.data||[],o.tableLoading=!1,o.tableLoadTime=Object(k["c"])(new Date)})).catch((function(t){o.$message.error(t.message),o.tableLoading=!1}))},reloadPodScheduler:function(){},onClearSearchFormApp:function(){this.$refs.searchFormApp.$children.find((function(t){return t.$el.className.includes("el-input")})).blur(),this.$refs.searchFormApp.focus()},findPodByName:function(t){for(var e in this.tableData)if(this.tableData[e].name===t)return this.tableData[e]},showAddress:function(){this.address={cluster:this.cluster,namespace:this.namespace,app:this.app},this.addressVisible=!0},podDetailDialog:function(t){var e=this;this.podDetailVisible=!0,this.podDetail.name===t&&this.podDetail.json||(this.podDetailLoading=!0,Object(x["e"])(this.cluster,this.namespace,t).then((function(a){e.podDetailLoading=!1,e.podDetail.name=t,e.podDetail.json=a.data})).catch((function(t){e.$message.error(t.message),e.podDetailLoading=!1})))},showJavaCMS:function(){var t=this,e=this.tableData&&this.tableData.length>0?this.tableData[0]:void 0;if(e){var a=this.cluster;this.javaCMSLoading=!0,this.javaCMSVisible=!0;var o="cmsFile##".concat(a,"##").concat(e.namespace,"##").concat(e.name),n=this.cache[o];if(n)return this.javaCMS=n,void(this.javaCMSLoading=!1);this.javaCMS.podObj=e,this.javaCMS.files=[];var i=this;Object(x["q"])(a,e.namespace,e.name).then((function(t){i.javaCMSLoading=!1,i.javaCMS.files=t.data,i.cache[o]=Object(k["a"])(i.javaCMS)})).catch((function(e){i.javaCMSLoading=!1,t.$message.error(e.message)}))}else this.$message({message:"页面没有找到任何Pod！",type:"warning"})},podLogFile:function(t){this.podFile.cluster=this.cluster,this.podFile.namespace=this.namespace,this.podFile.pod=t,this.podFileVisible=!0},podMonitor:function(t){var e="/api/page/redirect?type=grafana&cluster=".concat(this.cluster,"&app=").concat(this.app,"&namespace=").concat(this.namespace,"&pod=").concat(t);window.open(e)},podShell:function(t){this.webShellPage(this.cluster,this.namespace,this.app,t.name)},selectedPodShell:function(){if(this.tableData&&!(this.tableData.length<1)){var t,e=[],a=Object(y["a"])(this.tableData);try{for(a.s();!(t=a.n()).done;){var o=t.value;e.push(o.name)}}catch(n){a.e(n)}finally{a.f()}this.webShellPage(this.cluster,this.namespace,this.app,e.join(","))}},webShellPage:function(t,e,a,o){var n="/api/page/redirect?type=webShell&cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&pods=").concat(o,"&_t")+Date.now();window.open(n)},chTomcatAccessLogPage:function(t,e,a,o){var n="/api/page/redirect?type=clickhouse&logName=tomcat_access&cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&ip=").concat(o,"&_t")+Date.now();window.open(n)},chLogPage:function(t,e,a,o){var n="/api/page/redirect?type=clickhouse&logName=app_log&cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(a,"&ip=").concat(o,"&_t")+Date.now();window.open(n)},chAppLog:function(){this.chLogPage(this.cluster,this.namespace,this.app,"")},pipelinePage:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.app}})},chTomcatAccessLog:function(){this.chTomcatAccessLogPage(this.cluster,this.namespace,this.app,"")},appScaleDialog:function(){var t=this;this.pageLoading=!0,this.scaleForm.cluster=this.cluster,this.scaleForm.namespace=this.namespace,this.scaleForm.app=this.app,Object(w["a"])(this.scaleForm.cluster,this.scaleForm.namespace,this.scaleForm.app).then((function(e){t.scaleForm.replicas=e.data.replicas,t.scaleForm.currReplicas=t.scaleForm.replicas,t.scaleVisible=!0})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))},appScale:function(){var t=this,e=this.scaleForm.currReplicas,a=this.scaleForm.replicas,o="";if(e>a)o="当前为缩容操作，副本数变化： ".concat(e," → ").concat(a,"，是否继续？");else{if(!(e<a))return void this.$message.warning("副本数没有变化");o="当前为扩容操作，副本数变化： ".concat(e," → ").concat(a,"，是否继续？")}this.$confirm(o,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pageLoading=!0;var e=t.scaleForm.replicas;e>100&&t.$message.warning("实例数值只能是[0-100]之间"),t.scaleVisible=!1,Object(w["f"])(t.scaleForm).then((function(e){t.$message.success("操作成功，3秒后将刷新页面"),setTimeout((function(){window.location.reload()}),3e3)})).catch((function(e){t.$message.error("操作失败："+e.message)})).finally((function(){t.pageLoading=!1}))})).catch((function(){}))},redeployDialog:function(){this.redeployForm.cluster=this.cluster,this.redeployForm.namespace=this.namespace,this.redeployForm.app=this.app,this.redeployForm.maxSurge=25,this.redeployVisible=!0},redeploy:function(){var t=this;this.redeployLoading=!0;var e=this.redeployForm.maxSurge;(e<1||e>100)&&this.$message.warning("每批重启实例百分数值只能是[1-100]之间"),this.redeployVisible=!1,Object(w["b"])(this.redeployForm).then((function(e){t.$message.success("操作成功，请等待一段时间再刷新页面数据"),t.loadPod()})).catch((function(e){t.$message.error("操作失败："+e.message)})).finally((function(){t.redeployLoading=!1}))},rollbackDialog:function(){var t=this;this.rollbackTableVisible=!0,this.rollbackTableLoading=!0,Object(w["d"])(this.cluster,this.namespace,this.app).then((function(e){var a=e.data;Object(w["a"])(t.cluster,t.namespace,t.app).then((function(e){var o,n=Object(y["a"])(a);try{for(n.s();!(o=n.n()).done;){var i=o.value;i.revision===e.data.revision?i.isCurrReplicaSet=!0:i.isCurrReplicaSet=!1}}catch(s){n.e(s)}finally{n.f()}t.rollbackTableData=a})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.rollbackTableLoading=!1}))})).catch((function(e){t.rollbackTableLoading=!1,t.$message.error(e.message)}))},appRollback:function(t){var e=this;this.$confirm("确定要回滚到 "+t.deployTag+"?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.rollbackTableLoading=!0;var a=e.cluster,o=e.namespace,n=e.app;Object(w["e"])(a,o,n,t.revision,t.deployTag).then((function(t){e.$message.success("操作成功！"),e.loadPod(),e.rollbackTableVisible=!1})).catch((function(t){e.$message.error("操作失败："+t.message)})).finally((function(){e.rollbackTableLoading=!1}))})).catch((function(t){console.error(t)}))},appLogDropdownCommand:function(t){console.log("app log dropdown command: "+t),"chAppLog"===t?this.chAppLog():"chTomcatAccess"===t?this.chTomcatAccessLog():console.error("unknown command: "+t)},jacocoDropdownCommand:function(t){var e=this;if(console.log("jacoco dropdown command: "+t),this.tableData&&1===this.tableData.length){var a=this.tableData[0].name,o=this.cluster,n=this.namespace;"reset"===t?this.$confirm("是否确认要重置覆盖率测试?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pageLoading=!0,j(o,n,a).then((function(t){e.$message.success("操作成功！")})).catch((function(t){e.$message.error("操作失败："+t.message)})).finally((function(){e.pageLoading=!1}))})).catch((function(){})):"reportTask"===t?(this.pageLoading=!0,P(o,n,a).then((function(t){e.jacocoJarOptions=t.data,e.jacocoVisible=!0})).catch((function(t){e.$message.error("操作失败："+t.message)})).finally((function(){e.pageLoading=!1}))):console.error("unknown command: "+t)}else this.$message.error("Jacoco覆盖率测试只支持单实例应用")},jacocoReportTaskCreate:function(){var t=this,e=this.tableData[0].name,a=this.cluster,o=this.namespace,n="";this.jacocoForm.jars&&this.jacocoForm.jars.length>0&&(n=this.jacocoForm.jars.join(",")),this.jacocoReportDisable=!0,T(a,o,e,n).then((function(e){t.$message.success("操作成功！"),t.jacocoVisible=!1,window.open(e.data)})).catch((function(e){t.$message.error("操作失败："+e.message)})).finally((function(){t.jacocoReportDisable=!1}))},appBtnDropdownCommand:function(t){console.log("app button dropdown command: "+t),"scale"===t?this.appScaleDialog():"redeploy"===t?this.redeployDialog():"rollback"===t?this.rollbackDialog():"java_cms"===t?this.showJavaCMS():"resourceUpdate"===t?this.resourceUpdateDialog():console.error("unknown command: "+t)},manageDropdownCommand:function(t){console.log("manage dropdown command: "+t);var e=t.split("##");if(2===e.length){var a=e[0],o=e[1];"detail"===a?this.podDetailDialog(o):"delete"===a?this.podRemove(o):"deregister"===a?this.podDeregister(o):"versionRetain"===a?this.podVersionRetain(o):"file"===a?this.podLogFile(o):"stdout"===a?this.podStdoutLog(o):"chAppLog"===a?this.chLogPage(this.cluster,this.namespace,this.app,o):"chTomcatAccess"===a?this.chTomcatAccessLogPage(this.cluster,this.namespace,this.app,o):"openPyroscope"===a?this.openPyroscope(o):"viewPyroscope"===a?this.viewPyroscope(o):this.$message.error("未知操作："+t)}},openPyroscope:function(t){var e=this;this.$confirm("续性能分析大概有 2% 左右的性能开销，确定是否继续开启？ pod：".concat(t),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pageLoading=!0,Object(x["j"])(e.cluster,e.namespace,t).then((function(t){e.$message.success("已开启持续性能分析，修改的配置文件为："+t.data)})).catch((function(t){e.$message.error("操作失败："+t.message)})).finally((function(){e.pageLoading=!1}))})).catch((function(){}))},viewPyroscope:function(t){var e="/api/page/redirect?type=grafana&dashboard=pyroscope&cluster=".concat(this.cluster,"&app=").concat(this.app,"&namespace=").concat(this.namespace,"&pod=").concat(t);window.open(e,"_blank")},podRemove:function(t){var e=this;this.$confirm("删除后会重新创建一个新的实例，是否要继续删除实例？ 实例名：".concat(t),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.tableLoading=!0,Object(x["k"])(e.cluster,e.namespace,t).then((function(t){e.tableLoading=!1,e.$message.success("操作成功,请等待一段时间再刷新页面数据"),e.loadPod()})).catch((function(t){e.$message.error(t.message),e.tableLoading=!1}))})).catch((function(t){console.error(t)}))},podDeregister:function(t){var e=this;this.$confirm("摘除后，实例的HTTP流量、Dubbo流量将从服务提供者列表删除，RocketMQ消费者将停止(生产者不受影响），实例不会被删除。注意Dubbo服务必须等新实例启动后才会真正摘除。是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.tableLoading=!0,Object(x["l"])(e.cluster,e.namespace,t).then((function(t){e.tableLoading=!1,e.$message.success("操作成功！"),e.loadPod()})).catch((function(t){e.tableLoading=!1,e.$message.error("操作失败："+t.message)}))})).catch((function(t){console.error(t)}))},podVersionRetain:function(t){var e=this;this.$confirm("实例版本被保留后，实例会继续接受流量，但不计入副本数，不受发版影响（发版时不会被删除，不需要的话请手动删除）。是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.tableLoading=!0,Object(x["o"])(e.cluster,e.namespace,t).then((function(t){e.tableLoading=!1,e.$message.success("操作成功！"),e.loadPod()})).catch((function(t){e.tableLoading=!1,e.$message.error("操作失败："+t.message)}))})).catch((function(t){console.error(t)}))},podStdoutLog:function(t){this.podStdoutVisible=!0,this.podStdout.cluster=this.cluster,this.podStdout.namespace=this.namespace,this.podStdout.pod=t;var e=this.tableData.find((function(e){return e.name===t}));this.podStdout.containers=e?[e.container0Name].concat(Object(_["a"])(e.initContainersName)):[]},resourceUpdateDialog:function(){this.resourceUpdateForm.cluster=this.cluster,this.resourceUpdateForm.namespace=this.namespace,this.resourceUpdateForm.app=this.app;var t=this.tableData[0];t?(this.resourceUpdateForm.limitCPU=parseFloat((t.limitCpu/1e3).toFixed(1)),this.resourceUpdateForm.limitMemory=parseInt(t.limitMemory/1024/1024),this.resourceUpdateForm.requestCPU=parseFloat((this.tableData[0].requestCpu/1e3).toFixed(1)),this.resourceUpdateForm.requestMemory=parseInt(this.tableData[0].requestMemory/1024/1024),this.resourceUpdateVisible=!0):this.$message.error("无任何副本，不支持资源调整")},resourceUpdate:function(){var t=this;this.resourceLoading=!0,Object(w["h"])(this.resourceUpdateForm).then((function(e){t.$message.success("操作成功，3秒后将自动刷新页面"),t.resourceUpdateVisible=!1,setTimeout((function(){window.location.reload()}),3e3)})).catch((function(e){t.$message.error("操作失败："+e.message)})).finally((function(){t.resourceLoading=!1}))}}},G=Q,W=(a("a0e3"),Object(m["a"])(G,g,b,!1,null,null,null)),K=W.exports,X=a("71df"),Y={components:{AppSelector2:X["a"],PodList:K,PodExpand:f["a"],AppEvent:v},data:function(){return{pageLoading:!1,envPageLoading:!0,currCluster:"",currNamespace:"",currApp:"",currTab:"pod",tabIframeLoading:!1,appEnvs:{},currAppEnvs:[],monitorUrl:"",appLogUrl:"",tomcatAccessLogUrl:""}},created:function(){this.loadAppEnvs()},mounted:function(){},watch:{},computed:{appIsSelected:function(){return this.currCluster&&this.currNamespace&&this.currApp},envSelected:{get:function(){return this.currCluster+"/"+this.currNamespace},set:function(t){var e=t.split("/"),a=Object(i["a"])(e,2);this.currCluster=a[0],this.currNamespace=a[1]}},envSelectorDesc:function(){var t=this;if(!this.currAppEnvs.some((function(e){return e.cluster===t.currCluster&&e.namespace===t.currNamespace})))return"请选择一个正确的环境"}},methods:{loadAppEnvs:function(){var t=this;this.envPageLoading=!0,Object(s["b"])().then((function(e){t.appEnvs=e.data,t.currAppChange(t.currApp)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.envPageLoading=!1}))},envSelectedChange:function(t){this.updateQueryParam(),this.reloadTab()},selectFirstEnv:function(){!this.appEnvs||this.appEnvs.length<1||(this.currAppEnvs=this.appEnvs[this.currApp]||[],this.currAppEnvs.length>0&&(this.currCluster=this.currAppEnvs[0].cluster,this.currNamespace=this.currAppEnvs[0].namespace))},reloadTab:function(){var t=this.currTab;"monitor"===t?this.reloadMonitorUrl():"appLog"===t?this.reloadAppLogUrl():"tomcatAccessLog"===t&&this.reloadTomcatAccessLogUrl()},updateQueryParam:function(){var t=Object(k["a"])(this.$route.query);t["app"]=this.currApp,t["cluster"]=this.currCluster,t["namespace"]=this.currNamespace,this.$router.push({query:t})},currAppChange:function(t){this.currApp=t,this.currCluster||(this.currCluster=this.$route.query.cluster||""),this.currNamespace||(this.currNamespace=this.$route.query.namespace||""),this.currAppEnvs=this.appEnvs[t]||[],this.currAppEnvs.length>0&&(console.info("currApp: "+this.currApp+", currCluster: "+this.currCluster+", currNamespace: "+this.currNamespace),!this.currApp||this.currCluster&&this.currNamespace||this.selectFirstEnv()),this.updateQueryParam(),this.reloadTab()},tabChange:function(t,e){this.currTab!==t.name&&(this.currTab=t.name,this.tabChangeHandler(),this.updateQueryParam())},reloadMonitorUrl:function(){this.monitorUrl="/api/page/redirect?type=grafana&cluster=".concat(this.currCluster,"&app=").concat(this.currApp,"&namespace=").concat(this.currNamespace,"&kiosk=tv&theme=light")},reloadAppLogUrl:function(){this.appLogUrl="/api/page/redirect?type=clickhouse&pageType=share&logName=app_log&cluster=".concat(this.currCluster,"&namespace=").concat(this.currNamespace,"&app=").concat(this.currApp)},reloadTomcatAccessLogUrl:function(){this.tomcatAccessLogUrl="/api/page/redirect?type=clickhouse&pageType=share&logName=tomcat_access&cluster=".concat(this.currCluster,"&namespace=").concat(this.currNamespace,"&app=").concat(this.currApp)},tabChangeHandler:function(){try{this.tabIframeLoading=!0,console.info("tab change to: "+this.currTab),this.reloadTab()}finally{var t=this;setTimeout((function(){t.tabIframeLoading=!1}),1e3)}},clickhouseLogPage:function(t,e){var a="/api/page/redirect?type=clickhouse&logName=".concat(t,"&cluster=").concat(this.currCluster,"&namespace=").concat(this.currNamespace,"&app=").concat(this.currApp,"&ip=").concat(e,"&_t")+Date.now();window.open(a)},grafanaPage:function(t){var e="/api/page/redirect?type=grafana&cluster=".concat(this.currCluster,"&app=").concat(this.currApp,"&namespace=").concat(this.currNamespace,"&pod=").concat(t);window.open(e)}}},Z=Y,tt=(a("943e"),Object(m["a"])(Z,o,n,!1,null,null,null));e["default"]=tt.exports},5026:function(t,e,a){},"51a9":function(t,e,a){"use strict";a.d(e,"c",(function(){return n})),a.d(e,"e",(function(){return i})),a.d(e,"d",(function(){return s})),a.d(e,"l",(function(){return r})),a.d(e,"m",(function(){return l})),a.d(e,"a",(function(){return c})),a.d(e,"f",(function(){return p})),a.d(e,"i",(function(){return d})),a.d(e,"j",(function(){return u})),a.d(e,"k",(function(){return m})),a.d(e,"n",(function(){return h})),a.d(e,"g",(function(){return v})),a.d(e,"b",(function(){return f})),a.d(e,"h",(function(){return g})),a.d(e,"o",(function(){return b}));var o=a("b775");function n(t){return Object(o["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function i(t){return Object(o["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function s(t,e,a){return Object(o["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:a}})}function r(t){return Object(o["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function l(t){return Object(o["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function c(){return Object(o["a"])({url:"/v1/pipeline/all",method:"get"})}function p(t){return Object(o["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function d(t){return Object(o["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function u(t){return Object(o["a"])({url:"/v1/pipeline",method:"post",data:t})}function m(t){return Object(o["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function h(t){return Object(o["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function v(t,e,a,n){return Object(o["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:n}})}function f(t){return Object(o["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function g(t,e,a,n){return Object(o["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:n}})}function b(t){return Object(o["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"520a":function(t,e,a){"use strict";var o=a("0bfb"),n=RegExp.prototype.exec,i=String.prototype.replace,s=n,r="lastIndex",l=function(){var t=/a/,e=/b*/g;return n.call(t,"a"),n.call(e,"a"),0!==t[r]||0!==e[r]}(),c=void 0!==/()??/.exec("")[1],p=l||c;p&&(s=function(t){var e,a,s,p,d=this;return c&&(a=new RegExp("^"+d.source+"$(?!\\s)",o.call(d))),l&&(e=d[r]),s=n.call(d,t),l&&s&&(d[r]=d.global?s.index+s[0].length:e),c&&s&&s.length>1&&i.call(s[0],a,(function(){for(p=1;p<arguments.length-2;p++)void 0===arguments[p]&&(s[p]=void 0)})),s}),t.exports=s},"5f1b":function(t,e,a){"use strict";var o=a("23c6"),n=RegExp.prototype.exec;t.exports=function(t,e){var a=t.exec;if("function"===typeof a){var i=a.call(t,e);if("object"!==typeof i)throw new TypeError("RegExp exec method returned something other than an Object or null");return i}if("RegExp"!==o(t))throw new TypeError("RegExp#exec called on incompatible receiver");return n.call(t,e)}},"6e36":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pod-expand"},[t.pod?a("div",{staticClass:"expand-wrapper"},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form",{attrs:{"label-position":"left","label-width":"80px",size:"mini"}},[a("el-form-item",{staticClass:"expand-wrapper-item",attrs:{label:"实例名"}},[a("span",[t._v(t._s(t.pod.name))])]),t._v(" "),a("el-form-item",{staticClass:"expand-wrapper-item",attrs:{label:"集群/环境"}},[a("span",[t._v(t._s(t.pod.cluster)+"/"+t._s(t.pod.namespace))])]),t._v(" "),a("el-form-item",{attrs:{label:"运行版本"}},[a("span",[t._v(t._s(t.pod.deployTag))])]),t._v(" "),a("el-form-item",{attrs:{label:"容器镜像"}},[a("span",[t._v(t._s(t.pod.container0Image))])]),t._v(" "),a("el-form-item",{attrs:{label:"状态"}},[t._v("\n            "+t._s(t.pod.statusDesc)+"\n            "),a("span",{staticStyle:{"padding-left":"10px"}},[t._v(" |\n               "),a("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("PodPhase: "+t._s(t.pod.phase))]),t._v(" "),a("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("podReady: "+t._s(t.pod.ready))]),t._v(" "),a("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("Container0Status: "+t._s(t.pod.container0Status))])],1)]),t._v(" "),a("el-form-item",{attrs:{label:"所在机器"}},[a("div",[a("span",[t._v(t._s(t.pod.hostIP))]),t._v(" "),a("span",[t._v("\n                （资源池: "+t._s(t.pod.resourcePool?t.pod.resourcePool:"Common")+" ）\n              ")])])]),t._v(" "),a("el-form-item",{attrs:{label:"资源配置"}},[a("span",[a("b",{staticStyle:{"padding-right":"5px"}},[t._v("CPU: ")]),t._v(t._s((t.pod.requestCpu/1e3).toFixed(2))+" - "+t._s((t.pod.limitCpu/1e3).toFixed(2))+" ")]),t._v(" "),a("span",[a("b",{staticStyle:{"padding-right":"5px","padding-left":"30px"}},[t._v("内存 (MB): ")]),t._v("\n              "+t._s(Math.floor(t.pod.requestMemory/1024/1024))+" - "+t._s(Math.floor(t.pod.limitMemory/1024/1024))+"\n            ")])]),t._v(" "),a("el-form-item",{attrs:{label:"重启次数"}},[a("span",[t._v(t._s(t.pod.restartCount))])]),t._v(" "),t.pod.restartCount>0?a("el-form-item",{attrs:{label:"最近重启时间"}},[a("span",[t._v(t._s(t.pod.restartTime))])]):t._e(),t._v(" "),t.pod.restartCount>0?a("el-form-item",{attrs:{label:"最近重启说明"}},[a("span",[t._v("Reason: "+t._s(t.pod.restartReason||"--"))]),t._v(" "),a("span",{staticStyle:{"padding-left":"20px"}},[t._v("ExitCode: "+t._s(t.pod.restartCode))])]):t._e()],1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("div",{staticStyle:{color:"#606266","font-weight":"bold","padding-left":"15px","line-height":"40px"}},[t._v("事件列表")]),t._v(" "),a("pod-event",{attrs:{cluster:t.pod.cluster,namespace:t.pod.namespace,pod:t.pod.name}})],1)],1)],1):t._e()])},n=[],i=a("1262"),s={components:{PodEvent:i["a"]},props:{pod:{type:Object,default:function(){return{}},required:!0}},created:function(){},computed:{},data:function(){return{}},methods:{}},r=s,l=(a("8f56"),a("2877")),c=Object(l["a"])(r,o,n,!1,null,null,null);e["a"]=c.exports},"71c8":function(t,e,a){"use strict";a("cfed")},7514:function(t,e,a){"use strict";var o=a("5ca1"),n=a("0a49")(5),i="find",s=!0;i in[]&&Array(1)[i]((function(){s=!1})),o(o.P+o.F*s,"Array",{find:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")(i)},"75fc":function(t,e,a){"use strict";var o=a("a745"),n=a.n(o),i=a("db2a");function s(t){if(n()(t))return Object(i["a"])(t)}var r=a("67bb"),l=a.n(r),c=a("5d58"),p=a.n(c),d=a("774e"),u=a.n(d);function m(t){if("undefined"!==typeof l.a&&null!=t[p.a]||null!=t["@@iterator"])return u()(t)}var h=a("e630");function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t){return s(t)||m(t)||Object(h["a"])(t)||v()}a.d(e,"a",(function(){return f}))},"768b":function(t,e,a){"use strict";var o=a("a745"),n=a.n(o);function i(t){if(n()(t))return t}var s=a("67bb"),r=a.n(s),l=a("5d58"),c=a.n(l);function p(t,e){var a=null==t?null:"undefined"!==typeof r.a&&t[c.a]||t["@@iterator"];if(null!=a){var o,n,i=[],s=!0,l=!1;try{for(a=a.call(t);!(s=(o=a.next()).done);s=!0)if(i.push(o.value),e&&i.length===e)break}catch(p){l=!0,n=p}finally{try{s||null==a["return"]||a["return"]()}finally{if(l)throw n}}return i}}var d=a("e630");function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){return i(t)||p(t,e)||Object(d["a"])(t,e)||u()}a.d(e,"a",(function(){return m}))},"8f56":function(t,e,a){"use strict";a("e0c2")},"943e":function(t,e,a){"use strict";a("5026")},a0e3:function(t,e,a){"use strict";a("209a")},b0c5:function(t,e,a){"use strict";var o=a("520a");a("5ca1")({target:"RegExp",proto:!0,forced:o!==/./.exec},{exec:o})},b674:function(t,e,a){},cf89:function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.stdout.loading,expression:"stdout.loading"}],staticClass:"pod-stdout"},[a("div",{staticStyle:{position:"relative"}},[a("div",{staticStyle:{"font-weight":"bold",float:"left","line-height":"40px"}},[t._v(t._s(this.pod)+" /\n      "),a("el-select",{staticStyle:{width:"240px"},attrs:{size:"mini",placeholder:"请选择容器"},on:{change:t.loadStdoutLog},model:{value:t.container,callback:function(e){t.container=t._n(e)},expression:"container"}},t._l(t.containers,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),a("div",{staticStyle:{float:"right","margin-right":"20px"}},[a("span",[a("el-checkbox",{on:{change:t.loadStdoutLog},model:{value:t.stdout.previous,callback:function(e){t.$set(t.stdout,"previous",e)},expression:"stdout.previous"}},[t._v("重启前日志")])],1),t._v(" "),a("span",{staticStyle:{"margin-left":"20px"}},[t._v("\n            行数:\n            "),a("el-select",{staticStyle:{width:"120px"},on:{change:t.loadStdoutLog},model:{value:t.stdout.tailLines,callback:function(e){t.$set(t.stdout,"tailLines",t._n(e))},expression:"stdout.tailLines"}},[a("el-option",{attrs:{label:"2000",value:"2000"}}),t._v(" "),a("el-option",{attrs:{label:"5000",value:"5000"}}),t._v(" "),a("el-option",{attrs:{label:"10000",value:"10000"}}),t._v(" "),a("el-option",{attrs:{label:"50000",value:"50000"}})],1)],1),t._v(" "),a("span",{staticStyle:{display:"none"}},[t._v("\n        自动刷新("+t._s(t.stdout.reloadPeriod)+"秒):\n      "),a("el-switch",{on:{change:t.autoReloadSwitch},model:{value:t.stdout.autoReload,callback:function(e){t.$set(t.stdout,"autoReload",e)},expression:"stdout.autoReload"}})],1),t._v(" "),a("el-button",{staticClass:"el-icon-refresh",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.loadStdoutLog()}}},[t._v("刷新\n      ")]),t._v(" "),a("el-button",{staticClass:"el-icon-download",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.podStdoutLogDownload()}}},[t._v("下载\n      ")])],1),t._v(" "),a("div",{staticStyle:{clear:"both"}})]),t._v(" "),a("div",{staticStyle:{"text-align":"right","margin-right":"5px"}},[t._v("加载时间: "+t._s(t.stdout.lastReloadTime))]),t._v(" "),a("pre",{staticClass:"stdout-log-content",attrs:{id:"stdout-log-content"}},[t._v(t._s(t.stdout.content))])])},n=[],i=a("a527"),s={name:"PodStdout",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!1},containers:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{container:this.containers[0],stdout:{visible:!1,loading:!1,autoReload:!1,previous:!1,tailLines:2e3,reloadPeriod:10,reloadTimer:null,content:"",lastReloadTime:"--"}}},watch:{pod:function(t,e){this.container=this.containers[0],this.loadStdoutLog()}},computed:{},mounted:function(){this.loadStdoutLog()},beforeDestroy:function(){this.stopReloadTimer()},methods:{showStdoutLogDialog:function(){this.stdout.visible=!0,this.loadStdoutLog(),this.stdout.autoReload&&this.startReloadTimer()},loadStdoutLog:function(){var t=this;this.pod&&(console.log("load pod ".concat(this.pod," stdout log")),this.stdout.loading=!0,Object(i["g"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines,this.stdout.previous).then((function(e){t.stdout.content=e.data;var a=t;setTimeout((function(){a.scrollStdoutLogView()}),200),setTimeout((function(){a.scrollStdoutLogView()}),500),setTimeout((function(){a.scrollStdoutLogView()}),700),t.stdout.lastReloadTime=(new Date).toLocaleTimeString()})).catch((function(e){t.$message.error(e.message),t.stopReloadTimer()})).finally((function(){t.stdout.loading=!1})))},podStdoutLogDownload:function(){Object(i["c"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines)},stopReload:function(){this.stdout.autoReload=!1,this.stopReloadTimer()},scrollStdoutLogView:function(){var t=document.getElementById("stdout-log-content");t.scrollTop=t.scrollHeight},startReloadTimer:function(){this.stdout.reloadTimer&&this.stopReloadTimer();var t=this;this.stdout.reloadTimer=setInterval((function(){t.loadStdoutLog()}),1e3*t.stdout.reloadPeriod),console.log("started pod stdout log reload timer :"+this.stdout.reloadTimer)},stopReloadTimer:function(){clearInterval(this.stdout.reloadTimer),console.log("stopped pod stdout log reload timer :"+this.stdout.reloadTimer)},autoReloadSwitch:function(t){this.stdout.autoReload=t,t?this.startReloadTimer():this.stopReloadTimer()}}},r=s,l=(a("fbec"),a("2877")),c=Object(l["a"])(r,o,n,!1,null,"6ff71a9f",null);e["a"]=c.exports},cfed:function(t,e,a){},db3d:function(t,e,a){"use strict";a("46305")},de9b:function(t,e,a){"use strict";a("b674")},e0c2:function(t,e,a){},e18c:function(t,e,a){},fbec:function(t,e,a){"use strict";a("e18c")}}]);
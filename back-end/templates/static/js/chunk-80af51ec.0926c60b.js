(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-80af51ec"],{"03b6":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-descriptions",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"margin-top",attrs:{column:t.columns,size:"mini",border:"",labelClassName:"app-desc-label"}},[n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        应用\n        "),n("el-tooltip",{attrs:{content:"应用名称",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",{staticStyle:{"min-width":"160px"}},[t._v("\n        "+t._s(t.appMeta.name)+"\n        "),n("clipboard-icon",{attrs:{text:t.appMeta.name}}),t._v(" "),t.appMeta.level?n("el-tooltip",{attrs:{content:"服务等级",placement:"top"}},[n("el-tag",{staticStyle:{"margin-left":"20px","font-size":"12px"},attrs:{size:"mini",type:"warning",title:"应用级别"}},[t._v(t._s(t.appMeta.level))])],1):t._e()],1)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("管理员\n        "),n("el-tooltip",{attrs:{content:"拥有应用的所有权限，包括修改发布权限、修改管理员等",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v("\n      "+t._s(t.appMeta.admins&&t.appMeta.admins.length>0?t.appMeta.admins.join(","):"--")+"\n      "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n      ")])],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        负责人\n        "),n("el-tooltip",{attrs:{content:"服务的维护人员，能够接收到应用的告警信息，维护地址：https://oss.foneshare.cn/cms/edit/config/25292",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),t.appMeta.owner?t.appMeta.owner.length>16?n("div",[n("el-tooltip",{attrs:{content:t.appMeta.owner,placement:"top"}},[n("span",[t._v(t._s(t.appMeta.owner.substring(0,16))+"...")])])],1):n("div",[t._v("\n        "+t._s(t.appMeta.owner)+"\n      ")]):n("div",[t._v("\n        --\n      ")])],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        发布权限\n        "),n("el-tooltip",{attrs:{content:"如果配置了权限，则拥有权限的同学才能发布。如果没有配置，则所有人都可发布。",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",[t.appMeta.orgs&&t.appMeta.orgs.length>0?[n("div",{staticStyle:{"max-width":"400px"}},[t._v("\n            部门："+t._s(t.appMeta.orgs.join(","))+"\n            "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.orgPage}},[t._v("查看部门成员\n            ")])],1)]:[t._v("-任何人-\n        ")],t._v(" "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appAuthPage}},[t._v("编辑\n        ")])],2)],2),t._v(" "),n("el-descriptions-item",{attrs:{span:2}},[n("template",{slot:"label"},[t._v("\n        发布窗口\n        "),n("el-tooltip",{attrs:{content:"如果配置了时间窗口，则只能在时间窗口范围内才能发布。如果没有配置，则任意时间都可以发布。",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",[t.appMeta.timeWindow&&t.appMeta.timeWindow.length>0?[n("el-tooltip",{attrs:{effect:"light",placement:"top"}},[n("div",{attrs:{slot:"content"},slot:"content"},[n("pre",[t._v(t._s(t.appMeta.timeWindowDesc))])]),t._v(" "),n("span",{staticStyle:{display:"inline-block"}},[t._v("\n              "+t._s(t.appMeta.timeWindowDesc.substring(0,60).replaceAll("\n","  ")+(t.appMeta.timeWindowDesc.length>60?"...":""))+"\n            ")])]),t._v(" "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n          ")]),t._v(" "),this.excludeNamespaces.length>0?n("div",{staticStyle:{color:"#d45e0c","font-weight":"bold","max-width":"800px"}},[t._v("以下环境不受限制："+t._s(this.excludeNamespaces.join(", ")))]):t._e()]:[t._v("-任何时间-\n          "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n          ")])]],2)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        描述\n        "),n("el-tooltip",{attrs:{content:"应用描述信息",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v("\n      "+t._s(t.appMeta.remark)+"\n    ")],2)],1)],1)},r=[],o=(n("c5f6"),n("b562")),i=n("da37"),p={name:"pipeline-app",components:{ClipboardIcon:i["a"]},props:{app:{type:String,default:"",required:!0},columns:{type:Number,default:2,required:!1}},mounted:function(){this.loadApp()},watch:{app:function(t){this.loadApp()}},computed:{excludeNamespaces:function(){return this.$settings.timeWindow&&this.$settings.timeWindow.excludeNamespaces?this.$settings.timeWindow.excludeNamespaces:[]}},data:function(){return{loading:!1,appMeta:{}}},methods:{loadApp:function(){var t=this;this.app?(this.loading=!0,Object(o["j"])(this.app).then((function(e){t.appMeta=e.data})).catch((function(e){t.$message.error(e.message),t.appMeta={}})).finally((function(){t.loading=!1}))):console.log(this.app)},appAuthPage:function(){var t=this.$router.resolve({name:"app-permission",query:{app:this.app}}).href;window.open(t,"_blank")},orgPage:function(){var t=this.$router.resolve({name:"auth-org",query:{}}).href;window.open(t,"_blank")},appEditPage:function(){var t=this.$router.resolve({name:"app-list",query:{showEditDialog:"true",app:this.app}}).href;window.open(t,"_blank")}}},c=p,s=(n("9df5"),n("2877")),u=Object(s["a"])(c,a,r,!1,null,null,null);e["a"]=u.exports},"11e9":function(t,e,n){var a=n("52a7"),r=n("4630"),o=n("6821"),i=n("6a99"),p=n("69a8"),c=n("c69a"),s=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?s:function(t,e){if(t=o(t),e=i(e,!0),c)try{return s(t,e)}catch(n){}if(p(t,e))return r(!a.f.call(t,e),t[e])}},"181b":function(t,e,n){},"5dbc":function(t,e,n){var a=n("d3f4"),r=n("8b97").set;t.exports=function(t,e,n){var o,i=e.constructor;return i!==n&&"function"==typeof i&&(o=i.prototype)!==n.prototype&&a(o)&&r&&r(t,o),t}},"71df":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",{staticStyle:{"margin-bottom":"10px",height:"30px",overflow:"hidden"}},[n("label",{staticStyle:{display:"inline-block",width:"80px",color:"#999","font-size":"14px","padding-right":"12px","text-align":"right"}},[t._v("访问历史")]),t._v(" "),t._l(t.recentApps,(function(e){return n("el-button",{staticStyle:{"font-weight":"bold","margin-bottom":"5px","font-size":"14px",padding:"7px"},attrs:{size:"mini",type:"primary",plain:e!==t.currApp},on:{click:function(n){return t.changeCurrAppByBtn(e)}}},[t._v(t._s(e)+"\n    ")])}))],2),t._v(" "),n("div",[n("el-form",[n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"选择应用","label-width":"80px"}},[n("el-select",{staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{filterable:"",placeholder:"请选择应用"},on:{change:t.changeCurrAppBySelector},model:{value:t.currApp,callback:function(e){t.currApp=e},expression:"currApp"}},t._l(t.apps,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1)],1),t._v(" "),t.currApp&&t.showDetail?n("pipeline-app",{attrs:{app:this.currApp,columns:4}}):t._e()],1)},r=[],o=n("db72"),i=n("c24f"),p=n("b562"),c=n("03b6"),s={name:"app-selector2",components:{PipelineApp:c["a"]},props:{showDetail:{type:Boolean,default:!1},updateHistory:{type:Boolean,default:!1}},data:function(){return{apps:[],recentApps:[],currApp:"",reloadHistory:!1}},watch:{currApp:function(t){this.appChange()}},computed:{},mounted:function(){var t=this.$route.query.app;t&&(this.currApp=t),this.loadApps(),this.loadRecentApps()},methods:{appChange:function(){var t=this;this.$router.push({query:Object(o["a"])(Object(o["a"])({},this.$route.query),{},{tab:this.currTab})}),this.updateHistory&&Object(i["e"])(this.currApp).then((function(e){t.reloadHistory&&t.loadRecentApps()})).catch((function(t){})),this.$emit("change",this.currApp)},changeCurrAppByBtn:function(t){this.reloadHistory=!1,this.currApp=t},changeCurrAppBySelector:function(){this.reloadHistory=!0},loadRecentApps:function(){var t=this;Object(i["a"])().then((function(e){t.recentApps=e.data.recentApps,!t.currApp&&t.recentApps.length>0&&(t.currApp=t.recentApps[0])})).catch((function(t){console.log(t)}))},loadApps:function(){var t=this;Object(p["l"])().then((function(e){t.apps=e.data})).catch((function(e){t.$message.error("加载应用数据出错！ "+e.message)}))}}},u=s,l=n("2877"),d=Object(l["a"])(u,a,r,!1,null,null,null);e["a"]=d.exports},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return r})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return i})),n.d(e,"c",(function(){return p})),n.d(e,"b",(function(){return c})),n.d(e,"i",(function(){return s})),n.d(e,"d",(function(){return u})),n.d(e,"f",(function(){return l})),n.d(e,"e",(function(){return d}));var a=n("b775");function r(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function p(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function s(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function u(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function l(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,r,o){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:r,deployTag:o||""}})}},"8b97":function(t,e,n){var a=n("d3f4"),r=n("cb7c"),o=function(t,e){if(r(t),!a(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,a){try{a=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),a(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:a(t,n),t}}({},!1):void 0),check:o}},9093:function(t,e,n){var a=n("ce10"),r=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return a(t,r)}},"9df5":function(t,e,n){"use strict";n("181b")},aa77:function(t,e,n){var a=n("5ca1"),r=n("be13"),o=n("79e5"),i=n("fdef"),p="["+i+"]",c="​",s=RegExp("^"+p+p+"*"),u=RegExp(p+p+"*$"),l=function(t,e,n){var r={},p=o((function(){return!!i[t]()||c[t]()!=c})),s=r[t]=p?e(d):i[t];n&&(r[n]=s),a(a.P+a.F*p,"String",r)},d=l.trim=function(t,e){return t=String(r(t)),1&e&&(t=t.replace(s,"")),2&e&&(t=t.replace(u,"")),t};t.exports=l},b144:function(t,e,n){"use strict";function a(t){return JSON.parse(JSON.stringify(t))}function r(t){if(!t||!(t instanceof Date))return"";var e=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();return e}function o(t){return"isCoreApp"===t?"核心服务":"onlyDeployTag"===t?"只允许部署Tag":"addSysctlKeepalive"===t?"调整内核参数":"skyWalkingAgent"===t?"性能跟踪":"appLogToKafka"===t?"接入ClickHouse日志":"buildUseRuntimeJDK"===t?"镜像JDK版本编译代码":"jvmGcLog"===t?"GC日志":t}n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return o}))},b562:function(t,e,n){"use strict";n.d(e,"p",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"l",(function(){return p})),n.d(e,"j",(function(){return c})),n.d(e,"d",(function(){return s})),n.d(e,"i",(function(){return u})),n.d(e,"h",(function(){return l})),n.d(e,"m",(function(){return d})),n.d(e,"o",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"e",(function(){return h})),n.d(e,"c",(function(){return g})),n.d(e,"k",(function(){return v})),n.d(e,"q",(function(){return b})),n.d(e,"n",(function(){return _})),n.d(e,"g",(function(){return y}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function o(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function p(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function c(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function s(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function l(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function f(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function v(t,e){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function b(t,e){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(t){return Object(a["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:t}})}},c5f6:function(t,e,n){"use strict";var a=n("7726"),r=n("69a8"),o=n("2d95"),i=n("5dbc"),p=n("6a99"),c=n("79e5"),s=n("9093").f,u=n("11e9").f,l=n("86cc").f,d=n("aa77").trim,f="Number",m=a[f],h=m,g=m.prototype,v=o(n("2aeb")(g))==f,b="trim"in String.prototype,_=function(t){var e=p(t,!1);if("string"==typeof e&&e.length>2){e=b?e.trim():d(e,3);var n,a,r,o=e.charCodeAt(0);if(43===o||45===o){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===o){switch(e.charCodeAt(1)){case 66:case 98:a=2,r=49;break;case 79:case 111:a=8,r=55;break;default:return+e}for(var i,c=e.slice(2),s=0,u=c.length;s<u;s++)if(i=c.charCodeAt(s),i<48||i>r)return NaN;return parseInt(c,a)}}return+e};if(!m(" 0o1")||!m("0b1")||m("+0x1")){m=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof m&&(v?c((function(){g.valueOf.call(n)})):o(n)!=f)?i(new h(_(e)),n,m):_(e)};for(var y,x=n("9e1e")?s(h):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;x.length>w;w++)r(h,y=x[w])&&!r(m,y)&&l(m,y,u(h,y));m.prototype=g,g.constructor=m,n("2aba")(a,f,m)}},da37:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[n("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?n("span",[t._v(t._s(this.buttonText))]):t._e()])},r=[],o={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},i=o,p=n("2877"),c=Object(p["a"])(i,a,r,!1,null,null,null);e["a"]=c.exports},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);
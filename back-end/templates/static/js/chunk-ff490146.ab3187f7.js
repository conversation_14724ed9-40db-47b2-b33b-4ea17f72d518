(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ff490146"],{"0a49":function(t,e,i){var n=i("9b43"),a=i("626a"),s=i("4bf8"),o=i("9def"),r=i("cd1c");t.exports=function(t,e){var i=1==t,c=2==t,l=3==t,d=4==t,u=6==t,p=5==t||u,h=e||r;return function(e,r,f){for(var m,g,v=s(e),w=a(v),b=n(r,f,3),y=o(w.length),S=0,_=i?h(e,y):c?h(e,0):void 0;y>S;S++)if((p||S in w)&&(m=w[S],g=b(m,S,v),t))if(i)_[S]=g;else if(g)switch(t){case 3:return!0;case 5:return m;case 6:return S;case 2:_.push(m)}else if(d)return!1;return u?-1:l||d?d:_}}},1169:function(t,e,i){var n=i("2d95");t.exports=Array.isArray||function(t){return"Array"==n(t)}},"20d6":function(t,e,i){"use strict";var n=i("5ca1"),a=i("0a49")(6),s="findIndex",o=!0;s in[]&&Array(1)[s]((function(){o=!1})),n(n.P+n.F*o,"Array",{findIndex:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),i("9c6c")(s)},"2fdb":function(t,e,i){"use strict";var n=i("5ca1"),a=i("d2c8"),s="includes";n(n.P+n.F*i("5147")(s),"String",{includes:function(t){return!!~a(this,t,s).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},5147:function(t,e,i){var n=i("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[n]=!1,!"/./"[t](e)}catch(a){}}return!0}},"58d1":function(t,e,i){"use strict";i("6a7f")},6762:function(t,e,i){"use strict";var n=i("5ca1"),a=i("c366")(!0);n(n.P,"Array",{includes:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),i("9c6c")("includes")},"67e1":function(t,e,i){"use strict";var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container log-file-container"},[i("div",{style:this.btnStyle},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"点击进入到容器里",placement:"top"}},[i("el-button",{staticClass:"el-icon-bank-card",attrs:{type:"text"},on:{click:t.podShell}},[t._v("进入容器")])],1),t._v(" "),i("el-tooltip",{staticClass:"item",staticStyle:{"margin-left":"10px"},attrs:{effect:"dark",content:"上传文件到当前目录（支持批量上传）",placement:"top"}},[i("el-button",{staticClass:"el-icon-upload",staticStyle:{"margin-left":"30px"},attrs:{type:"text"},on:{click:function(e){t.uploadDialogVisible=!0}}},[t._v("上传文件\n      ")])],1),t._v(" "),this.fromPage?t._e():i("el-tooltip",{staticClass:"item",staticStyle:{"margin-left":"10px"},attrs:{effect:"dark",content:"在新窗口打开当前页面",placement:"top"}},[i("router-link",{attrs:{to:{name:"pod-file-page",query:{cluster:this.cluster,namespace:this.namespace,pod:this.pod,path:this.path}},target:"_blank"}},[i("i",{staticClass:"el-icon-news",staticStyle:{color:"#409EFF","font-size":"14px","font-weight":"500"}},[t._v("新窗口打开")])])],1)],1),t._v(" "),i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{attrs:{label:"",prop:"cluster"}},[i("el-input",{staticStyle:{width:"160px"},attrs:{placeholder:"",disabled:!0},model:{value:this.cluster,callback:function(e){t.$set(this,"cluster",e)},expression:"this.cluster"}})],1),t._v(" "),i("el-form-item",{attrs:{prop:"namespace"}},[i("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"",disabled:!0},model:{value:this.namespace,callback:function(e){t.$set(this,"namespace",e)},expression:"this.namespace"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"",prop:"pod"}},[i("el-input",{staticStyle:{width:"340px"},attrs:{disabled:!0},model:{value:this.pod,callback:function(e){t.$set(this,"pod",e)},expression:"this.pod"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"",prop:"path"}},[i("el-input",{staticStyle:{width:"180px"},attrs:{disabled:!0},model:{value:this.path,callback:function(e){t.$set(this,"path",e)},expression:"this.path"}})],1)],1),t._v(" "),t.multipleSelection.length>0?i("el-button",{staticClass:"el-icon-paperclip",staticStyle:{"margin-bottom":"10px"},attrs:{size:"mini"},on:{click:t.archiveFiles}},[t._v("\n    文件打包\n  ")]):t._e(),t._v(" "),i("el-table",{attrs:{data:t.tableData,"element-loading-text":"Loading",fit:"","highlight-current-row":"","row-class-name":t.tableRowClassName},on:{"selection-change":t.selectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),i("el-table-column",{attrs:{type:"index"}}),t._v(" "),i("el-table-column",{attrs:{label:"类型",width:"60",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isDir?i("svg-icon",{staticStyle:{fill:"#f6af34"},attrs:{"icon-class":"folder"}}):e.row.name.includes(".hprof")?i("span",{staticStyle:{color:"red","font-weight":"bold"}},[t._v("OOM")]):e.row.name.endsWith(".log")||e.row.name.endsWith(".txt")?i("svg-icon",{attrs:{"icon-class":"txt"}}):e.row.name.endsWith(".zip")||e.row.name.endsWith(".gz")?i("svg-icon",{attrs:{"icon-class":"zip"}}):e.row.name.endsWith(".svg")?i("svg-icon",{attrs:{"icon-class":"svg"}}):e.row.name.endsWith(".htm")||e.row.name.endsWith(".html")?i("svg-icon",{attrs:{"icon-class":"html"}}):i("svg-icon",{attrs:{"icon-class":"file-unknown"}})]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"文件名",sortable:"","min-width":"200",prop:"name"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isDir?i("div",{staticStyle:{display:"inline-block"}},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"点击进入目录",placement:"top"}},[i("span",{staticStyle:{color:"#3a8ee6",cursor:"pointer","font-weight":"bold"},on:{click:function(i){return t.entryDir(e.row.name)}}},[t._v("\n            "+t._s(".."===e.row.name?".. (上级目录）":e.row.name)+"\n          ")])])],1):i("div",{staticStyle:{display:"inline-block"}},[t._v("\n          "+t._s(e.row.name)+"\n        ")]),t._v(" "),i("div",{staticStyle:{display:"inline-block","padding-right":"10px"}},[e.row.name.startsWith("gc.log")?i("div",[i("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",effect:"plain",size:"mini"}},[t._v("GC文件")]),t._v(" "),i("el-tooltip",{attrs:{placement:"top",content:"可以通过可视化工具进行分析，推荐：gceasy、gcviewer"}},[i("el-tag",{attrs:{type:"info",effect:"plain",size:"mini"}},[i("i",{staticClass:"el-icon-info help-text-icon",staticStyle:{"font-size":"12px"}},[t._v("提示")])])],1)],1):e.row.name.includes(".hprof")?i("div",[i("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",effect:"plain",size:"mini"}},[t._v("\n              OOM-Dump文件\n            ")]),t._v(" "),i("el-tooltip",{attrs:{placement:"top",content:"请通过预览按钮查看如何在线分析dump文件"}},[i("el-tag",{attrs:{type:"info",effect:"plain",size:"mini"}},[i("i",{staticClass:"el-icon-info help-text-icon",staticStyle:{"font-size":"12px"}},[t._v("提示")])])],1)],1):e.row.name.startsWith("tomcat-access")?i("div",[i("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",effect:"plain",size:"mini"}},[t._v("\n              http访问日志\n            ")])],1):e.row.name.startsWith("thread-dump")?i("div",[i("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",effect:"plain",size:"mini"}},[t._v("\n              线程dump文件\n            ")])],1):e.row.name.startsWith("log_before_restart")?i("div",[i("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",effect:"plain",size:"mini"}},[t._v("\n              重启前的日志文件\n            ")])],1):t._e()])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"大小",sortable:"",prop:"humanizeSize"}}),t._v(" "),i("el-table-column",{attrs:{label:"修改时间",sortable:"",prop:"modifyTime"}}),t._v(" "),i("el-table-column",{attrs:{label:"操作",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isDir?t._e():i("div",[e.row.size<=t.downloadLimitSize||e.row.name.endsWith(".jprof")||e.row.name.endsWith(".zip")?i("el-button",{attrs:{type:"text",icon:"el-icon-download"},on:{click:function(i){return t.download(e.row)}}},[t._v("下载\n          ")]):t._e(),t._v(" "),e.row.name.endsWith(".svg")||e.row.name.endsWith(".htm")||e.row.name.endsWith(".html")?i("el-button",{attrs:{type:"text",icon:"el-icon-view"},on:{click:function(i){return t.preview(e.row)}}},[t._v("预览\n          ")]):t._e()],1)]}}])})],1),t._v(" "),i("el-dialog",{attrs:{title:"文件上传",visible:t.uploadDialogVisible,width:"32%","close-on-click-modal":!1,"append-to-body":""},on:{"update:visible":function(e){t.uploadDialogVisible=e}}},[i("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{action:"/api/v1/k8s/pod/file/upload",multiple:!0,"file-list":t.uploadData.fileList,"on-progress":t.uploadProgress,"on-success":t.uploadSuccess,"on-error":t.uploadError,limit:50,name:"file",data:this.searchForm,"auto-upload":!1}},[i("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[t._v("选取文件")]),t._v(" "),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:t.uploadFiles}},[t._v("上传到服务器")]),t._v(" "),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("备注： 上传的文件在容器删除后也会一并被删除，不建议上传太大的文件")])],1),t._v(" "),i("div",{staticStyle:{height:"50px"}})],1)],1)},a=[],s=(i("20d6"),i("aef6"),i("6762"),i("2fdb"),i("f559"),i("7f7f"),i("2d63")),o=i("a527"),r={props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!0},path:{type:String,default:"/opt/tomcat/logs",required:!1},fromPage:{type:Boolean,default:!1,required:!1}},watch:{cluster:function(t){this.searchForm.cluster=t,this.loadFile()},namespace:function(t){this.searchForm.namespace=t,this.loadFile()},pod:function(t){this.searchForm.pod=t,this.loadFile()},path:function(t){this.searchForm.path=t,this.loadFile()}},data:function(){return{dedicatedCloudDownloadMaxSize:104857600,searchForm:{cluster:"",namespace:"",pod:"",path:""},downloadLimitSize:536870912e3,tableData:[],pageLoading:!1,uploadDialogVisible:!1,uploadData:{fileList:[]},multipleSelection:[],highLightRow:""}},computed:{isDedicatedCloud:function(){if(this.cluster){var t,e=Object(s["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var i=t.value;if(this.cluster===i.name)return"fxiaokeCloud"!==i.cloudCategory}}catch(n){e.e(n)}finally{e.f()}}return!1},btnStyle:function(){return this.fromPage?{}:{marginTop:"-45px",zIndex:9999,position:"absolute",marginLeft:"120px"}}},mounted:function(){this.$route.query.downloadLimitSize?this.downloadLimitSize=parseInt(this.$route.query.downloadLimitSize):this.downloadLimitSize=536870912e3,this.searchForm.cluster=this.cluster,this.searchForm.namespace=this.namespace,this.searchForm.pod=this.pod,this.searchForm.path=this.path,this.$route.query.dedicatedCloudDownloadMaxSize&&(this.dedicatedCloudDownloadMaxSize=parseInt(this.$route.query.dedicatedCloudDownloadMaxSize)),this.loadFile()},methods:{loadFile:function(){var t=this;this.path.startsWith("/opt")||(this.$message.warning("只允许查看 /opt 目录下的文件"),this.path="/opt"),this.pageLoading=!0,Object(o["f"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))},podShell:function(){var t="/api/page/redirect?type=webShell&cluster=".concat(this.cluster,"&namespace=").concat(this.namespace,"&pods=").concat(this.pod);window.open(t)},parentDir:function(){var t=this.path;if(t.includes("/")&&"/"!==t){var e=t.substring(0,t.lastIndexOf("/"));this.path=e||"/"}else this.$message.warning("已经是最顶层目录了")},entryDir:function(t){if(".."!==t){var e=this.path;e=e.endsWith("/")?"".concat(this.path).concat(t):"".concat(this.path,"/").concat(t),this.path=e}else this.parentDir()},parseFilePath:function(t){return t.endsWith("/")?t.slice(0,-1):t},preview:function(t){var e=this,i={cluster:this.cluster,namespace:this.namespace,pod:this.pod,path:this.parseFilePath(this.path)+"/"+t.name,fileSize:t.size};t.size>10485760?this.$message.error("文件大于10MB，无法预览"):(this.pageLoading=!0,Object(o["r"])(i).then((function(t){e.pageLoading=!1,Object(o["p"])(t.data)})).catch((function(t){e.pageLoading=!1,e.$message.error(t.message)})))},download:function(t){var e=this,i={cluster:this.cluster,namespace:this.namespace,pod:this.pod,path:this.parseFilePath(this.path)+"/"+t.name,fileSize:t.size};this.isDedicatedCloud&&(console.log("download file from dedicated cloud"),t.size>this.dedicatedCloudDownloadMaxSize)?this.$message.error("为避免网络带宽占用，专属云不允许下载 ".concat(Math.floor(this.dedicatedCloudDownloadMaxSize/1024/1024),"MB 以上的文件。如果有需要请联系 @吴志辉 处理")):(this.pageLoading=!0,Object(o["r"])(i).then((function(i){e.pageLoading=!1,Object(o["b"])(i.data,t.name)})).catch((function(t){e.pageLoading=!1,e.$message.error(t.message)})))},uploadFiles:function(){this.$refs.upload.submit()},uploadProgress:function(t,e,i){},uploadSuccess:function(t,e,i){if(200===t.code?this.$message.success("上传成功，文件名：".concat(e.name)):(this.$message.error("上传失败，文件名：".concat(e.name,"，msg: ").concat(t.message)),i.splice(i.findIndex((function(t){return t.name===e.name})),1)),i&&i.length>0&&e.name===i[i.length-1].name){var n=this;setTimeout((function(){n.loadFile()}),1e3)}},uploadError:function(t,e,i){this.$message.error("上传失败，文件名：".concat(e.name,"，msg: ").concat(t))},selectionChange:function(t){this.multipleSelection=t},archiveFiles:function(){var t=this,e=this.multipleSelection.map((function(t){return t.size})).reduce((function(t,e){return t+e}));if(e>5368709120)this.$message.warning("总文件大小不能超过5G");else{var i=this.multipleSelection.map((function(t){return t.name})),n={cluster:this.cluster,namespace:this.namespace,pod:this.pod,dir:this.parseFilePath(this.path),files:i};Object(o["a"])(n).then((function(e){t.pageLoading=!1,t.tableRowClassName=function(t){var i=t.row;t.rowIndex;return i.name===e.data?"high-light-row":""},t.loadFile(),t.$message.success("归档成功，文件名："+e.data)})).catch((function(e){t.pageLoading=!1,t.$message.error(e.message)}))}},tableRowClassName:function(t){t.row,t.rowIndex;return""}}},c=r,l=(i("58d1"),i("2877")),d=Object(l["a"])(c,n,a,!1,null,null,null);e["a"]=d.exports},"6a7f":function(t,e,i){},a527:function(t,e,i){"use strict";i.d(e,"h",(function(){return a})),i.d(e,"d",(function(){return s})),i.d(e,"i",(function(){return o})),i.d(e,"e",(function(){return r})),i.d(e,"g",(function(){return c})),i.d(e,"c",(function(){return l})),i.d(e,"k",(function(){return d})),i.d(e,"l",(function(){return u})),i.d(e,"m",(function(){return p})),i.d(e,"o",(function(){return h})),i.d(e,"f",(function(){return f})),i.d(e,"r",(function(){return m})),i.d(e,"b",(function(){return g})),i.d(e,"a",(function(){return v})),i.d(e,"p",(function(){return w})),i.d(e,"q",(function(){return b})),i.d(e,"n",(function(){return y})),i.d(e,"j",(function(){return S}));i("96cf"),i("3b8d");var n=i("b775");function a(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:t,namespace:e,app:i}})}function s(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:t,namespace:e,app:i}})}function o(t,e){return Object(n["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:t,namespace:e}})}function r(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:t,namespace:e,pod:i}})}function c(t,e,i,a,s,o){return Object(n["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:t,namespace:e,pod:i,container:a,tailLines:s,previous:o}})}function l(t,e,i,n,a){var s="/api/v1/k8s/pod/stdout/download?cluster=".concat(t,"&namespace=").concat(e,"&pod=").concat(i,"&container=").concat(n,"&tailLines=").concat(a,'&_time="')+(new Date).getTime();window.open(s)}function d(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:t,namespace:e,pod:i}})}function u(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:t,namespace:e,pod:i}})}function p(t){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:t}})}function h(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:t,namespace:e,pod:i}})}function f(t){return Object(n["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:t})}function m(t){return Object(n["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:t})}function g(t,e){window.open("/api/v1/k8s/pod/file/download?fileId="+t+"&fileName="+e+"&_time="+(new Date).getTime())}function v(t){return Object(n["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:t})}function w(t){window.open("/api/v1/k8s/pod/file/preview?fileId="+t)}function b(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:t,namespace:e,pod:i}})}function y(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:t,namespace:e,pod:i}})}function S(t,e,i){return Object(n["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:t,namespace:e,pod:i}})}},aae3:function(t,e,i){var n=i("d3f4"),a=i("2d95"),s=i("2b4c")("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[s])?!!e:"RegExp"==a(t))}},aef6:function(t,e,i){"use strict";var n=i("5ca1"),a=i("9def"),s=i("d2c8"),o="endsWith",r=""[o];n(n.P+n.F*i("5147")(o),"String",{endsWith:function(t){var e=s(this,t,o),i=arguments.length>1?arguments[1]:void 0,n=a(e.length),c=void 0===i?n:Math.min(a(i),n),l=String(t);return r?r.call(e,l,c):e.slice(c-l.length,c)===l}})},cd1c:function(t,e,i){var n=i("e853");t.exports=function(t,e){return new(n(t))(e)}},d2c8:function(t,e,i){var n=i("aae3"),a=i("be13");t.exports=function(t,e,i){if(n(e))throw TypeError("String#"+i+" doesn't accept regex!");return String(a(t))}},e853:function(t,e,i){var n=i("d3f4"),a=i("1169"),s=i("2b4c")("species");t.exports=function(t){var e;return a(t)&&(e=t.constructor,"function"!=typeof e||e!==Array&&!a(e.prototype)||(e=void 0),n(e)&&(e=e[s],null===e&&(e=void 0))),void 0===e?Array:e}},f559:function(t,e,i){"use strict";var n=i("5ca1"),a=i("9def"),s=i("d2c8"),o="startsWith",r=""[o];n(n.P+n.F*i("5147")(o),"String",{startsWith:function(t){var e=s(this,t,o),i=a(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),n=String(t);return r?r.call(e,n,i):e.slice(i,i+n.length)===n}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1106c729"],{"2ffc":function(t,n,e){"use strict";e.r(n);var i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container",staticStyle:{position:"relative"}},[e("div",{staticStyle:{position:"absolute",right:"10px",top:"20px"}},[e("el-button",{staticStyle:{padding:"0",float:"right"},attrs:{type:"text"},on:{click:t.clear_cache}},[t._v("清除系统缓存\n    ")])],1),t._v(" "),e("div",{staticStyle:{position:"relative"}},[e("el-tabs",{attrs:{"tab-position":"top"}},[e("el-tab-pane",{attrs:{label:"settings.json"}},[e("div",{staticStyle:{"font-size":"14px",position:"absolute",right:"10px",top:"0","z-index":"999"}},[e("clipboard-icon",{attrs:{text:JSON.stringify(this.settings,null,2),"button-text":"一键复制"}})],1),t._v(" "),e("vue-json-pretty",{attrs:{data:this.settings}})],1),t._v(" "),e("el-tab-pane",{attrs:{label:"oncall.json"}},[e("div",{staticStyle:{"font-size":"14px",position:"absolute",right:"10px",top:"0","z-index":"999"}},[e("clipboard-icon",{attrs:{text:JSON.stringify(this.oncallConfig,null,2),"button-text":"一键复制"}})],1),t._v(" "),e("vue-json-pretty",{attrs:{data:this.oncallConfig}})],1)],1)],1)])},o=[],a=e("d538"),s=e.n(a),c=e("6797"),l=e("da37"),r={data:function(){return{loading:!1,oncallConfig:{err:"数据未加载"},settings:{}}},mounted:function(){this.loadOncallConfig(),this.settings=this.$settings},methods:{loadOncallConfig:function(){var t=this;Object(c["b"])().then((function(n){t.oncallConfig=n.data})).catch((function(n){t.$message.error("oncall config load fail, err:"+n.message)}))},clear_cache:function(){var t=this;this.loading=!0,Object(c["a"])().then((function(n){t.$message.success("操作成功")})).catch((function(n){t.$message.error(n.message)})).finally((function(){t.loading=!1}))}},components:{ClipboardIcon:l["a"],VueJsonPretty:s.a}},u=r,d=e("2877"),p=Object(d["a"])(u,i,o,!1,null,null,null);n["default"]=p.exports},6797:function(t,n,e){"use strict";e.d(n,"c",(function(){return o})),e.d(n,"b",(function(){return a})),e.d(n,"a",(function(){return s}));var i=e("b775");function o(t){return Object(i["a"])({url:"/v1/sys/log",method:"get",params:{tailLines:t}})}function a(){return Object(i["a"])({url:"/v1/sys/oncall-config",method:"get"})}function s(){return Object(i["a"])({url:"/v1/sys/setting/cache",method:"delete"})}},da37:function(t,n,e){"use strict";var i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(n){return t.copyToClipboard()}}},[e("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?e("span",[t._v(t._s(this.buttonText))]):t._e()])},o=[],a={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,n=this.text;n?navigator.clipboard.writeText(n).then((function(){t.$message.success("复制成功")})).catch((function(){var e=document.createElement("input");document.body.appendChild(e),e.setAttribute("value",n),e.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(e),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},s=a,c=e("2877"),l=Object(c["a"])(s,i,o,!1,null,null,null);n["a"]=l.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3c600b0d"],{"29e3":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container",staticStyle:{"max-width":"840px"}},[a("el-divider",{staticStyle:{padding:"10px 0"},attrs:{"content-position":"center"}},[a("b",{staticStyle:{"font-size":"1.2em"}},[e._v("说明")])]),e._v(" "),a("div",{staticStyle:{"font-size":"14px",color:"#444"}},[e._v("\n    非Java系列的应用我们是通过Gitlab CI 来部署到k8s里面，大家可以通过当前页面配置和下载部署文件。"),a("br"),e._v("\n    Wiki文档：\n    "),a("a",{attrs:{target:"_blank",href:"http://wiki.firstshare.cn/pages/viewpage.action?pageId=103195231"}},[a("el-button",{attrs:{type:"text"}},[e._v("http://wiki.firstshare.cn/pages/viewpage.action?pageId=103195231")])],1),e._v("\n    有不明白的地方可以联系运维同学\n  ")]),e._v(" "),a("el-divider",{staticStyle:{padding:"10px 0"},attrs:{"content-position":"center"}},[a("b",{staticStyle:{"font-size":"1.2em"}},[e._v("GitLab CI 部署配置文件下载")])]),e._v(" "),a("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"应用名",prop:"app"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"right-start"}},[a("div",{staticStyle:{"max-width":"400px"},attrs:{slot:"content"},slot:"content"},[e._v("\n          输入部署到k8s的应用名，一般跟Git工程名一致\n        ")]),e._v(" "),a("el-input",{model:{value:e.form.app,callback:function(t){e.$set(e.form,"app",t)},expression:"form.app"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"开发语言",prop:"language"}},[a("el-select",{model:{value:e.form.language,callback:function(t){e.$set(e.form,"language",t)},expression:"form.language"}},e._l(e.languageOption,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"实例数"}},[a("el-input-number",{attrs:{step:1,min:1,max:10},model:{value:e.form.replicas,callback:function(t){e.$set(e.form,"replicas",t)},expression:"form.replicas"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"CPU"}},[a("el-input-number",{attrs:{step:.1,precision:2,min:.1,max:8},model:{value:e.form.cpu,callback:function(t){e.$set(e.form,"cpu",t)},expression:"form.cpu"}}),e._v("  （单位：Core）\n    ")],1),e._v(" "),a("el-form-item",{attrs:{label:"内存"}},[a("el-input-number",{attrs:{step:256,min:125,max:10240},model:{value:e.form.memory,callback:function(t){e.$set(e.form,"memory",t)},expression:"form.memory"}}),e._v("  （单位：MB）\n    ")],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-checkbox-group",{model:{value:e.form.namespace,callback:function(t){e.$set(e.form,"namespace",t)},expression:"form.namespace"}},[a("el-col",{attrs:{span:6}},[a("div",[a("el-checkbox",{attrs:{label:"fstest",border:""}})],1),e._v(" "),a("div",[a("el-checkbox",{attrs:{label:"firstshare",border:""}})],1),e._v(" "),a("div",[a("el-checkbox",{attrs:{label:"foneshare",border:""}})],1)]),e._v(" "),a("el-col",{attrs:{span:6}},[a("div",[a("el-checkbox",{attrs:{label:"fstest-gray",border:""}})],1),e._v(" "),a("div",[a("el-checkbox",{attrs:{label:"firstshare-gray",border:""}})],1),e._v(" "),a("div",[a("el-checkbox",{attrs:{label:"foneshare-gray",border:""}})],1)])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"端口",prop:"ports"}},e._l(e.form.ports,(function(t){return a("div",{staticStyle:{margin:"5px 0"}},[a("el-input",{staticStyle:{display:"inline-block",width:"200px"},attrs:{placeholder:"端口名",clearable:"",disabled:t.disabled},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"item.name"}}),e._v("\n        =\n        "),a("el-input-number",{staticStyle:{display:"inline-block",width:"150px"},attrs:{placeholder:"端口值","controls-position":"right",min:0,max:65535,step:1e3,disabled:t.disabled},model:{value:t.port,callback:function(a){e.$set(t,"port",a)},expression:"item.port"}}),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.removable,expression:"item.removable"}],on:{click:function(a){return a.preventDefault(),e.removePort(t)}}},[e._v("删除")])],1)})),0),e._v(" "),a("el-form-item",[a("el-button",{on:{click:e.addPort}},[e._v("添加端口")]),e._v(" "),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.onSubmit("form")}}},[e._v("下载【k8s manifest】文件")])],1)],1)],1)},n=[],o=(a("7f7f"),a("6a4c")),l={data:function(){var e=function(e,t,a){for(var r in console.log(t),t){var n=t[r];if(!n.name||!n.port)return void a(new Error("请输入所有端口的名称和值"))}a()};return{submitLoading:!1,languageOption:[{value:"Javascript",label:"Javascript"},{value:"Python",label:"Python"},{value:"Golang",label:"Golang"},{value:"Java",label:"Java"},{value:"PHP",label:"PHP"},{value:"Unknown",label:"Unknown"}],form:{app:"",language:"",namespace:[],replicas:1,cpu:.2,memory:256,ports:[{name:"http",port:80,disabled:!0,removable:!1}]},rules:{app:[{required:!0,message:"请输入应用名称"},{min:4,message:"最少4个字符"}],language:[{required:!0,message:"请选择语言"}],namespace:[{type:"array",required:!0,message:"请至少选择一个运行环境"}],ports:[{validator:e,message:"请输入所有端口的名称和值"}]}}},mounted:function(){},methods:{onSubmit:function(e){var t=this;this.$refs[e].validate((function(e){e&&(t.submitLoading=!0,Object(o["a"])(t.form).then((function(e){t.submitLoading=!1,Object(o["b"])(e.data)})).catch((function(){t.submitLoading=!1})))}))},addPort:function(){this.form.ports.push({name:"",value:"",disabled:!1,removable:!0})},removePort:function(e){var t=this.form.ports.indexOf(e);-1!==t&&this.form.ports.splice(t,1)}}},i=l,s=(a("6cea"),a("2877")),c=Object(s["a"])(i,r,n,!1,null,"0a7352b4",null);t["default"]=c.exports},"6a4c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return l}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/gitlab/ci-file/create",method:"post",data:e})}function o(e){window.open("/api/v1/gitlab/ci-file/download?fileId="+e)}function l(e){return Object(r["a"])({url:"/v1/gitlab/tags",method:"post",data:e})}},"6cea":function(e,t,a){"use strict";a("ce47")},ce47:function(e,t,a){}}]);
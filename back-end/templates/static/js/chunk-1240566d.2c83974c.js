(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1240566d"],{"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"e",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"l",(function(){return u})),a.d(t,"m",(function(){return p})),a.d(t,"a",(function(){return c})),a.d(t,"f",(function(){return i})),a.d(t,"i",(function(){return s})),a.d(t,"j",(function(){return d})),a.d(t,"k",(function(){return m})),a.d(t,"n",(function(){return b})),a.d(t,"g",(function(){return f})),a.d(t,"b",(function(){return v})),a.d(t,"h",(function(){return h})),a.d(t,"o",(function(){return g}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function o(e){return Object(n["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function l(e,t,a){return Object(n["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function u(e){return Object(n["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(n["a"])({url:"/v1/pipeline/all",method:"get"})}function i(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function s(e){return Object(n["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(n["a"])({url:"/v1/pipeline",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function b(e){return Object(n["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function f(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function v(e){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function h(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function g(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"757c":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"app-redeploy-v2"}}),e._v(" "),a("el-alert",{attrs:{title:"应用批量重发V2(CI/CD分离模式）",type:"info",description:"使用当前运行的版本重新发布应用。","show-icon":""}}),e._v(" "),a("el-row",{staticStyle:{"max-width":"1080px","margin-top":"10px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:18}},[a("el-form",[a("el-form-item",[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"内容格式：集群/环境/应用名，多个之间用换行分割"},model:{value:e.searchForm,callback:function(t){e.searchForm=t},expression:"searchForm"}})],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:6}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.loadTableData}},[e._v("查询")]),a("br"),a("br"),e._v(" "),a("el-checkbox",{model:{value:e.openVersionCompare,callback:function(t){e.openVersionCompare=t},expression:"openVersionCompare"}},[e._v("开启版本对比")])],1)],1),e._v(" "),a("el-table",{attrs:{data:e.tableData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{type:"selection",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"发布过？",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.extraAttr.reDeployed)+"\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.openVersionCompare&&"k8s0"!==t.row.cluster?e._e():a("div",[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.reDeploy(t.row)}}},[e._v("使用当前版本重发\n          ")]),e._v(" "),a("br"),e._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy-history",query:{}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[e._v("\n              发布历史\n            ")])],1),e._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:t.row.app}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[e._v("\n              发布流程\n            ")])],1)],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",width:"100px",prop:"status"}}),e._v(" "),a("el-table-column",{attrs:{label:"实例数(运行/配置)",align:"center",prop:"replicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.openVersionCompare&&"k8s0"===t.row.cluster&&!e.replicasIsSame(t.row.app,t.row.namespace)?a("div",{staticStyle:{color:"#FB5151"}},[e._v("\n          "+e._s(t.row.extraAttr.runningPodNum)+" / "+e._s(t.row.replicas)+"\n        ")]):a("div",[e._v("\n          "+e._s(t.row.extraAttr.runningPodNum)+" / "+e._s(t.row.replicas)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"版本",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.openVersionCompare&&"k8s0"===t.row.cluster&&!e.versionIsSame(t.row.app,t.row.namespace)?a("div",{staticStyle:{color:"#FB5151"}},[e._v("\n        "+e._s(t.row.extraAttr.deployTag)+"\n      ")]):a("div",[e._v("\n        "+e._s(t.row.extraAttr.deployTag)+"\n      ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"发版信息"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"10px"}},[a("div",[e._v("发布人:"+e._s(t.row.extraAttr.deployUser))]),e._v(" "),a("div",[e._v(e._s(t.row.extraAttr.deployRemark))])])]}}])})],1)],1)},r=[],o=a("2d63"),l=a("a68b"),u=a("51a9"),p=a("8504"),c=a("76fe"),i={components:{appManageTab:l["a"]},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{pageLoading:!1,openVersionCompare:!1,searchForm:"",tableData:[],deployRemark:""}},methods:{loadTableData:function(){var e=this;this.pageLoading=!0,this.tableData=[],Object(u["m"])(this.searchForm).then((function(t){var a,n=Object(o["a"])(t.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.extraAttr.deployTag="?",r.extraAttr.runningPodNum="?",r.extraAttr.reDeployed="NO",e.findDeployment(r)}}catch(l){n.e(l)}finally{n.f()}e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1}))},versionIsSame:function(e,t){var a,n="",r="",l=Object(o["a"])(this.tableData);try{for(l.s();!(a=l.n()).done;){var u=a.value;"k8s0"===u.cluster&&u.namespace===t&&u.app===e&&(n=u.extraAttr.deployTag),"k8s1"===u.cluster&&u.namespace===t&&u.app===e&&(r=u.extraAttr.deployTag)}}catch(p){l.e(p)}finally{l.f()}return""!==n&&""!==r&&n===r},replicasIsSame:function(e,t){var a,n="",r="",l="",u="",p=Object(o["a"])(this.tableData);try{for(p.s();!(a=p.n()).done;){var c=a.value;"k8s0"===c.cluster&&c.namespace===t&&c.app===e&&(n=c.replicas,l=c.runningPodNum),"k8s1"===c.cluster&&c.namespace===t&&c.app===e&&(r=c.replicas,u=c.runningPodNum)}}catch(i){p.e(i)}finally{p.f()}return n===r&&l===u},findDeployment:function(e){null!==e?Object(p["a"])(e.cluster,e.namespace,e.app).then((function(t){e.extraAttr.deployTag=t.data.deployTag,e.extraAttr.runningPodNum=t.data.replicas,e.extraAttr.deployRemark=t.data.deployRemark,e.extraAttr.deployUser=t.data.deployUser})).catch((function(e){console.log(e.message)})):this.$message.warning("找不到发布流程，应用：".concat(cluster," / ").concat(namespace," / ").concat(app))},reDeploy:function(e){var t=this;this.$prompt("请填写发版备注","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:this.deployRemark}).then((function(a){var n=a.value;t.deployRemark=n,Object(c["f"])(e.cluster,e.namespace,e.app,n).then((function(a){t.$message.success("操作成功"),e.extraAttr.reDeployed="YES"})).catch((function(e){t.$message.error(e.message)}))}))}}},s=i,d=a("2877"),m=Object(d["a"])(s,n,r,!1,null,null,null);t["default"]=m.exports},"76fe":function(e,t,a){"use strict";a.d(t,"k",(function(){return r})),a.d(t,"i",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return u})),a.d(t,"b",(function(){return p})),a.d(t,"f",(function(){return c})),a.d(t,"c",(function(){return i})),a.d(t,"g",(function(){return s})),a.d(t,"h",(function(){return d})),a.d(t,"d",(function(){return m})),a.d(t,"l",(function(){return b})),a.d(t,"j",(function(){return f}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/job/search",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:e}})}function l(e){return Object(n["a"])({url:"/v1/job/build-image",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/v1/job/deploy-app",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/v1/job/build-and-deploy",method:"post",data:e})}function c(e,t,a,r){return Object(n["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:e,namespace:t,app:a,remark:r}})}function i(e,t,a,r){return Object(n["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:e,namespace:t,app:a,remark:r}})}function s(e){return Object(n["a"])({url:"/v1/job/detail",method:"get",params:{id:e}})}function d(e){return Object(n["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:e}})}function m(e){return Object(n["a"])({url:"/v1/job/cancel",method:"put",data:{id:e}})}function b(e,t){return Object(n["a"])({url:"/v1/job/update-status?id=".concat(e,"&status=").concat(t),method:"put"})}function f(e){return Object(n["a"])({url:"/v1/job/redo",method:"post",data:{id:e}})}},8504:function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"c",(function(){return u})),a.d(t,"b",(function(){return p})),a.d(t,"i",(function(){return c})),a.d(t,"d",(function(){return i})),a.d(t,"f",(function(){return s})),a.d(t,"e",(function(){return d}));var n=a("b775");function r(e,t){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:e,namespace:t}})}function o(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:e})}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:e,namespace:t,app:a}})}function p(e){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:e})}function c(e){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:e})}function i(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function s(e){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:e})}function d(e,t,a,r,o){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:e,namespace:t,app:a,revision:r,deployTag:o||""}})}},a68b:function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},r=[],o=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),l=o,u=(a("d54f"),a("2877")),p=Object(u["a"])(l,n,r,!1,null,null,null);t["a"]=p.exports},d54f:function(e,t,a){"use strict";a("eba1")},eba1:function(e,t,a){}}]);
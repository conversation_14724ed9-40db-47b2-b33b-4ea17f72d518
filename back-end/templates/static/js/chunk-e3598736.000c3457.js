(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e3598736"],{8504:function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return c})),a.d(t,"c",(function(){return l})),a.d(t,"b",(function(){return u})),a.d(t,"i",(function(){return p})),a.d(t,"d",(function(){return s})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return d}));var r=a("b775");function n(e,t){return Object(r["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:e,namespace:t}})}function o(e,t,a){return Object(r["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:e,namespace:t,app:a}})}function c(e){return Object(r["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:e})}function l(e,t,a){return Object(r["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:e,namespace:t,app:a}})}function u(e){return Object(r["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:e})}function p(e){return Object(r["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:e})}function s(e,t,a){return Object(r["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function i(e){return Object(r["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:e})}function d(e,t,a,n,o){return Object(r["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:e,namespace:t,app:a,revision:n,deployTag:o||""}})}},"9ebb":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("app-manage-tab",{attrs:{"active-name":"helm-chart-build"}}),e._v(" "),a("div",[a("el-form",{ref:"searchForm",attrs:{model:e.searchForm}},[a("el-form-item",{attrs:{label:"部署到纯私有环境"}},[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},on:{change:e.cloudTypeChange},model:{value:e.privateCloud,callback:function(t){e.privateCloud=t},expression:"privateCloud"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"选择集群",prop:"cluster"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择k8s集群",disabled:e.privateCloud},on:{change:e.loadApp},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"选择环境",prop:"namespace"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择Namespace",filterable:"",disabled:e.privateCloud},on:{change:e.loadApp},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"选择应用",prop:"app"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{filterable:"",placeholder:"选择应用",multiple:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},[a("el-option",{attrs:{value:"",label:"全部"}}),e._v(" "),e._l(this.appOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}))],2)],1),e._v(" "),e.privateCloud?a("el-form-item",{attrs:{label:"覆盖环境"}},[a("el-select",{staticStyle:{width:"340px"},model:{value:e.searchForm.overrideNamespace,callback:function(t){e.$set(e.searchForm,"overrideNamespace",t)},expression:"searchForm.overrideNamespace"}},[a("el-option",{attrs:{value:"cmhk-crm-di-std",label:"招商局集团测试（cmhk-crm-di-std）"}})],1)],1):e._e(),e._v(" "),a("el-form-item",[a("div",{staticStyle:{"margin-top":"20px","padding-left":"160px"}},[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text"},on:{click:e.logHistory}},[e._v("查看历史结果")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.createHelmChartBuildJob}},[e._v("开始构建")])],1)])],1)],1),e._v(" "),e.jobResult.length>0?a("div",{staticStyle:{"margin-top":"20px",border:"1px solid #ccc","background-color":"#eee",padding:"5px",width:"760px","font-size":"12px"}},e._l(e.jobResult,(function(t){return a("div",[e._v("\n      "+e._s(t)+"\n    ")])})),0):e._e()],1)},n=[],o=(a("7f7f"),a("2d63")),c=a("a68b"),l=a("8504"),u=a("c1ab"),p={components:{appManageTab:c["a"]},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(r){t.e(r)}finally{t.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:"",overrideNamespace:""},appOptions:[],jobResult:[],privateCloud:!1}},methods:{cloudTypeChange:function(){console.log(this.privateCloud),this.privateCloud?(this.searchForm.cluster="forceecrm-k8s1",this.searchForm.namespace="forceecrm-public-prod",this.loadApp()):this.searchForm.overrideNamespace=""},loadApp:function(){var e=this.searchForm.cluster,t=this.searchForm.namespace;if(e&&t){var a=this;Object(l["g"])(e,t).then((function(e){a.appOptions=e.data})).catch((function(e){console.error(e)}))}else this.appOptions=[]},createHelmChartBuildJob:function(){var e=this;!this.privateCloud||this.searchForm.overrideNamespace?Object(u["l"])(this.searchForm.cluster,this.searchForm.namespace,this.searchForm.app,this.searchForm.overrideNamespace).then((function(t){e.jobResult.push("- 操作成功，构建完成后结果会存储到审计日志。构建服务比较多时，耗时会比较长。"+t.data)})).catch((function(t){e.$message.error(t.message)})).finally((function(){})):this.$message.warning("私有部署环境，必须选择覆盖环境")},logHistory:function(){var e=this.$router.resolve({name:"log-list",query:{operate:"helm-chart-build-job"}});window.open(e.href,"_blank")}}},s=p,i=a("2877"),d=Object(i["a"])(s,r,n,!1,null,null,null);t["default"]=d.exports},a68b:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"服务定时重启",name:"app-cron-reboot"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程-资源批量修改",name:"pipeline-resource-update"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"专属云应用发布助手",name:"dedicated-cloud-publish-helper"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"helm chart创建",name:"helm-chart-build"}})],1)],1)},n=[],o=(a("7f7f"),{props:{activeName:{type:String,default:""}},mounted:function(){},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;"app-restart"===a?this.$router.push({name:"tool-app-restart"}):"app-redeploy"===a?this.$router.push({name:"tool-app-redeploy"}):"app-redeploy-v2"===a?this.$router.push({name:"tool-app-redeploy-v2"}):"app-deploy"===a?this.$router.push({name:"tool-app-deploy"}):"app-build-v2"===a?this.$router.push({name:"tool-app-build-v2"}):"pipeline-clone"===a?this.$router.push({name:"tool-pipeline-clone-by-namespace"}):"dedicated-cloud-publish-helper"===a?this.$router.push({name:"tool-dedicated-cloud-publish-helper"}):"app-gc"===a?this.$router.push({name:"tool-app-gc"}):"app-cron-reboot"===a?this.$router.push({name:"tool-app-reboot"}):"yaml-export"===a?this.$router.push({name:"tool-yaml-export"}):"helm-chart-build"===a?this.$router.push({name:"tool-helm-chart-build"}):"pipeline-resource-update"===a?this.$router.push({name:"tool-pipeline-resource-update"}):"cms-config-migrate"===a?this.$router.push({name:"tool-cms-config-migrate"}):"pod-deregister-manage"===a?this.$router.push({name:"tool-pod-deregister-manage"}):this.$message.error("未知操作")}}}),c=o,l=(a("d54f"),a("2877")),u=Object(l["a"])(c,r,n,!1,null,null,null);t["a"]=u.exports},c1ab:function(e,t,a){"use strict";a.d(t,"i",(function(){return n})),a.d(t,"k",(function(){return o})),a.d(t,"y",(function(){return c})),a.d(t,"z",(function(){return l})),a.d(t,"d",(function(){return u})),a.d(t,"g",(function(){return p})),a.d(t,"C",(function(){return s})),a.d(t,"E",(function(){return i})),a.d(t,"x",(function(){return d})),a.d(t,"b",(function(){return m})),a.d(t,"f",(function(){return h})),a.d(t,"B",(function(){return f})),a.d(t,"D",(function(){return b})),a.d(t,"w",(function(){return v})),a.d(t,"F",(function(){return g})),a.d(t,"l",(function(){return y})),a.d(t,"e",(function(){return O})),a.d(t,"a",(function(){return j})),a.d(t,"A",(function(){return k})),a.d(t,"j",(function(){return _})),a.d(t,"h",(function(){return x})),a.d(t,"r",(function(){return F})),a.d(t,"u",(function(){return $})),a.d(t,"v",(function(){return C})),a.d(t,"n",(function(){return w})),a.d(t,"o",(function(){return N})),a.d(t,"s",(function(){return S})),a.d(t,"t",(function(){return T})),a.d(t,"c",(function(){return q})),a.d(t,"p",(function(){return A})),a.d(t,"q",(function(){return R})),a.d(t,"m",(function(){return H}));var r=a("b775");function n(e){return Object(r["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:e}})}function o(e){return Object(r["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:e}})}function c(e){return Object(r["a"])({url:"/v1/tool/scan-jar",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:e})}function u(){return Object(r["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function p(e){return Object(r["a"])({url:"/v1/tool/app-restart/create",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/v1/tool/app-restart/start",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:e})}function m(){return Object(r["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function h(e,t,a,n,o,c,l,u){return Object(r["a"])({url:"/v1/tool/app-deploy/create?type=".concat(e,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(t,"&suffixVersion=").concat(a,"&message=").concat(n,"&dependencyCheck=").concat(c,"&parentPom=").concat(l),method:"post",data:u})}function f(e){return Object(r["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:e})}function v(e){return Object(r["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:e})}function g(e){return Object(r["a"])({url:"/v1/tool/yaml-export",method:"post",data:e})}function y(e,t,a,n){return Object(r["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(e,"&namespace=").concat(t,"&app=").concat(a,"&overrideNamespace=").concat(n),method:"post"})}function O(e,t,a,n,o){return Object(r["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(e,"&namespace=").concat(t,"&version=").concat(a,"&remark=").concat(n,"&dryRun=").concat(o),method:"post"})}function j(){return Object(r["a"])({url:"/v1/tool/all-app-owners",method:"get"})}function k(){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function _(e){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(e),method:"get"})}function x(e){return Object(r["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(e),method:"delete"})}function F(e){return Object(r["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:e})}function $(e){return Object(r["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:e})}function C(e){return Object(r["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:e})}function w(e){return Object(r["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:e})}function N(e){return Object(r["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:e})}function S(e){return Object(r["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:e})}function T(e,t){return Object(r["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+e,method:"post",data:t})}function q(e){return Object(r["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:e})}function A(e){return Object(r["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:e})}function R(e){return Object(r["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:e})}function H(e,t,a){return Object(r["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(e,"&namespace=").concat(t,"&op=").concat(a),method:"get"})}},d54f:function(e,t,a){"use strict";a("eba1")},eba1:function(e,t,a){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-320aab56"],{"504c":function(e,t,n){var a=n("9e1e"),l=n("0d58"),i=n("6821"),r=n("52a7").f;e.exports=function(e){return function(t){var n,s=i(t),o=l(s),c=o.length,u=0,p=[];while(c>u)n=o[u++],a&&!r.call(s,n)||p.push(e?[n,s[n]]:s[n]);return p}}},"51a9":function(e,t,n){"use strict";n.d(t,"c",(function(){return l})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return r})),n.d(t,"l",(function(){return s})),n.d(t,"m",(function(){return o})),n.d(t,"a",(function(){return c})),n.d(t,"f",(function(){return u})),n.d(t,"i",(function(){return p})),n.d(t,"j",(function(){return d})),n.d(t,"k",(function(){return m})),n.d(t,"n",(function(){return v})),n.d(t,"g",(function(){return b})),n.d(t,"b",(function(){return f})),n.d(t,"h",(function(){return _})),n.d(t,"o",(function(){return h}));var a=n("b775");function l(e){return Object(a["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function i(e){return Object(a["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function r(e,t,n){return Object(a["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:n}})}function s(e){return Object(a["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(a["a"])({url:"/v1/pipeline/all",method:"get"})}function u(e){return Object(a["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(a["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(a["a"])({url:"/v1/pipeline",method:"post",data:e})}function m(e){return Object(a["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function v(e){return Object(a["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function b(e,t,n,l){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:l}})}function f(e){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function _(e,t,n,l){return Object(a["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:l}})}function h(e){return Object(a["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"579f":function(e,t,n){},"768b":function(e,t,n){"use strict";var a=n("a745"),l=n.n(a);function i(e){if(l()(e))return e}var r=n("67bb"),s=n.n(r),o=n("5d58"),c=n.n(o);function u(e,t){var n=null==e?null:"undefined"!==typeof s.a&&e[c.a]||e["@@iterator"];if(null!=n){var a,l,i=[],r=!0,o=!1;try{for(n=n.call(e);!(r=(a=n.next()).done);r=!0)if(i.push(a.value),t&&i.length===t)break}catch(u){o=!0,l=u}finally{try{r||null==n["return"]||n["return"]()}finally{if(o)throw l}}return i}}var p=n("e630");function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){return i(e)||u(e,t)||Object(p["a"])(e,t)||d()}n.d(t,"a",(function(){return m}))},"815f":function(e,t,n){"use strict";n("d1b7")},"837b":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{style:{fontSize:e.fontsize}},[e.pipeline.app?n("div",{staticClass:"expand-row-wrapper"},[n("div",{staticClass:"expand-row-item"},[n("label",[e._v("备注")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.remark||"--"))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("k8s集群")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.cluster))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("基础镜像")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.baseImage.substring(e.pipeline.baseImage.lastIndexOf("/")+1)))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("资源池调度")]),e._v(" "),n("span",[e._v("调度策略 ("+e._s(e.pipeline.schedule.strategy)+")")]),e._v(" "),n("span",[e._v("资源池 ("+e._s(e.pipeline.schedule.node)+")")])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("部署策略")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.deployStrategy))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("选项开关")]),e._v(" "),n("div",{staticStyle:{"padding-left":"10px",display:"inline-block"}},[n("el-checkbox-group",{model:{value:e.pipeOptions,callback:function(t){e.pipeOptions=t},expression:"pipeOptions"}},e._l(e.pipeOptionsAll,(function(t){return n("el-checkbox",{staticClass:"pipeline-option",style:{fontSize:e.fontsize},attrs:{label:t,size:"mini",disabled:""}})})),1)],1)]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("健康检测")]),e._v(" "),n("div",{staticStyle:{"padding-left":"10px",display:"inline-block"}},[n("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"启动检测",checked:e.pipeline.startupProbe.enable,size:"mini",disabled:""}}),e._v(" "),n("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"存活检查",checked:e.pipeline.livenessProbe.enable,size:"mini",disabled:""}}),e._v(" "),n("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"就绪检测",checked:e.pipeline.readinessProbe.enable,size:"mini",disabled:""}})],1)]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("JVM参数")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.jvmOpts||"--"))])]),e._v(" "),e.pipeline.pvc.enable?n("div",{staticClass:"expand-row-item"},[n("label",[e._v("持久存储")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.pvc.enable?e.pipeline.pvc.name:""))])]):e._e(),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("关闭前保留时间")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.preStopRetainSeconds))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("关闭前回调地址")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.preStopWebhook||"--"))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("Eolinker任务数")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.eolinkerIDs&&e.pipeline.eolinkerIDs.length>0?e.pipeline.eolinkerIDs.length:"--"))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("Webhook")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.webhook.url||"--"))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("端口")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.ports.map((function(e){return e.value})).join(",")))])]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("自定义环境变量")]),e._v(" "),n("span",[e._l(e.pipeline.envs,(function(t,a){return["USER"===t.type?n("div",{staticStyle:{display:"inline-block","padding-right":"20px"}},[e._v("\n            "+e._s(t.name)+"="+e._s(t.value)+"\n          ")]):e._e()]}))],2)]),e._v(" "),n("div",{staticClass:"expand-row-item"},[n("label",[e._v("创建时间")]),e._v(" "),n("span",[e._v(e._s(e.pipeline.createdTime))])])]):n("div",[e._v("\n    数据丢失\n  ")])])},l=[],i=n("768b"),r=(n("ac6a"),n("ffc1"),n("b144")),s={props:{pipeline:{type:Object,default:function(){return{}},required:!0},fontsize:{type:String,default:"12px",required:!1}},mounted:function(){for(var e=0,t=Object.entries(this.pipeline.options);e<t.length;e++){var n=Object(i["a"])(t[e],2),a=n[0],l=n[1];this.pipeOptionsAll.push(Object(r["b"])(a)),l&&this.pipeOptions.push(Object(r["b"])(a))}this.pipeOptionsOrigin=Object(r["a"])(this.pipeOptions)},computed:{},data:function(){return{pipeOptionsOrigin:[],pipeOptions:[],pipeOptionsAll:[]}},methods:{}},o=s,c=(n("d152"),n("2877")),u=Object(c["a"])(o,a,l,!1,null,null,null);t["a"]=u.exports},b144:function(e,t,n){"use strict";function a(e){return JSON.parse(JSON.stringify(e))}function l(e){if(!e||!(e instanceof Date))return"";var t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds();return t}function i(e){return"isCoreApp"===e?"核心服务":"onlyDeployTag"===e?"只允许部署Tag":"addSysctlKeepalive"===e?"调整内核参数":"skyWalkingAgent"===e?"性能跟踪":"appLogToKafka"===e?"接入ClickHouse日志":"buildUseRuntimeJDK"===e?"镜像JDK版本编译代码":"jvmGcLog"===e?"GC日志":e}n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return i}))},b562:function(e,t,n){"use strict";n.d(t,"p",(function(){return l})),n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"l",(function(){return s})),n.d(t,"j",(function(){return o})),n.d(t,"d",(function(){return c})),n.d(t,"i",(function(){return u})),n.d(t,"h",(function(){return p})),n.d(t,"m",(function(){return d})),n.d(t,"o",(function(){return m})),n.d(t,"f",(function(){return v})),n.d(t,"e",(function(){return b})),n.d(t,"c",(function(){return f})),n.d(t,"k",(function(){return _})),n.d(t,"q",(function(){return h})),n.d(t,"n",(function(){return g})),n.d(t,"g",(function(){return y}));var a=n("b775");function l(e){return Object(a["a"])({url:"/v1/app/search",method:"get",params:e})}function i(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function r(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function s(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function o(e){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(a["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:n}})}function m(e){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function v(e){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function _(e,t){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function h(e,t){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function g(e,t){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function y(e){return Object(a["a"])({url:"/v1/app/create-health-review-in-crm",method:"post",params:{app:e}})}},bad7:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container sync-config-to-other",attrs:{"element-loading-text":"拼命加载中"}},[n("div",{staticStyle:{margin:"10px auto","text-align":"center"}},[n("el-checkbox-group",{on:{change:e.columnChange},model:{value:e.columns,callback:function(t){e.columns=t},expression:"columns"}},[n("el-checkbox",{attrs:{label:"选项开关",checked:!0}}),e._v(" "),n("el-checkbox",{attrs:{label:"部署模块"}}),e._v(" "),n("el-checkbox",{attrs:{label:"CPU"}}),e._v(" "),n("el-checkbox",{attrs:{label:"内存"}}),e._v(" "),n("el-checkbox",{attrs:{label:"副本数"}}),e._v(" "),n("el-checkbox",{attrs:{label:"流程状态"}}),e._v(" "),n("el-checkbox",{attrs:{label:"基础镜像"}}),e._v(" "),n("el-checkbox",{attrs:{label:"Webhook"}}),e._v(" "),n("el-checkbox",{attrs:{label:"端口"}})],1)],1),e._v(" "),n("el-row",[n("el-col",{attrs:{span:24}},[n("el-card",[n("el-table",{attrs:{data:e.pipelineSrc,"element-loading-text":"数据加载中...",size:"small",fit:""}},[n("el-table-column",{attrs:{label:"应用",prop:"app"}}),e._v(" "),n("el-table-column",{attrs:{label:"环境",prop:"namespace","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{"font-weight":"bold"}},[e._v("\n                "+e._s(t.row.namespace)+" （"+e._s(t.row.cluster)+"）\n              ")])]}}])}),e._v(" "),this.columns.includes("选项开关")?n("el-table-column",{key:Math.random(),attrs:{label:"选项开关","min-width":"400"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row.options,(function(t,a,l){return[n("el-checkbox",{attrs:{label:e.pipelineOptionDesc(a),checked:t,disabled:""}})]}))]}}],null,!1,1008408390)}):e._e(),e._v(" "),this.columns.includes("部署模块")?n("el-table-column",{key:Math.random(),attrs:{label:"部署模块","min-width":"400"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-table",{attrs:{data:t.row.appModules,size:"mini",border:""}},[n("el-table-column",{attrs:{type:"index",width:"40"}}),e._v(" "),n("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址","min-width":"200px"}}),e._v(" "),n("el-table-column",{attrs:{prop:"module",label:"子模块"}}),e._v(" "),n("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}})],1)]}}],null,!1,2035001323)}):e._e(),e._v(" "),this.columns.includes("CPU")?n("el-table-column",{key:Math.random(),attrs:{label:"CPU",prop:"resources.limitCPU"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n              CPU\n              "),n("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return t.row.resources?[e._v("\n              "+e._s(t.row.resources.requestCPU.toFixed(1))+" - "+e._s(t.row.resources.limitCPU.toFixed(1))+"\n            ")]:void 0}}],null,!0)}):e._e(),e._v(" "),this.columns.includes("内存")?n("el-table-column",{key:Math.random(),attrs:{prop:"resources.limitMemory"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n              内存 (MB)\n              "),n("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return t.row.resources?[e._v("\n              "+e._s(t.row.resources.requestMemory)+" - "+e._s(t.row.resources.limitMemory)+"\n            ")]:void 0}}],null,!0)}):e._e(),e._v(" "),this.columns.includes("副本数")?n("el-table-column",{key:Math.random(),attrs:{label:"副本数",prop:"replicas"}}):e._e(),e._v(" "),this.columns.includes("流程状态")?n("el-table-column",{key:Math.random(),attrs:{label:"状态",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["enabled"===t.row.status?n("el-tag",{attrs:{type:"success",size:"small"}},[e._v("\n                "+e._s(e.convertStatus(t.row.status))+"\n              ")]):n("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("\n                "+e._s(e.convertStatus(t.row.status))+"\n              ")])]}}],null,!1,506774265)}):e._e(),e._v(" "),this.columns.includes("基础镜像")?n("el-table-column",{key:Math.random(),attrs:{label:"基础镜像"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.baseImage.split("/").pop())+"\n            ")]}}],null,!1,1062962205)}):e._e(),e._v(" "),this.columns.includes("Webhook")?n("el-table-column",{key:Math.random(),attrs:{label:"Webhook"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.webhook)+"\n            ")]}}],null,!1,220369486)}):e._e(),e._v(" "),this.columns.includes("端口")?n("el-table-column",{key:Math.random(),attrs:{label:"端口"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.ports,(function(t){return n("div",[e._v("\n                "+e._s(t.name)+" = "+e._s(t.value)+"\n              ")])}))}}],null,!1,1565022692)}):e._e()],1)],1)],1)],1),e._v(" "),n("el-row",[n("el-col",{attrs:{span:24}},[n("div",{staticStyle:{"text-align":"center",margin:"0 auto"}},[n("div",{staticStyle:{display:"inline-block",width:"100px"}},[n("div",{staticStyle:{display:"flex",margin:"20px auto"}},[n("div",{staticStyle:{display:"inline-block",height:"80px",margin:"0 20px"}},[n("svg-icon",{staticStyle:{"font-size":"60px"},attrs:{"icon-class":"down2"}}),n("br"),e._v(" "),n("span",{staticStyle:{"font-size":"20px"}},[e._v("同步到")])],1)])])])])],1),e._v(" "),n("el-row",[n("el-col",{attrs:{span:24}},[n("el-card",[n("el-table",{ref:"table-dest",attrs:{data:e.pipelineDest,size:"small","element-loading-text":"数据加载中...",fit:""}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"60"}}),e._v(" "),n("el-table-column",{attrs:{label:"应用",prop:"app"}}),e._v(" "),n("el-table-column",{attrs:{label:"环境",prop:"namespace","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{"font-weight":"bold"}},[e._v("\n                "+e._s(t.row.namespace)+" （"+e._s(t.row.cluster)+"）\n              ")])]}}])}),e._v(" "),this.columns.includes("选项开关")?n("el-table-column",{key:Math.random(),attrs:{label:"选项开关","min-width":"400"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row.options,(function(t,a,l){return[n("el-checkbox",{attrs:{label:e.pipelineOptionDesc(a),checked:t,disabled:""}})]}))]}}],null,!1,1008408390)}):e._e(),e._v(" "),this.columns.includes("部署模块")?n("el-table-column",{key:Math.random(),attrs:{label:"部署模块","min-width":"400"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-table",{attrs:{data:t.row.appModules,size:"mini",border:""}},[n("el-table-column",{attrs:{type:"index",width:"40"}}),e._v(" "),n("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址","min-width":"200px"}}),e._v(" "),n("el-table-column",{attrs:{prop:"module",label:"子模块"}}),e._v(" "),n("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}})],1)]}}],null,!1,2035001323)}):e._e(),e._v(" "),this.columns.includes("CPU")?n("el-table-column",{key:Math.random(),attrs:{label:"CPU",prop:"resources.limitCPU"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n              CPU\n              "),n("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return t.row.resources?[e._v("\n              "+e._s(t.row.resources.requestCPU.toFixed(1))+" - "+e._s(t.row.resources.limitCPU.toFixed(1))+"\n            ")]:void 0}}],null,!0)}):e._e(),e._v(" "),this.columns.includes("内存")?n("el-table-column",{key:Math.random(),attrs:{prop:"resources.limitMemory"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n              内存 (MB)\n              "),n("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return t.row.resources?[e._v("\n              "+e._s(t.row.resources.requestMemory)+" - "+e._s(t.row.resources.limitMemory)+"\n            ")]:void 0}}],null,!0)}):e._e(),e._v(" "),this.columns.includes("副本数")?n("el-table-column",{key:Math.random(),attrs:{label:"副本数",prop:"replicas"}}):e._e(),e._v(" "),this.columns.includes("流程状态")?n("el-table-column",{key:Math.random(),attrs:{label:"状态",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["enabled"===t.row.status?n("el-tag",{attrs:{type:"success",size:"small"}},[e._v("\n                "+e._s(e.convertStatus(t.row.status))+"\n              ")]):n("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("\n                "+e._s(e.convertStatus(t.row.status))+"\n              ")])]}}],null,!1,506774265)}):e._e(),e._v(" "),this.columns.includes("基础镜像")?n("el-table-column",{key:Math.random(),attrs:{label:"基础镜像"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.baseImage.split("/").pop())+"\n            ")]}}],null,!1,1062962205)}):e._e(),e._v(" "),this.columns.includes("Webhook")?n("el-table-column",{key:Math.random(),attrs:{label:"Webhook"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.webhook)+"\n            ")]}}],null,!1,220369486)}):e._e(),e._v(" "),this.columns.includes("端口")?n("el-table-column",{key:Math.random(),attrs:{label:"端口"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.ports,(function(t){return n("div",[e._v("\n                "+e._s(t.name)+" = "+e._s(t.value)+"\n              ")])}))}}],null,!1,1565022692)}):e._e()],1)],1)],1)],1),e._v(" "),n("div",{staticStyle:{"margin-top":"10px","margin-bottom":"30px","text-align":"center"}},[n("el-button",{attrs:{type:"warning"},on:{click:e.onAbort}},[e._v("取消")]),e._v(" "),n("el-button",{staticStyle:{"margin-left":"40px"},attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("提交")])],1)],1)},l=[],i=n("2d63"),r=(n("b562"),n("837b")),s=n("51a9"),o=n("b144"),c={components:{PipelineExpand:r["a"]},data:function(){return{loading:!1,app:"",columns:[],pipelineSrcId:null,appModules:[],dialogAppModuleVisible:!1,submitLoading:!1,pipelineSrc:[],pipelineDest:[]}},mounted:function(){this.pipelineSrcId=parseInt(this.$route.query.pipelineId),this.app=this.$route.query.app,this.loadTableData()},computed:{},methods:{pipelineOptionDesc:o["b"],loadTableData:function(){var e=this;this.loading=!0,Object(s["c"])(this.app).then((function(t){var n,a=Object(i["a"])(t.data);try{for(a.s();!(n=a.n()).done;){var l=n.value;l.id===e.pipelineSrcId?e.pipelineSrc.push(l):e.pipelineDest.push(l)}}catch(r){a.e(r)}finally{a.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},columnChange:function(e){this.columns=e},pipelinePage:function(){var e={app:this.app};this.$router.push({name:"cicd-app-deploy",query:e})},convertStatus:function(e){switch(e){case"enabled":return"可用";case"disabled":return"禁用";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}},onAbort:function(){var e=this;this.$confirm("确认取消吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pipelinePage()}))},onSubmit:function(){var e=this,t=this.$createElement,n=this.pipelineSrc[0],a=this.$refs["table-dest"].store.states.selection;!a||a.length<1?this.$message.warning("请选择需要被同步的发布流程"):this.columns.length<1?this.$message.warning("请选择需要被同步的配置项"):this.$msgbox({title:"提示",message:t("p",null,[t("span",{style:"word-break: break-all"},"".concat(n.namespace,"(").concat(n.cluster,") 的配置( ").concat(this.columns.join(",")," )将被同步到 ").concat(a.map((function(e){return"".concat(e.namespace,"(").concat(e.cluster,")")})).join(","))),t("div",null,"是否继续？")]),showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){e.submitLoading=!0;var t={pipelineSrcId:n.id,pipelineDestIds:a.map((function(e){return e.id})),columns:e.columns};Object(s["n"])(t).then((function(t){e.$message.success("操作成功, 请刷新页面查看结果"),e.submitLoading=!1})).catch((function(t){e.$message({dangerouslyUseHTMLString:!0,message:t.message,type:"error"}),e.submitLoading=!1}))}))}}},u=c,p=(n("815f"),n("2877")),d=Object(p["a"])(u,a,l,!1,null,null,null);t["default"]=d.exports},d152:function(e,t,n){"use strict";n("579f")},d1b7:function(e,t,n){},ffc1:function(e,t,n){var a=n("5ca1"),l=n("504c")(!0);a(a.S,"Object",{entries:function(e){return l(e)}})}}]);
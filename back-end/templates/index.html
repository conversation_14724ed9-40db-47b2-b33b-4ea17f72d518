<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href=/favicon.ico><title>K8S应用管理系统</title><link href=/static/css/chunk-elementUI.8fc3034b.css rel=stylesheet><link href=/static/css/chunk-libs.887691f5.css rel=stylesheet><link href=/static/css/app.1068b4f6.css rel=stylesheet></head><body><noscript><strong>We're sorry but K8S应用管理系统 doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id=app></div><script>(function(c){function e(e){for(var u,f,k=e[0],a=e[1],t=e[2],b=0,r=[];b<k.length;b++)f=k[b],h[f]&&r.push(h[f][0]),h[f]=0;for(u in a)Object.prototype.hasOwnProperty.call(a,u)&&(c[u]=a[u]);o&&o(e);while(r.length)r.shift()();return d.push.apply(d,t||[]),n()}function n(){for(var c,e=0;e<d.length;e++){for(var n=d[e],u=!0,f=1;f<n.length;f++){var k=n[f];0!==h[k]&&(u=!1)}u&&(d.splice(e--,1),c=a(a.s=n[0]))}return c}var u={},f={runtime:0},h={runtime:0},d=[];function k(c){return a.p+"static/js/"+({}[c]||c)+"."+{"chunk-001dd103":"4b08714c","chunk-03b95cfe":"82a94c05","chunk-047cc1f8":"ed600389","chunk-0939a8a5":"c296290d","chunk-0ce4a4a0":"8457c522","chunk-10a52e78":"f43f28ea","chunk-1240566d":"2c83974c","chunk-13f6c39c":"69efbb14","chunk-16a6030f":"3e28ebd3","chunk-21e43d0b":"04309102","chunk-2d0c8fb7":"cfa38f15","chunk-2d0e55f3":"4fb1097d","chunk-2d0e95df":"68987768","chunk-2d0f1194":"83447b95","chunk-2d21e36c":"adb9fd02","chunk-1106c729":"8cef1df1","chunk-80af51ec":"0926c60b","chunk-ff490146":"ab3187f7","chunk-f6384076":"641d2d96","chunk-320aab56":"9921b289","chunk-322fefef":"b902dd23","chunk-330e9030":"f051cbad","chunk-338b6501":"c7642789","chunk-36986833":"cbc2e217","chunk-3c600b0d":"06ed9650","chunk-44c83c1f":"c2ad61e8","chunk-48dd5394":"bbc27330","chunk-49b42ff1":"c4cdc392","chunk-360dfa28":"4e1d9fcc","chunk-49b97962":"59694492","chunk-4c5113fa":"053b5d6e","chunk-0569abaf":"5eb70cd8","chunk-28d72371":"c3b90b8c","chunk-7db5cb51":"de28a7b8","chunk-5644595c":"38c7f026","chunk-609b1506":"5dfb4fe6","chunk-6458699b":"be4a22e6","chunk-1906db76":"6dacb3b0","chunk-49f2ddea":"5de03489","chunk-cb89a6f6":"e227af06","chunk-66e3dfb0":"42a74841","chunk-6826591f":"25eb0062","chunk-68e39c2d":"158260b5","chunk-6e25c452":"c0be5582","chunk-706550fc":"b34ebe98","chunk-7078067f":"34c9a017","chunk-75c0e4d8":"c7e650ac","chunk-7d9600f6":"e5c696f2","chunk-7e31027f":"74d7d839","chunk-6f99211b":"f51de52b","chunk-8fecc9e2":"3b325f02","chunk-067f45a4":"eead0791","chunk-12a0dfa5":"70204c9f","chunk-24e44ee8":"370670a6","chunk-7e4cb85e":"691a0468","chunk-0c85b64f":"58983af6","chunk-2f84080c":"0c1e11d5","chunk-59d2e235":"dc868993","chunk-7133f72c":"23ec2dc3","chunk-a6c57e06":"a2cabcd4","chunk-9aee7e76":"4e43cefc","chunk-bd3584a2":"e0735640","chunk-d16aa210":"dbcd99bc","chunk-e3598736":"000c3457","chunk-249bf490":"10f31961","chunk-ba9e7e2e":"236e424a","chunk-2d0bdfa6":"887ed6f3"}[c]+".js"}function a(e){if(u[e])return u[e].exports;var n=u[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.e=function(c){var e=[],n={"chunk-001dd103":1,"chunk-03b95cfe":1,"chunk-047cc1f8":1,"chunk-0939a8a5":1,"chunk-0ce4a4a0":1,"chunk-10a52e78":1,"chunk-1240566d":1,"chunk-13f6c39c":1,"chunk-80af51ec":1,"chunk-ff490146":1,"chunk-f6384076":1,"chunk-320aab56":1,"chunk-330e9030":1,"chunk-338b6501":1,"chunk-36986833":1,"chunk-3c600b0d":1,"chunk-44c83c1f":1,"chunk-48dd5394":1,"chunk-360dfa28":1,"chunk-49b97962":1,"chunk-28d72371":1,"chunk-7db5cb51":1,"chunk-5644595c":1,"chunk-609b1506":1,"chunk-1906db76":1,"chunk-49f2ddea":1,"chunk-cb89a6f6":1,"chunk-68e39c2d":1,"chunk-6e25c452":1,"chunk-706550fc":1,"chunk-7078067f":1,"chunk-75c0e4d8":1,"chunk-7d9600f6":1,"chunk-7e31027f":1,"chunk-6f99211b":1,"chunk-12a0dfa5":1,"chunk-24e44ee8":1,"chunk-7e4cb85e":1,"chunk-0c85b64f":1,"chunk-59d2e235":1,"chunk-7133f72c":1,"chunk-a6c57e06":1,"chunk-9aee7e76":1,"chunk-d16aa210":1,"chunk-e3598736":1,"chunk-249bf490":1,"chunk-ba9e7e2e":1};f[c]?e.push(f[c]):0!==f[c]&&n[c]&&e.push(f[c]=new Promise((function(e,n){for(var u="static/css/"+({}[c]||c)+"."+{"chunk-001dd103":"f219bb77","chunk-03b95cfe":"d0a05406","chunk-047cc1f8":"f219bb77","chunk-0939a8a5":"c3c7bc0d","chunk-0ce4a4a0":"c3c7bc0d","chunk-10a52e78":"c3c7bc0d","chunk-1240566d":"c3c7bc0d","chunk-13f6c39c":"c3c7bc0d","chunk-16a6030f":"31d6cfe0","chunk-21e43d0b":"31d6cfe0","chunk-2d0c8fb7":"31d6cfe0","chunk-2d0e55f3":"31d6cfe0","chunk-2d0e95df":"31d6cfe0","chunk-2d0f1194":"31d6cfe0","chunk-2d21e36c":"31d6cfe0","chunk-1106c729":"31d6cfe0","chunk-80af51ec":"e562d17c","chunk-ff490146":"69d207a9","chunk-f6384076":"9cfa3e17","chunk-320aab56":"5a29655f","chunk-322fefef":"31d6cfe0","chunk-330e9030":"c3c7bc0d","chunk-338b6501":"83b86fc1","chunk-36986833":"c3c7bc0d","chunk-3c600b0d":"a69a0622","chunk-44c83c1f":"afd0de55","chunk-48dd5394":"d56f7089","chunk-49b42ff1":"31d6cfe0","chunk-360dfa28":"b094f99b","chunk-49b97962":"c3c7bc0d","chunk-4c5113fa":"31d6cfe0","chunk-0569abaf":"31d6cfe0","chunk-28d72371":"b6d58e6c","chunk-7db5cb51":"eb8faad8","chunk-5644595c":"c3c7bc0d","chunk-609b1506":"47e929ad","chunk-6458699b":"31d6cfe0","chunk-1906db76":"12b5b7ed","chunk-49f2ddea":"d5a06fde","chunk-cb89a6f6":"b2a9b1ef","chunk-66e3dfb0":"31d6cfe0","chunk-6826591f":"31d6cfe0","chunk-68e39c2d":"c3c7bc0d","chunk-6e25c452":"b094f99b","chunk-706550fc":"48a68644","chunk-7078067f":"c3c7bc0d","chunk-75c0e4d8":"4f780f60","chunk-7d9600f6":"5f985448","chunk-7e31027f":"c3c7bc0d","chunk-6f99211b":"fb4a288d","chunk-8fecc9e2":"31d6cfe0","chunk-067f45a4":"31d6cfe0","chunk-12a0dfa5":"83b86fc1","chunk-24e44ee8":"f219bb77","chunk-7e4cb85e":"8fc50c65","chunk-0c85b64f":"3d0ee905","chunk-2f84080c":"31d6cfe0","chunk-59d2e235":"1dc74cdb","chunk-7133f72c":"dfeeb22a","chunk-a6c57e06":"6891b6e2","chunk-9aee7e76":"3923dff6","chunk-bd3584a2":"31d6cfe0","chunk-d16aa210":"c3c7bc0d","chunk-e3598736":"c3c7bc0d","chunk-249bf490":"c3c7bc0d","chunk-ba9e7e2e":"3322b808","chunk-2d0bdfa6":"31d6cfe0"}[c]+".css",h=a.p+u,d=document.getElementsByTagName("link"),k=0;k<d.length;k++){var t=d[k],b=t.getAttribute("data-href")||t.getAttribute("href");if("stylesheet"===t.rel&&(b===u||b===h))return e()}var r=document.getElementsByTagName("style");for(k=0;k<r.length;k++){t=r[k],b=t.getAttribute("data-href");if(b===u||b===h)return e()}var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",o.onload=e,o.onerror=function(e){var u=e&&e.target&&e.target.src||h,d=new Error("Loading CSS chunk "+c+" failed.\n("+u+")");d.code="CSS_CHUNK_LOAD_FAILED",d.request=u,delete f[c],o.parentNode.removeChild(o),n(d)},o.href=h;var i=document.getElementsByTagName("head")[0];i.appendChild(o)})).then((function(){f[c]=0})));var u=h[c];if(0!==u)if(u)e.push(u[2]);else{var d=new Promise((function(e,n){u=h[c]=[e,n]}));e.push(u[2]=d);var t,b=document.createElement("script");b.charset="utf-8",b.timeout=120,a.nc&&b.setAttribute("nonce",a.nc),b.src=k(c),t=function(e){b.onerror=b.onload=null,clearTimeout(r);var n=h[c];if(0!==n){if(n){var u=e&&("load"===e.type?"missing":e.type),f=e&&e.target&&e.target.src,d=new Error("Loading chunk "+c+" failed.\n("+u+": "+f+")");d.type=u,d.request=f,n[1](d)}h[c]=void 0}};var r=setTimeout((function(){t({type:"timeout",target:b})}),12e4);b.onerror=b.onload=t,document.head.appendChild(b)}return Promise.all(e)},a.m=c,a.c=u,a.d=function(c,e,n){a.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},a.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},a.t=function(c,e){if(1&e&&(c=a(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var u in c)a.d(n,u,function(e){return c[e]}.bind(null,u));return n},a.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return a.d(e,"a",e),e},a.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},a.p="/",a.oe=function(c){throw console.error(c),c};var t=window["webpackJsonp"]=window["webpackJsonp"]||[],b=t.push.bind(t);t.push=e,t=t.slice();for(var r=0;r<t.length;r++)e(t[r]);var o=b;n()})([]);</script><script src=/static/js/chunk-elementUI.a81ac2cd.js></script><script src=/static/js/chunk-libs.45c56889.js></script><script src=/static/js/app.9fd82159.js></script></body></html>
package models

import (
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
)

type App struct {
	gorm.Model
	Name       string               `gorm:"size:64;<-:create;index;unique;not null"`
	Owner      string               `gorm:"size:256"`
	Remark     string               `gorm:"size:1024"`
	Orgs       datatype.StrList     `gorm:"type:text;default:'[]'"`
	Admins     datatype.StrList     `gorm:"type:text;default:'[]'"`
	TimeWindow datatype.TimePeriods `gorm:"type:text;size:2048;default:'[]'"`
	Level      string               `gorm:"size:16"`
	//Admins     datatype.StrList     `gorm:"type:text;size:2048;default:'[]'"`
	//Users  datatype.StrList `gorm:"type:text;default:'[]'"`
}

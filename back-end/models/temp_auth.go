package models

import (
	"fs-k8s-app-manager/pkg/constant"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"time"
)

/*
*
临时授权, 只能授权当天时间段
*/
type TempAuth struct {
	gorm.Model
	App        string `gorm:"size:64;<-:create;index;not null"`
	User       string `gorm:"size:32;index"`
	Operate    string `gorm:"size:32;index"`
	Approver   string `gorm:"size:32;index"`
	Approved   bool   `gorm:"not null;default:false"`
	StartHour  string `gorm:"size:12"`
	EndHour    string `gorm:"size:12"`
	Authorizer string `gorm:"size:32"`
	Remark     string `gorm:"size:256"`
}

func (t TempAuth) NowInTimeRange() bool {
	time1, err := time.Parse("15:04", t.StartHour)
	if err != nil {
		log.Info("StartHour parse fail ", err.Error())
		return false
	}
	time2, err := time.Parse("15:04", t.EndHour)
	if err != nil {
		log.Info("EndHour parse fail ", err.<PERSON>rror())
		return false
	}

	minuteOfDay := time.Now().Hour()*60 + time.Now().Minute()
	minuteStart := time1.Hour()*60 + time1.Minute()
	minuteEnd := time2.Hour()*60 + time2.Minute()
	return minuteStart <= minuteOfDay && minuteOfDay < minuteEnd
}

func (t TempAuth) OperateDesc() string {
	if t.Operate == constant.TEMP_AUTH_OPERATE_DEPLOY {
		return "镜像构建+发布"
	}
	return "未知"
}

package operation

import (
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/docker"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/artifact_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"io"
	"sort"
	"strings"
)

func AllPipelines(c *gin.Context) {
	namespace := c.Query("namespace")
	count, pipes, err := pipeline_service.Search("", namespace, "", 0, 10000)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pipes) != int(count) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "当前不支持分页查询，请把查询数调整为大于发布流程总数")
		return
	}
	if v, err := dto.ParsePipelineList(pipes); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
	} else {
		web.SuccessJson(c, v)
	}
}

func PipelineUpdate(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		panic("只有管理员才有权限操作")
	}
	count, pipes, err := pipeline_service.Search("", "", "", 0, 10000)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pipes) != int(count) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "当前不支持分页查询，请把查询数调整为大于发布流程总数")
		return
	}

	op := c.Query("operate")
	var output interface{}
	if op == "apm-open" {
		output, err = PipelineUpdateApm(pipes, c.Query("cluster"), c.Query("namespace"), true)
	} else if op == "apm-close" {
		output, err = PipelineUpdateApm(pipes, c.Query("cluster"), c.Query("namespace"), false)
	} else if op == "debug-port-change" {
		//output, err = debugPortChange(pipes)
	} else if op == "jvm-appender" {
		output, err = jvmAppender(pipes, c.Query("cluster"), c.Query("namespace"), c.Query("val"))
	} else if op == "jvm-remove" {
		output, err = jvmRemove(pipes, c.Query("cluster"), c.Query("namespace"), c.Query("val"))
	} else if op == "update-liveness-initialTime" {
		output, err = updateLivenessInitialTime(pipes)
	} else if op == "reset-health-check-config" {
		output, err = resetHealthCheckConfig(pipes, c.Query("cluster"), c.Query("namespace"))
	} else if op == "app-module-url-update" {
		output, err = appModuleUrlUpdate(pipes, c.Query("cluster"), c.Query("namespace"))
	} else if op == "artifact-url-update" {
		output, err = artifactUrlUpdate(pipes)
	} else if op == "disable-startup-probe" {
		output, err = disableStartupProbe(pipes, c.Query("cluster"))
	} else if op == "enable-startup-probe" {
		output, err = enableStartupProbe(pipes, c.Query("cluster"))
	} else if op == "openJdk8-to-dragonwell8" {
		output, err = baseImageModify(c, "fs-tomcat8:openjdk8", "fs-tomcat8:ali-dragonwell8")
	} else if op == "dragonwell8-to-openJdk8" {
		output, err = baseImageModify(c, "fs-tomcat8:ali-dragonwell8", "fs-tomcat8:openjdk8")
	} else if op == "clickhouse-log-open" {
		output, err = updateClickHouseLog(pipes, c.Query("namespace"), true)
	} else if op == "clickhouse-log-close" {
		output, err = updateClickHouseLog(pipes, c.Query("namespace"), false)
	} else if op == "sort-app-modules" {
		output, err = sortAppModules(pipes, c.Query("namespace"))
	} else if op == "env-clear" {
		output, err = envClear(pipes, c.DefaultQuery("namespace", "_all_"))
	} else if op == "add-clickhouse-log-by-coreApp" {
		output, err = addClickHouseLogByCoreApp(pipes)
	} else if op == "remove-process-name-suffix" {
		if false { //暂且关闭这个操作
			output, err = RemoveProcessNameSuffix(pipes)
		}
	} else if op == "add-process-name-suffix" {
		output, err = AddProcessNameSuffix(pipes)
	} else if op == "disable-memory-over-config" {
		output, err = DisableMemoryOverConfig(pipes, c.Query("cluster"), c.Query("namespace"), c.Query("app"))
	} else if op == "update-resource" {
		//output, err = PipelineUpdateResource(c, pipes)
	} else if op == "jemalloc-image-update-open" {
		output, err = JemallocImageUpdate(pipes, c.Query("namespace"))
	} else if op == "remove-env-cluster-and-namespace" {
		output, err = RemoveEnvClusterAndNamespace(pipes, c.Query("cluster"), c.Query("namespace"))
	} else if op == "oom-report-url-update" {
		output, err = OOMReportUrlUpdate(pipes, c.Query("namespace"))
	} else {
		err = fmt.Errorf("unknow operate type: " + op)
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, output)

}

func OOMReportUrlUpdate(pipes []models.Pipeline, namespace string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}
		cluster := settings.GetSetting().GetCluster(p.Cluster)
		if cluster == nil || cluster.ThirdServices.OOMReportUrl == "" {
			log.Infof("can't found cluster oom report url, skipped")
			continue
		}

		if found, index := p.Envs.IndexOf("OOM_REPORT_URL"); !found {
			p.Envs = append(p.Envs, datatype.Env{
				Name:  "OOM_REPORT_URL",
				Value: cluster.ThirdServices.OOMReportUrl,
				Type:  constant.ENV_TYPE_SYSTEM,
			})
		} else {
			if p.Envs[index].Value == cluster.ThirdServices.OOMReportUrl {
				continue
			}
			p.Envs[index].Value = cluster.ThirdServices.OOMReportUrl
		}
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func JemallocImageUpdate(pipes []models.Pipeline, namespace string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}

		if found, _ := p.Envs.IndexOf("JEMALLOC_ENABLE"); found {
			continue
		} else {
			p.Envs = append(p.Envs, datatype.Env{
				Name:  "JEMALLOC_ENABLE",
				Value: "true",
				Type:  constant.ENV_TYPE_USER,
			})
		}
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func RemoveEnvClusterAndNamespace(pipes []models.Pipeline, cluster, namespace string) (interface{}, error) {
	//Pod下的K8S_CLUSTER和K8S_NAMESPACE已经固定在yaml文件中，不需要在发布流程中设置
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}

		if found, _ := p.Envs.IndexOf("K8S_CLUSTER"); !found {
			if found, _ := p.Envs.IndexOf("K8S_NAMESPACE"); !found {
				continue
			}
		}

		newEnvs := make([]datatype.Env, 0, len(p.Envs))
		for _, it := range p.Envs {
			if it.Name != "K8S_CLUSTER" && it.Name != "K8S_NAMESPACE" {
				newEnvs = append(newEnvs, it)
			}
		}
		p.Envs = newEnvs
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func PipelineUpdateApm(pipes []models.Pipeline, cluster, namespace string, open bool) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}
		if p.Options.SkyWalkingAgent == open {
			continue
		}
		p.Options.SkyWalkingAgent = open
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

//func debugPortChange(pipes []models.Pipeline) (interface{}, error) {
//	output := make([]string, 0, len(pipes))
//	fmt.Println("-----------------------------------------------------")
//	for _, p := range pipes {
//		//if p.App != "fs-appserver-schedule-provider" {
//		//	continue
//		//}
//		needUpdate := false
//		for idx, it := range p.Envs {
//			if it.Name == "JAVA_OPTS" {
//				if strings.Contains(it.Value, "address=8081") {
//					p.Envs[idx].Value = strings.TrimSpace(strings.ReplaceAll(it.Value, "address=8081", "address=18081"))
//					needUpdate = true
//				}
//				break
//			}
//		}
//		for idx, it := range p.Ports {
//			if it.Value == 8081 {
//				p.Ports[idx].Value = 18081
//				break
//			}
//		}
//		if needUpdate {
//			fmt.Println(fmt.Sprintf("echo  '===== %s/%s ====='", p.Namespace, p.App))
//			fmt.Println(fmt.Sprintf("kubectl get rs  -n %s | grep %s", p.Namespace, p.App))
//			fmt.Println(fmt.Sprintf("kubectl get service  -n %s %s", p.Namespace, p.App))
//			fmt.Println(fmt.Sprintf("kubectl get deployment  -n %s %s -o yaml > /tmp/dtemp.yml && sed -i '' 's/address=8081/address=18081/g' /tmp/dtemp.yml && kubectl apply -f /tmp/dtemp.yml", p.Namespace, p.App))
//			fmt.Println(fmt.Sprintf("kubectl get service -n %s %s  -o yaml  > /tmp/stemp.yml && sed -i '' 's/port: 8081/port: 18081/g' /tmp/stemp.yml && kubectl apply -f /tmp/stemp.yml", p.Namespace, p.App))
//			fmt.Println(fmt.Sprintf("kubectl get rs  -n %s | grep %s", p.Namespace, p.App))
//			fmt.Println(fmt.Sprintf("kubectl get service  -n %s %s", p.Namespace, p.App))
//			fmt.Println("sleep 10")
//			//output = append(output, fmt.Sprintf("%s/%s/%s: %s", p.Cluster, p.Namespace, p.App, newVal))
//			//fmt.Println(fmt.Sprintf("%s/%s/%s: %s", p.Cluster, p.Namespace, p.App, newVal))
//			if err := pipeline_service.SaveOrUpdate(&p); err == nil {
//				output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
//			} else {
//				output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
//			}
//		}
//	}
//	fmt.Println("----------------------------------------")
//	return output, nil
//}

func jvmRemove(pipes []models.Pipeline, cluster, namespace string, val string) (interface{}, error) {
	val = strings.TrimSpace(val)
	if val == "" {
		return nil, fmt.Errorf("val must not be empty")
	}
	if strings.Contains(val, " ") {
		return nil, fmt.Errorf("val must not contain blank characters")
	}
	if !strings.HasPrefix(val, "-") {
		return nil, fmt.Errorf("val must be start with '-'")
	}
	output := make([]string, 0, len(pipes))
	//必须加上前后空格，避免误匹配
	val = " " + val + " "
	for _, p := range pipes {
		if p.Cluster != cluster || p.Namespace != namespace {
			continue
		}
		needUpdate := false
		for idx, it := range p.Envs {
			if it.Name == "JAVA_OPTS" {
				oldVal := " " + it.Value + " "
				if strings.Contains(oldVal, val) {
					p.Envs[idx].Value = strings.TrimSpace(strings.ReplaceAll(oldVal, val, " "))
					needUpdate = true
				}
				break
			}
		}
		if needUpdate {
			if err := pipeline_service.SaveOrUpdate(&p); err == nil {
				output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
			} else {
				output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
			}
		}
	}
	return output, nil
}

func jvmAppender(pipes []models.Pipeline, cluster, namespace string, val string) (interface{}, error) {
	val = strings.TrimSpace(val)
	if val == "" {
		return nil, fmt.Errorf("val must not be empty")
	}
	if strings.Contains(val, " ") {
		return nil, fmt.Errorf("val must not contain blank characters")
	}
	if !strings.HasPrefix(val, "-") {
		return nil, fmt.Errorf("val must be start with '-'")
	}
	//必须加上前后空格，避免误匹配
	val = " " + val + " "
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if p.Cluster != cluster || p.Namespace != namespace {
			continue
		}

		//只有 dragonwell8 jdk 才支持 UseWisp 参数
		if strings.Contains(val, "UseWisp") && !strings.Contains(p.BaseImage, "dragonwell8") {
			continue
		}
		needUpdate := false
		for idx, it := range p.Envs {
			if it.Name == "JAVA_OPTS" {
				oldVal := " " + it.Value + " "
				if !strings.Contains(oldVal, val) {
					p.Envs[idx].Value = oldVal + val
					needUpdate = true
				}
				break
			}
		}
		if needUpdate {
			if err := pipeline_service.SaveOrUpdate(&p); err == nil {
				output = append(output, fmt.Sprintf("[Success]: %s/%s/%s/%s", p.Cluster, p.Namespace, p.App, docker.GetImageSimpleName(p.BaseImage)))
			} else {
				output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
			}
		}
	}
	return output, nil
}

func updateLivenessInitialTime(pipes []models.Pipeline) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if p.LivenessProbe.Enable && p.LivenessProbe.InitialDelaySeconds < 1200 {
			p.LivenessProbe.InitialDelaySeconds = 1200
		}
		if p.ReadinessProbe.Enable && p.ReadinessProbe.InitialDelaySeconds > 120 {
			p.ReadinessProbe.InitialDelaySeconds = 60
		}
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func resetHealthCheckConfig(pipes []models.Pipeline, cluster string, namespace string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	def := settings.GetSetting().PipelineDefault
	for _, p := range pipes {
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}

		//如果用户通过自定义延长了重启时间， 则不做更改
		if p.LivenessProbe.FailureThreshold*p.LivenessProbe.PeriodSeconds > 180 {
			continue
		}

		update := false

		defStartupProbe := def.StartupProbe
		if p.StartupProbe.Enable && !p.StartupProbe.Equals(defStartupProbe) {
			p.StartupProbe = defStartupProbe
			p.StartupProbe.Enable = true
			update = true
		}

		//如果没有启动探针没有配置，则延长就绪探针配置的初始延迟时间
		defReadinessProbe := def.ReadinessProbe
		if !p.StartupProbe.Enable && defReadinessProbe.InitialDelaySeconds < 30 {
			defReadinessProbe.InitialDelaySeconds = 30
		}
		if p.ReadinessProbe.Enable && !p.ReadinessProbe.Equals(defReadinessProbe) {
			p.ReadinessProbe = defReadinessProbe
			p.ReadinessProbe.Enable = true
			update = true
		}

		defLivenessProbe := def.LivenessProbe
		if p.LivenessProbe.Enable && !p.LivenessProbe.Equals(defLivenessProbe) {
			p.LivenessProbe = defLivenessProbe
			p.LivenessProbe.Enable = true
			update = true
		}
		if update {
			if err := pipeline_service.SaveOrUpdate(&p); err == nil {
				output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
			} else {
				output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
			}
		}
	}
	return output, nil
}

func appModuleUrlUpdate(pipes []models.Pipeline, cluster string, namespace string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}

		update := false

		for idx, it := range p.AppModules {
			if strings.HasPrefix(it.GitUrl, "http://") {
				p.AppModules[idx].GitUrl = strings.Replace(it.GitUrl, "http://", "https://", 1)
				update = true
			}
		}
		if update {
			if err := pipeline_service.SaveOrUpdate(&p); err == nil {
				output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
			} else {
				output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
			}
		}
	}
	return output, nil
}

func artifactUrlUpdate(pipes []models.Pipeline) (interface{}, error) {
	output := make([]string, 0, len(pipes))

	artifacts, err := artifact_service.FindAll()
	if err != nil {
		return nil, err
	}

	for _, arti := range artifacts {
		update := false
		if strings.HasPrefix(arti.GitUrl, "http://") {
			arti.GitUrl = strings.Replace(arti.GitUrl, "http://", "https://", 1)
			update = true
		}
		if update {
			if err := artifact_service.Update(&arti); err == nil {
				output = append(output, fmt.Sprintf("[Success]: %s/%s", arti.GitUrl, arti.Module))
			} else {
				output = append(output, fmt.Sprintf("[Fail]: %s/%s, err: %s", arti.GitUrl, arti.Module, err.Error()))
			}
		}
	}
	return output, nil
}

func disableStartupProbe(pipes []models.Pipeline, cluster string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}
		if !p.StartupProbe.Enable {
			continue
		}
		p.StartupProbe.Enable = false
		if p.ReadinessProbe.InitialDelaySeconds < 30 {
			p.ReadinessProbe.InitialDelaySeconds = 30
		}
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func enableStartupProbe(pipes []models.Pipeline, cluster string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	def := settings.GetSetting().PipelineDefault
	for _, p := range pipes {
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}
		if p.StartupProbe.Enable {
			continue
		}
		//如果启动探针没有配置，则使用默认配置
		if p.StartupProbe.InitialDelaySeconds == 0 || p.StartupProbe.PeriodSeconds == 0 {
			p.StartupProbe = def.StartupProbe
		}
		p.StartupProbe.Enable = true
		if p.ReadinessProbe.InitialDelaySeconds > 10 {
			p.ReadinessProbe.InitialDelaySeconds = 10
		}
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func baseImageModify(c *gin.Context, srcImage, destImage string) (interface{}, error) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, err
	}
	lines := strings.TrimSpace(string(body))
	if lines == "" {
		return nil, fmt.Errorf("body must not be empty")
	}
	output := make([]map[string]interface{}, 0, 500)
	for _, line := range strings.Split(lines, "\n") {
		item := map[string]interface{}{}
		output = append(output, item)
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 3 {
			item["status"] = "line format error, val: " + line
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]
		item["cluster"] = cluster
		item["namespace"] = namespace
		item["app"] = app
		p, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			item["status"] = "fail,pipeline not found, err: " + err.Error()
			continue
		}
		newImage := strings.ReplaceAll(p.BaseImage, srcImage, destImage)
		item["oldImage"] = p.BaseImage
		item["newImage"] = newImage
		if strings.EqualFold(p.BaseImage, newImage) {
			item["status"] = "skipped, no need to update"
			continue
		}
		p.BaseImage = newImage
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			item["status"] = "success"
		} else {
			item["status"] = "fail, " + err.Error()
		}
	}
	log_service.CreateBySys("发布流程-批量更新", "jdkSwitch", output)
	return output, nil
}

func updateClickHouseLog(pipes []models.Pipeline, namespace string, open bool) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if p.Namespace != namespace {
			continue
		}
		p.Options.AppLogToKafka = open
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func sortAppModules(pipes []models.Pipeline, namespace string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}
		if len(p.AppModules) < 2 {
			continue
		}
		//部署模块排序，便于批量发布时的比较
		sort.Slice(p.AppModules, func(i, j int) bool {
			item1 := p.AppModules[i]
			item2 := p.AppModules[j]
			return item1.ToString() < item2.ToString()
		})
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func envClear(pipes []models.Pipeline, namespace string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}

		envMap := make(map[string]datatype.Env)
		for _, it := range p.Envs {
			envMap[it.Name] = it
		}
		oldLen := len(envMap)
		delete(envMap, constant.ENV_KEY_SKY_WALKING_ENABLE)
		delete(envMap, constant.ENV_KEY_APP_LOG_TO_KAFKA)
		delete(envMap, constant.ENV_KEY_JVM_GC_LOG_ENABLE)
		delete(envMap, constant.ENV_KEY_JACOCO_AGENT_ENABLE)
		newLen := len(envMap)
		if oldLen == newLen {
			continue
		}

		ret := make([]datatype.Env, 0, len(envMap))
		for _, it := range envMap {
			ret = append(ret, it)
		}
		sort.Slice(ret, func(i, j int) bool {
			e1 := ret[i]
			e2 := ret[j]
			//排序规则：系统变量排前面
			if e1.Type == constant.ENV_TYPE_SYSTEM && e2.Type != constant.ENV_TYPE_SYSTEM {
				return true
			} else {
				return strings.Compare(e1.Name, e2.Name) < 1
			}
		})
		p.Envs = ret
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func addClickHouseLogByCoreApp(pipes []models.Pipeline) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	coreApps := make([]string, 0, len(pipes))
	for _, p := range pipes {
		//部署了svip环境，或单元化环境则认为是核心服务
		if p.Namespace == "foneshare-yqsl" || p.Namespace == "foneshare01" {
			if !strslice.Find(coreApps, p.App) {
				coreApps = append(coreApps, p.App)
			}
		}
	}
	for _, p := range pipes {
		if !strslice.Find(coreApps, p.App) {
			continue
		}
		if p.Options.AppLogToKafka {
			continue
		}
		if !strings.Contains(p.Namespace, "foneshare") {
			continue
		}
		p.Options.AppLogToKafka = true
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}
func RemoveProcessNameSuffix(pipes []models.Pipeline) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if p.Namespace == "foneshare" {
			continue
		}
		envMap := make(map[string]datatype.Env)
		for _, it := range p.Envs {
			envMap[it.Name] = it
		}

		envMap["CATALINA_OPTS"] = datatype.Env{
			Name: "CATALINA_OPTS",
			Value: fmt.Sprintf("-Dprocess.profile=%s -Dspring.profiles.active=%s -Dprocess.name=%s -Dapp.name=%s",
				p.Namespace, p.Namespace, p.App, p.App),
			Type: constant.ENV_TYPE_SYSTEM,
		}
		envMap["K8S_PROCESS_NAME"] = datatype.Env{
			Name:  "K8S_PROCESS_NAME",
			Value: p.App,
			Type:  constant.ENV_TYPE_SYSTEM,
		}

		ret := make([]datatype.Env, 0, len(envMap))
		for _, it := range envMap {
			ret = append(ret, it)
		}
		sort.Slice(ret, func(i, j int) bool {
			e1 := ret[i]
			e2 := ret[j]
			//排序规则：系统变量排前面
			if e1.Type == constant.ENV_TYPE_SYSTEM && e2.Type != constant.ENV_TYPE_SYSTEM {
				return true
			} else {
				return strings.Compare(e1.Name, e2.Name) < 1
			}
		})
		p.Envs = ret
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func AddProcessNameSuffix(pipes []models.Pipeline) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		envMap := make(map[string]datatype.Env)
		for _, it := range p.Envs {
			envMap[it.Name] = it
		}
		oldValue := envMap["CATALINA_OPTS"].Value
		processName := settings.GetSetting().GetProcessName(p.Namespace, p.App)
		newValue := fmt.Sprintf("-Dprocess.profile=%s -Dspring.profiles.active=%s -Dprocess.name=%s -Dapp.name=%s",
			p.Namespace, p.Namespace, processName, p.App)
		if oldValue == newValue {
			continue
		}
		envMap["CATALINA_OPTS"] = datatype.Env{
			Name:  "CATALINA_OPTS",
			Value: newValue,
			Type:  constant.ENV_TYPE_SYSTEM,
		}
		envMap["K8S_PROCESS_NAME"] = datatype.Env{
			Name:  "K8S_PROCESS_NAME",
			Value: processName,
			Type:  constant.ENV_TYPE_SYSTEM,
		}

		ret := make([]datatype.Env, 0, len(envMap))
		for _, it := range envMap {
			ret = append(ret, it)
		}
		sort.Slice(ret, func(i, j int) bool {
			e1 := ret[i]
			e2 := ret[j]
			//排序规则：系统变量排前面
			if e1.Type == constant.ENV_TYPE_SYSTEM && e2.Type != constant.ENV_TYPE_SYSTEM {
				return true
			} else {
				return strings.Compare(e1.Name, e2.Name) < 1
			}
		})
		p.Envs = ret
		if err := pipeline_service.SaveOrUpdate(&p); err == nil {
			output = append(output, fmt.Sprintf("[Success]: %s/%s/%s", p.Cluster, p.Namespace, p.App))
		} else {
			output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
		}
	}
	return output, nil
}

func DisableMemoryOverConfig(pipes []models.Pipeline, cluster, namespace, app string) (interface{}, error) {
	output := make([]string, 0, len(pipes))
	for _, p := range pipes {
		if cluster != "_all_" && p.Cluster != cluster {
			continue
		}
		if namespace != "_all_" && p.Namespace != namespace {
			continue
		}
		if app != "_all_" && p.App != app {
			continue
		}
		if p.Resources.RequestMemory != p.Resources.LimitMemory {
			desc := fmt.Sprintf("ReqMemChage: %d -> %d", p.Resources.RequestMemory, p.Resources.LimitMemory)
			p.Resources.RequestMemory = p.Resources.LimitMemory
			if err := pipeline_service.SaveOrUpdate(&p); err == nil {
				output = append(output, fmt.Sprintf("[Success]: %s/%s/%s (%s)", p.Cluster, p.Namespace, p.App, desc))
			} else {
				output = append(output, fmt.Sprintf("[Fail]: %s/%s/%s, err: %s", p.Cluster, p.Namespace, p.App, err.Error()))
			}
		}
	}
	log_service.CreateBySys("发布流程-批量更新", "DisableMemoryOverConfig"+fmt.Sprintf("%s/%s/%s", cluster, namespace, app), output)
	return output, nil
}

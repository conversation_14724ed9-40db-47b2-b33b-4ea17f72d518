package podautoscaler

import (
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/kubectl/pascli"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

type PodAutoScalerParam struct {
	Cluster                           string `form:"cluster" binding:"required"`
	Namespace                         string `form:"namespace" binding:"required"`
	App                               string `form:"app" binding:"required"`
	Paused                            bool   `form:"paused"`
	MinReplicas                       int32  `form:"minReplicas" binding:"required"`
	MaxReplicas                       int32  `form:"maxReplicas" binding:"required"`
	ScaleUpStabilizationWindowSeconds int32  `form:"scaleUpStabilizationWindowSeconds" `
	CpuTargetPercent                  int32  `form:"cpuTargetPercent" binding:"required"`

	ScaleUpHourWindow                   []int32 `form:"scaleUpHourWindow"`
	ScaleDownHourWindow                 []int32 `form:"scaleDownHourWindow"`
	ScaleUpReplicaStep                  int32   `form:"scaleUpReplicaStep"`
	ScaleDownReplicaStep                int32   `form:"scaleDownReplicaStep"`
	ScaleUpInitialDelaySeconds          int32   `form:"scaleUpInitialDelaySeconds"`
	ScaleDownStabilizationWindowSeconds int32   `form:"scaleDownStabilizationWindowSeconds"`
}

type SearchParam struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace"`
	App       string `form:"app"`
}

type DeleteParam struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace" binding:"required"`
	App       string `form:"app" binding:"required"`
}

func Create(c *gin.Context) {
	var p PodAutoScalerParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	dto, err := buildDTO(p)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	iYaml, err := pascli.ApplyPodAutoScaler(dto)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	subject := fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.App)
	log_service.Create(auth.GetRealName(c), "自动扩缩容V2-编辑", subject, p)
	log_service.Create(auth.GetRealName(c), "自动扩缩容V2-Apply", subject, iYaml)
	web.SuccessJson(c, nil)
}

func CreateForCoreApp(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "无权限操作")
		return
	}

	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	serviceLevel := c.Query("serviceLevel")
	if cluster == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster is empty")
		return
	}
	if namespace == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "namespace is empty")
		return
	}
	if serviceLevel == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "serviceLevel is empty")
		return
	}
	//todo: 代码实现
	web.SuccessJson(c, nil)
}

func buildDTO(p PodAutoScalerParam) (dto.PodAutoScalerDTO, error) {
	if len(p.ScaleUpHourWindow) == 0 {
		p.ScaleUpHourWindow = []int32{7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22}
	}
	if len(p.ScaleDownHourWindow) == 0 {
		p.ScaleDownHourWindow = []int32{23, 0, 1, 2, 3, 4, 5, 6}
	}
	if p.ScaleUpReplicaStep == 0 {
		p.ScaleUpReplicaStep = 3
	}
	if p.ScaleDownReplicaStep == 0 {
		p.ScaleDownReplicaStep = 1
	}
	if p.ScaleUpInitialDelaySeconds == 0 {
		p.ScaleUpInitialDelaySeconds = 600
	}
	if p.ScaleDownStabilizationWindowSeconds == 0 {
		p.ScaleDownStabilizationWindowSeconds = 600
	}

	if p.CpuTargetPercent <= 0 || p.CpuTargetPercent > 100 {
		return dto.PodAutoScalerDTO{}, fmt.Errorf("cpuTargetPercent must be between 1 and 100")
	}
	pipe, err := pipeline_service.FirstInEnv(p.Cluster, p.Namespace, p.App)
	if err != nil {
		return dto.PodAutoScalerDTO{}, err
	}
	minReplicas := p.MinReplicas
	if minReplicas < int32(pipe.Replicas) {
		minReplicas = int32(pipe.Replicas)
	}

	if p.MaxReplicas <= minReplicas {
		return dto.PodAutoScalerDTO{}, fmt.Errorf("maxReplicas must be greater than minReplicas")
	}
	if p.ScaleUpStabilizationWindowSeconds < 30 {
		return dto.PodAutoScalerDTO{}, fmt.Errorf("scaleUpStabilizationWindowSeconds must be greater than or equal to 30")
	}
	ret := dto.PodAutoScalerDTO{
		Cluster:                             p.Cluster,
		Namespace:                           p.Namespace,
		App:                                 p.App,
		Paused:                              p.Paused,
		MinReplicas:                         minReplicas,
		MaxReplicas:                         p.MaxReplicas,
		ScaleUpInitialDelaySeconds:          p.ScaleUpInitialDelaySeconds,
		ScaleUpStabilizationWindowSeconds:   p.ScaleUpStabilizationWindowSeconds,
		ScaleUpReplicaStep:                  p.ScaleUpReplicaStep,
		ScaleUpHourWindow:                   p.ScaleUpHourWindow,
		ScaleDownStabilizationWindowSeconds: p.ScaleDownStabilizationWindowSeconds,
		ScaleDownReplicaStep:                p.ScaleDownReplicaStep,
		ScaleDownHourWindow:                 p.ScaleDownHourWindow,
		TriggerCpuUtilization:               fmt.Sprintf("%d", p.CpuTargetPercent),
		Labels: map[string]string{
			"app.kubernetes.io/managed-by": "fs-k8s-app-manager"},
		Annotations: map[string]string{
			"fxiaoke.com/last-modify-user": "wuzh",
			"fxiaoke.com/last-modify-time": time.Now().Format("2006-01-02 15:04:05"),
		},
	}
	return ret, nil
}

func Delete(c *gin.Context) {
	var p DeleteParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	_, err := pascli.DeletePodAutoScaler(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	subject := fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.App)
	log_service.Create(auth.GetRealName(c), "自动扩缩容V2-删除", subject, p)
	web.SuccessJson(c, nil)
}

func Search(c *gin.Context) {
	var p SearchParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	pasList, err := pascli.ListPodAutoScalerFromK8s(p.Cluster, p.Namespace)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := pasList.Items
	if p.App != "" {
		data = make([]dto.PodAutoScaler, 0, 20)
		for _, pas := range pasList.Items {
			if strings.Contains(pas.Name, p.App) {
				data = append(data, pas)
			}
		}
	}
	for i := 0; i < len(data); i++ {
		data[i].Spec.ScaleUp.HourWindowDesc = getHourWindowDesc(data[i].Spec.ScaleUp.HourWindow)
		data[i].Spec.ScaleDown.HourWindowDesc = getHourWindowDesc(data[i].Spec.ScaleDown.HourWindow)
	}
	web.SuccessJson(c, data)
}

func getHourWindowDesc(hourWindow []int32) string {
	if len(hourWindow) == 0 {
		return "[-关闭-]"
	}
	if len(hourWindow) == 24 {
		return "[全天]"
	}
	strArr := make([]string, len(hourWindow))
	for i, num := range hourWindow {
		strArr[i] = fmt.Sprintf("%d", num)
	}
	if strings.Join(strArr, "#") == "7#8#9#10#11#12#13#14#15#16#17#18#19#20#21#22" {
		return "[07:00 - 22:00]"
	}
	if strings.Join(strArr, "#") == "23#0#1#2#3#4#5#6" {
		return "[23:00 - 06:00]"
	}
	if strings.Join(strArr, "#") == "23#0#1#2#3#4#5#6#7" {
		return "[23:00 - 07:00]"
	}
	if strings.Join(strArr, "#") == "22#23#0#1#2#3#4#5#6" {
		return "[22:00 - 06:00]"
	}
	if strings.Join(strArr, "#") == "22#23#0#1#2#3#4#5#6#7" {
		return "[22:00 - 07:00]"
	}

	return strings.Join(strArr, ",")
}

//func Migrate(c *gin.Context) {
//	user, _ := auth.GetUser(c)
//	if !perm_service.IsAdmin(user) {
//		web.FailJson(c, web.CODE_CLIENT_ERROR, "无权限操作")
//		return
//	}
//	cluster := c.Query("cluster")
//	if cluster == "" {
//		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster is empty")
//		return
//	}
//	clu := settings.GetSetting().GetCluster(cluster)
//	if clu == nil {
//		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster not found")
//		return
//	}
//	items, err := auto_scale_service.Search(cluster, "", "")
//	if err != nil {
//		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
//		return
//	}
//
//	data := make([]string, 0, len(items))
//	for _, item := range items {
//		if output, err := pascli.GetPodAutoScaler(item.Cluster, item.Namespace, item.App); err == nil || strings.TrimSpace(output) != "" {
//			data = append(data, fmt.Sprintf("%s/%s/%s: already has pod auto scaler", item.Cluster, item.Namespace, item.App))
//			continue
//		}
//
//		pipeline, err := pipeline_service.FirstInEnv(item.Cluster, item.Namespace, item.App)
//		if err != nil {
//			data = append(data, fmt.Sprintf("%s/%s/%s: not found pipeline", item.Cluster, item.Namespace, item.App))
//			continue
//		}
//		maxReplicas := int32(item.Replicas)
//		minReplicas := int32(pipeline.Replicas)
//		if minReplicas >= maxReplicas {
//			maxReplicas = minReplicas + 3
//		}
//		param := dto.PodAutoScalerDTO{
//			Cluster:                             item.Cluster,
//			Namespace:                           item.Namespace,
//			App:                                 item.App,
//			Paused:                              false,
//			MinReplicas:                         minReplicas,
//			MaxReplicas:                         maxReplicas,
//			ScaleUpInitialDelaySeconds:          600,
//			ScaleUpStabilizationWindowSeconds:   60,
//			ScaleUpReplicaStep:                  3,
//			ScaleUpHourWindow:                   []int32{7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22},
//			ScaleDownStabilizationWindowSeconds: 600,
//			ScaleDownReplicaStep:                1,
//			ScaleDownHourWindow:                 []int32{23, 0, 1, 2, 3, 4, 5, 6},
//			TriggerCpuUtilization:               fmt.Sprintf("%d", item.CpuTargetPercent),
//			Labels: map[string]string{
//				"app.kubernetes.io/managed-by": "fs-k8s-app-manager"},
//			Annotations: map[string]string{
//				"fxiaoke.com/last-modify-user": user.RealName,
//				"fxiaoke.com/last-modify-time": time.Now().Format("2006-01-02 15:04:05"),
//			},
//		}
//		iYaml, err := pascli.ApplyPodAutoScaler(param)
//		if err != nil {
//			data = append(data, fmt.Sprintf("%s/%s/%s: apply pod auto scaler error:%s", item.Cluster, item.Namespace, item.App, err.Error()))
//			continue
//		}
//		log_service.Create(auth.GetRealName(c), "自动扩缩容V2-Apply", fmt.Sprintf("%s/%s/%s", param.Cluster, param.Namespace, param.App), iYaml)
//	}
//	web.SuccessJson(c, data)
//}

func AllClustersAutoScalerV2(c *gin.Context) {
	cacheKey := key.Pre().K8S.Key("AllClusterAutoScalerV2")
	if data, found := cache.GetStruct(cacheKey, map[string]bool{}); found {
		web.SuccessJson(c, data)
		return
	}

	data := make(map[string]bool)
	for _, cluster := range settings.GetSetting().Clusters {
		data[cluster.Name] = pascli.IsInstalledPodAutoScaler(cluster.Name)
	}
	_ = cache.SetStruct(cacheKey, data, 24*time.Minute)
	web.SuccessJson(c, data)
}

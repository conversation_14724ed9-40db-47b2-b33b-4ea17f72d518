package app

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/kubectl/pascli"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/template"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

const appNamesCacheKey = "AppNames"

type TreeNode struct {
	Value    string     `json:"value"`
	Label    string     `json:"label"`
	Children []TreeNode `json:"children"`
}

type SearchParams struct {
	param.PageSearch
	Level string `form:"level"`
}

func List(c *gin.Context) {
	var p SearchParams
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}

	data := make(map[string]interface{})
	if entities, err := app_service.Search(p.Keyword, p.Level, p.Page, p.Limit); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		data["data"] = dto.ToAppList(entities)
		data["count"] = app_service.Count(p.Keyword, p.Level)
	}
	web.SuccessJson(c, data)
}
func AllNames(c *gin.Context) {
	if data, found := cache.GetStruct(key.Pre().APP.Key(appNamesCacheKey), []string{}); found {
		web.SuccessJson(c, data)
		return
	}
	data := make([]string, 0, 500)
	if entities, err := app_service.FindAll(); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		for _, it := range entities {
			data = append(data, it.Name)
		}
	}
	cache.SetStruct(key.Pre().APP.Key(appNamesCacheKey), data, 5*time.Minute)
	web.SuccessJson(c, data)
}

func FindByName(c *gin.Context) {
	app := c.Query("name")
	entity, err := app_service.FindByName(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, dto.ToApp(entity))
}
func DeleteByName(c *gin.Context) {
	app := c.Query("name")

	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, app) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "你没有应用权限")
		return
	}

	pipes, err := pipeline_service.FindByApp(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pipes) > 0 {
		web.FailJson(c, web.CODE_SERVER_ERROR, "应用下存在发布流程，不允许被删除")
		return
	}
	log_service.Create(auth.GetRealName(c), "应用-删除", app, "")
	if err := app_service.DeleteByName(app); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func Create(c *gin.Context) {
	var p dto.App
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	log_service.Create(auth.GetRealName(c), "应用-创建", p.Name, p)
	entity := p.ToModel()
	if err := app_service.Create(&entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	cache.Delete(key.Pre().APP.Key(appNamesCacheKey))
	web.SuccessJson(c, nil)
}

func Edit(c *gin.Context) {
	var p dto.App
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	dbEntity, err := app_service.FindByName(p.Name)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	isAdmin := perm_service.IsAdmin(user)
	if !isAdmin {
		//只有管理员才能修改发布窗口
		if dbEntity.TimeWindow.Desc() != "" && dbEntity.TimeWindow.Desc() != p.TimeWindow.Desc() {
			if !perm_service.IsTimeWindowAdmin(user) {
				web.FailJson(c, web.CODE_SERVER_ERROR, "只有【发版时间窗口-管理员】允许修改发版时间窗口")
				return
			}
		}
		//当应用已存在管理员时，则进行操作鉴权
		if len(dbEntity.Admins) != 0 && !perm_service.IsAppAdmin(user, dbEntity.Name) {
			web.FailJson(c, web.CODE_SERVER_ERROR, "只有【应用管理员】允许执行当前操作")
			return
		}
	}

	log_service.Create(auth.GetRealName(c), "应用-编辑", p.Name, p)
	dbEntity.Orgs = p.Orgs
	dbEntity.Level = p.Level
	dbEntity.Remark = p.Remark
	dbEntity.Admins = p.Admins
	dbEntity.TimeWindow = p.TimeWindow
	err = app_service.Update(&dbEntity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	perm_service.ClearAppsCache()
	web.SuccessJson(c, nil)
}

func EditPermission(c *gin.Context) {
	var p PermissionParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAppAdmin(user, p.App) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有【应用管理员】允许执行当前操")
		return
	}
	dbEntity, err := app_service.FindByName(p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "应用-权限编辑", p.App, p)
	dbEntity.Orgs = p.Orgs
	err = app_service.Update(&dbEntity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	perm_service.ClearAppsCache()
	web.SuccessJson(c, nil)
}

func Address(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	for _, it := range []string{cluster, namespace, app} {
		if it == "" {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "缺失查询参数")
			return
		}
	}

	service, err := k8s_service.ServiceDTO(cluster, namespace, app)
	if service == nil {
		err = fmt.Errorf("not found service from %s/%s/%s", cluster, namespace, app)
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := make([]dto.AppAddress, 0, len(service.Ports))
	clu := settings.GetSetting().GetCluster(cluster)
	for _, it := range service.Ports {
		outerAddr := make([]string, 0, 2)
		innerAddr := make([]string, 0, 2)
		if clu == nil {
			continue
		}

		if clu.ShowNodeVipAddr && clu.NodeVIP != "" {
			outerAddr = append(outerAddr, fmt.Sprintf("%s:%d", clu.NodeVIP, it.NodePort))
		}
		if clu.ShowIngressAddr && it.Name == "http" && clu.IngressParentHost != "" {
			outerAddr = append(outerAddr, fmt.Sprintf("%s.%s.%s", app, namespace, clu.IngressParentHost))
		}
		remark := ""
		if !clu.IsFxiaokeCloud() {
			innerAddr = append(innerAddr, fmt.Sprintf("%s.%s:%d", app, namespace, it.Port))
			remark = "<div style='color:#F56C6C;'>非纷享云环境：配置中心不要使用【集群外访问地址】</div>"
		} else {
			//纷享云不显示集群内地址，推荐用lvs vip + nodeport (性能最好）
			//为了便于应用在集群间的迁移， 纷享云不显示内部地址
			innerAddr = append(innerAddr, "- 纷享云环境不显示内部地址 -")
			remark = "<div style='color:#F56C6C;'>纷享云环境：配置中心不要使用【集群内访问地址】</div>"
		}
		data = append(data, dto.AppAddress{
			Name:                it.Name,
			Protocol:            it.Protocol,
			Port:                it.Port,
			TargetPort:          it.TargetPort,
			NodePort:            it.NodePort,
			ClusterInnerAddress: innerAddr,
			ClusterOuterAddress: outerAddr,
			Remark:              remark,
		})
	}

	web.SuccessJson(c, data)
}

func AppsWithEnv(c *gin.Context) {
	cacheKey := key.Pre().APP.Key("apps-with-env")
	if data, found := cache.GetStruct(cacheKey, map[string][]map[string]string{}); found {
		web.SuccessJson(c, data)
		return
	}
	pipes, err := pipeline_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := make(map[string][]map[string]string)
	for _, p := range pipes {
		//if p.Status != constant.PIPELINE_STATUS_ENABLED {
		//	continue
		//}
		_, found := data[p.App]
		if !found {
			data[p.App] = make([]map[string]string, 0, 30)
		}
		envDesc := fmt.Sprintf("%s | %s", p.Namespace, p.Cluster)
		clusterDesc := ""
		clu := settings.GetSetting().GetCluster(p.Cluster)
		if clu != nil && !clu.IsFxiaokeCloud() {
			envDesc = fmt.Sprintf("%s | %s", clu.Description, p.Cluster)
			//如果不是专属云下的主环境，则带上数字后缀
			match := regexp.MustCompile(`-(\d+)$`).FindStringSubmatch(p.Namespace)
			if len(match) > 1 {
				envDesc = fmt.Sprintf("%s-%s | %s", clu.Description, match[1], p.Cluster)
			}
		}
		if clu != nil {
			clusterDesc = clu.Description
		}
		data[p.App] = append(data[p.App], map[string]string{
			"cluster":        p.Cluster,
			"namespace":      p.Namespace,
			"clusterDesc":    clusterDesc,
			"envDesc":        envDesc,
			"replicas":       strconv.Itoa(int(p.Replicas)),
			"pipelineStatus": p.StatusDesc(),
		})
	}
	for app, _ := range data {
		if len(data[app]) > 1 {
			sort.Slice(data[app], func(i, j int) bool {
				itemI := settings.GetSetting().GetEnvSortValue(data[app][i]["cluster"], data[app][i]["namespace"])
				itemJ := settings.GetSetting().GetEnvSortValue(data[app][j]["cluster"], data[app][j]["namespace"])
				return itemI < itemJ
			})
		}
	}

	_ = cache.SetStruct(cacheKey, data, 8*time.Hour)
	web.SuccessJson(c, data)
}

func GroupByNamespace(c *gin.Context) {
	cacheKey := key.Pre().APP.Key("group-by-namespace")
	if data, found := cache.GetStruct(cacheKey, []TreeNode{}); found {
		web.SuccessJson(c, data)
		return
	}
	pipes, err := pipeline_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	treeNodes := make(map[string]map[string][]TreeNode)
	for _, p := range pipes {
		_, found := treeNodes[p.Cluster]
		if !found {
			treeNodes[p.Cluster] = make(map[string][]TreeNode)
		}
		_, found = treeNodes[p.Cluster][p.Namespace]
		if !found {
			treeNodes[p.Cluster][p.Namespace] = make([]TreeNode, 0, 200)
		}
		treeNodes[p.Cluster][p.Namespace] = append(treeNodes[p.Cluster][p.Namespace], TreeNode{
			Value:    p.App,
			Label:    p.App,
			Children: nil,
		})
	}

	data := make([]TreeNode, 0, len(treeNodes))
	for cluster, namespaces := range treeNodes {
		clusterName := cluster
		clu := settings.GetSetting().GetCluster(cluster)
		if clu != nil && clu.Description != "" {
			clusterName = fmt.Sprintf("%s(%s)", clu.Description, clu.Name)
		}
		clusterNode := TreeNode{
			Value:    cluster,
			Label:    clusterName,
			Children: make([]TreeNode, 0, len(namespaces)),
		}
		for namespace, apps := range namespaces {
			namespaceNode := TreeNode{
				Value:    namespace,
				Label:    namespace,
				Children: apps,
			}
			clusterNode.Children = append(clusterNode.Children, namespaceNode)
		}
		data = append(data, clusterNode)
	}
	sort.Slice(data, func(i, j int) bool {
		weightI := 100
		weightJ := 100
		if idx := settings.GetSetting().ClusterIndexOf(data[i].Value); idx >= 0 {
			weightI = idx
		}
		if idx := settings.GetSetting().ClusterIndexOf(data[j].Value); idx >= 0 {
			weightJ = idx
		}
		return weightI < weightJ
	})
	_ = cache.SetStruct(cacheKey, data, 60*time.Minute)
	web.SuccessJson(c, data)
}

func All(c *gin.Context) {
	cacheKey := key.Pre().APP.Key("all-apps")
	if data, found := cache.GetStruct(cacheKey, []dto.App{}); found {
		web.SuccessJson(c, data)
		return
	}
	entities, err := app_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data := dto.ToAppList(entities)
	_ = cache.SetStruct(cacheKey, data, 8*time.Hour)
	web.SuccessJson(c, data)
}

func GitModules(c *gin.Context) {
	app := c.Query("app")
	pipelineId := c.Query("pipelineId")

	pipes := make([]models.Pipeline, 0, 1)
	if app != "" {
		appObj, err := app_service.FindByName(app)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		pipes, err = pipeline_service.FindByApp(appObj.Name)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
	} else if pipelineId != "" {
		pipeIdUint, err := strconv.ParseUint(pipelineId, 10, 32)
		if err != nil {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "pipelineId参数格式错误")
			return
		}
		pipe, err := pipeline_service.FindById(uint(pipeIdUint))
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		pipes = append(pipes, pipe)
	}
	data := make([]map[string]string, 0, len(pipes))
	for _, pipe := range pipes {
		mavenImage, err := getMavenImage(pipe.BaseImage)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, "获取Maven镜像失败，"+err.Error())
			return
		}
	ModuleLoop:
		for _, mod := range pipe.AppModules {
			for _, d := range data {
				if d["gitUrl"] == mod.GitUrl && d["module"] == mod.Module {
					continue ModuleLoop
				}
			}
			//mavenImage: 当多个发布流程使用了不同jdk时， 需要考虑使用哪个maven镜像更合适
			data = append(data, map[string]string{
				"gitUrl":     mod.GitUrl,
				"module":     mod.Module,
				"mavenImage": mavenImage,
			})
		}
	}
	sort.Slice(data, func(i, j int) bool {
		if data[i]["gitUrl"] < data[j]["gitUrl"] {
			return true
		} else if data[i]["gitUrl"] > data[j]["gitUrl"] {
			return false
		} else {
			return data[i]["module"] < data[j]["module"]
		}
	})
	web.SuccessJson(c, data)
}

func getMavenImage(image string) (string, error) {
	jdk, err := extractJDK(image)
	if err != nil {
		return "", err
	}
	for _, it := range settings.GetSetting().MavenImages {
		if strings.HasSuffix(it, jdk) {
			return it, nil
		}
	}
	return "", fmt.Errorf("未找到匹配的Maven版本")
}
func extractJDK(image string) (string, error) {
	//查找openjdk
	re := regexp.MustCompile(`openjdk(\d+)`)
	matches := re.FindStringSubmatch(image)
	if len(matches) >= 2 {
		return "openjdk" + matches[1], nil
	}
	//查找dragonwell jdk
	re = regexp.MustCompile(`dragonwell(\d+)`)
	matches = re.FindStringSubmatch(image)
	if len(matches) >= 2 {
		return "dragonwell" + matches[1], nil
	}
	return "", fmt.Errorf("未找到匹配的JDK版本")
}

// CreateAppHealthReviewInCRM 在CRM创建应用健康度评审对象
func CreateAppHealthReviewInCRM(c *gin.Context) {
	user, _ := auth.GetUser(c)
	app := c.DefaultQuery("app", "")
	pipes, err := pipeline_service.FindByApp(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	exist := checkAppReviewCRMObjectIsExist(app, user.EmployeeId)
	if exist {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "应用在【后端服务健康度 Review】对象中已存在，请点击链接查看：https://www.fxiaoke.com/XV/UI/Home#crm/list/=/service_health_review__c")
		return
	}

	fxiaokePipes := make([]models.Pipeline, 0, len(pipes))
	dedicatedCloudPipes := make([]models.Pipeline, 0, len(pipes))
	for _, pipe := range pipes {
		if pipe.Status != constant.PIPELINE_STATUS_ENABLED || pipe.Namespace == "jacoco" {
			continue
		}
		clu := settings.GetSetting().GetCluster(pipe.Cluster)
		if clu != nil && clu.IsFxiaokeCloud() {
			fxiaokePipes = append(fxiaokePipes, pipe)
		} else {
			dedicatedCloudPipes = append(dedicatedCloudPipes, pipe)
		}
	}

	podResourceText := ""
	appAutoScaleText := ""
	dedicatedCloudPodResource := ""
	if len(fxiaokePipes) > 0 {
		podResourceText = fmt.Sprintf("%d 个环境\\n", len(fxiaokePipes))
		for _, it := range fxiaokePipes {
			podResourceText += fmt.Sprintf("【%s】CPU: %.1f - %.1f； 内存: %d - %d； Pod: %d\\n",
				it.Namespace, it.Resources.RequestCPU, it.Resources.LimitCPU, it.Resources.RequestMemory, it.Resources.LimitMemory, it.Replicas)
			if v := getAutoScaleDesc(it.Cluster, it.Namespace, it.App); v != "" {
				appAutoScaleText += v + "\\n"
			}
		}
	} else {
		podResourceText = "纷享云无部署"
	}

	if len(dedicatedCloudPipes) > 0 {
		dedicatedCloudPodResource = fmt.Sprintf("%d 个环境\\n", len(dedicatedCloudPipes))
		for _, it := range dedicatedCloudPipes {
			dedicatedCloudPodResource += fmt.Sprintf("【%s】CPU: %.1f - %.1f； 内存: %d - %d； Pod: %d\\n",
				it.Namespace, it.Resources.RequestCPU, it.Resources.LimitCPU, it.Resources.RequestMemory, it.Resources.LimitMemory, it.Replicas)
		}
	} else {
		dedicatedCloudPodResource = "专属云无部署"
	}

	objId, err := CreateAppReviewObject(app, user.EmployeeId, podResourceText, dedicatedCloudPodResource, appAutoScaleText)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if objId != "" {
		web.SuccessJson(c, "https://www.fxiaoke.com/XV/UI/Home#objdetail/service_health_review__c/"+objId)
		return
	}
}
func CreateAppReviewObject(appName string, userId int, podResourceText string, dedicatedCloudPodResource string, appAutoScaleText string) (string, error) {
	body, err := buildAppReviewBody(appName, userId, podResourceText, dedicatedCloudPodResource, appAutoScaleText)
	if err != nil {
		return "", err
	}
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-fs-ei", strconv.Itoa(1)).
		SetHeader("x-fs-userinfo", strconv.Itoa(userId))

	req.SetBody(body)
	url := fmt.Sprintf("%s/v3/object_data/service_health_review__c/create", config.Conf.FsPaas.Host)
	log.Info("=== paas object body ===")
	log.Info(body)
	resp, err := req.Post(url)
	if err != nil {
		return "", err
	}

	if resp.StatusCode() != 200 && resp.StatusCode() != 201 {
		return "", fmt.Errorf("send paas message fail, %s, %s", resp.Status(), string(resp.Body()))
	}

	/*返回格式
	{
	    "code": 0,
	    "message": "OK",
	    "data": {
	        "objectData": {
	            "record_type": "default__c",
	            "owner": [
	                "5548"
	            ],
	            "package": "CRM",
	            "object_describe_api_name": "service_health_review__c",
	            "review_date__c": 1740412800000,
	            "service__c": "631ad25048f2da0001a68d7a",
	            "pod_resource__c": "<todo>",
	            "pod_status_peak_period__c": "<todo>",
	            "pod_exception_details__c": "<todo>",
	            "pod_autoscaling_strategy__c": "<todo>",
	            "app_core_storage_evaluatio__c": "<todo>",
	            "monitor_screenshot__c": {
	                "__xt": {
	                    "__json": {
	                        "type": "doc",
	                        "content": [
	                            {
	                                "type": "paragraph",
	                                "content": [
	                                    {
	                                        "text": "<todo>",
	                                        "type": "text"
	                                    }
	                                ],
	                                "attrs": {
	                                    "textAlign": "left"
	                                }
	                            }
	                        ]
	                    },
	                    "__summeryNodeCount": 1,
	                    "version": 2
	                },
	                "text": "<todo>"
	            },
	            "app_optimization__c": "",
	            "dedicated_cloud_pod_resour__c": "<todo>",
	            "cloud_optimization__c": "",
	            "core_api_call_assess__c": {
	                "__xt": {
	                    "__json": {
	                        "type": "doc",
	                        "content": [
	                            {
	                                "type": "paragraph",
	                                "content": [
	                                    {
	                                        "text": "<todo>",
	                                        "type": "text"
	                                    }
	                                ],
	                                "attrs": {
	                                    "textAlign": "left"
	                                }
	                            }
	                        ]
	                    },
	                    "__summeryNodeCount": 1,
	                    "version": 2
	                },
	                "text": "<todo>"
	            },
	            "heavy_api_assess__c": "<todo>",
	            "api_optimization__c": "",
	            "app_isolation_assess__c": "<todo>",
	            "middleware_isolation_assess__c": "<todo>",
	            "flow_control_evaluation__c": "",
	            "isolation_optimization__c": "",
	            "ext_service_dependency__c": "<todo>",
	            "ext_middleware_dependency__c": "<todo>",
	            "dependency_optimization__c": "",
	            "business_metric_evaluation__c": "",
	            "unique_metric_tasks__c": "",
	            "tenant_id": "1",
	            "_id": "67bdb3c2976fd10001cb1126",
	            "created_by": [
	                "5548"
	            ],
	            "last_modified_by": [
	                "5548"
	            ],
	            "create_time": 1740485570458,
	            "lock_status": "0",
	            "life_status": "normal",
	            "relevant_team": [
	                {
	                    "teamMemberEmployee": [
	                        "5548"
	                    ],
	                    "teamMemberType": "0",
	                    "teamMemberRole": "1",
	                    "teamMemberPermissionType": "2",
	                    "teamMemberDeptCascade": "0"
	                }
	            ],
	            "data_own_department": [
	                "21"
	            ],
	            "name": "2025-02_10",
	            "is_deleted": false,
	            "owner_department": "",
	            "content_completion_eva__c": "",
	            "optimization_suggestion__c": "",
	            "version": 1,
	            "order_by": null,
	            "data_auth_code": "4bbcd536d6c96c00",
	            "extend_obj_data_id": null,
	            "last_modified_time": 1740485570458,
	            "core_api_call_assess__c__o": "<todo>",
	            "monitor_screenshot__c__o": "<todo>"
	        },
	        "relatedDataList": {},
	        "isDuplicate": false,
	        "writeDB": true,
	        "newObjectData": {}
	    }
	}
	*/
	respBody := resp.String()
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(respBody), &result); err != nil {
		return "", nil
	}
	log.Info("=== paas object result ===")
	log.Info(respBody)

	data, ok := result["data"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf(respBody)
	}

	objectData, ok := data["objectData"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf(respBody)
	}

	id, ok := objectData["_id"].(string)
	if !ok {
		return "", fmt.Errorf(respBody)
	}
	return id, nil
}

func checkAppReviewCRMObjectIsExist(appName string, userId int) bool {
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-fs-ei", strconv.Itoa(1)).
		SetHeader("x-fs-userinfo", strconv.Itoa(userId))

	body := fmt.Sprintf(`{
"search_query_info":"{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"service__c.display_name\",\"field_values\":[\"%s\"],\"operator\":\"EQ\",\"filterGroup\":\"1\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}",
"field_projection":["service__c__r","service_level__c"],
"need_return_count_num":false
}`, appName)
	req.SetBody(body)
	url := fmt.Sprintf("%s/v3/object_data/service_health_review__c/find_simple", config.Conf.FsPaas.Host)
	log.Info("=== paas message body ===")
	log.Info(body)
	resp, err := req.Post(url)
	if err != nil {
		return false
	}

	if resp.StatusCode() != 200 && resp.StatusCode() != 201 {
		return false
	}
	repoBody := resp.String()
	return strings.Contains(repoBody, appName) && strings.Contains(repoBody, "service_level__c")
}

func buildAppReviewBody(appName string, userId int, podResource string, dedicatedCloudPodResource string, appAutoScaleText string) (string, error) {
	appObjectId := getAppObjectId(appName)
	if appObjectId == "" {
		return "", fmt.Errorf("请确认 %s 在【研发服务模块】对象里的服务等级是否为L0或L1", appName)
	}
	p := map[string]interface{}{
		"SysUserId":                 userId,
		"AppObjectId":               appObjectId,
		"PodResource":               podResource,
		"DedicatedCloudPodResource": dedicatedCloudPodResource,
		"AppAutoScaleText":          appAutoScaleText,
	}
	tepl := `
    {
      "object_data": {
        "record_type": "default__c",
				"owner":["{{.SysUserId}}"],
				"package":"CRM",
				"create_by":"{{.SysUserId}}",
				"object_describe_api_name":"service_health_review__c",
				"review_date__c": 1740412800000,
				"service__c": "{{.AppObjectId}}",
				"pod_resource__c": "{{.PodResource}}",
				"pod_status_peak_period__c": "<todo>",
				"pod_exception_details__c": "<todo>",
				"pod_autoscaling_strategy__c": "{{.AppAutoScaleText}}",
				"app_core_storage_evaluatio__c": "<todo>",
				"monitor_screenshot__c": {
      "text": "<todo>",
      "__xt": {
        "__json": {
          "type": "doc",
          "content": [
            {
              "type": "paragraph",
              "attrs": {
                "textAlign": "left"
              },
              "content": [
                {
                  "type": "text",
                  "text": "<todo>"
                }
              ]
            }
          ]
        },
        "__summeryNodeCount": 1,
        "version": 2
      }
    },
    "app_optimization__c": "",
    "dedicated_cloud_pod_resour__c": "{{.DedicatedCloudPodResource}}",
    "cloud_optimization__c": "",
    "core_api_call_assess__c": {
      "text": "<todo>",
      "__xt": {
        "__json": {
          "type": "doc",
          "content": [
            {
              "type": "paragraph",
              "attrs": {
                "textAlign": "left"
              },
              "content": [
                {
                  "type": "text",
                  "text": "<todo>"
                }
              ]
            }
          ]
        },
        "__summeryNodeCount": 1,
        "version": 2
      }
    },
    "heavy_api_assess__c": "<todo>",
    "api_optimization__c": "",
    "app_isolation_assess__c": "<todo>",
    "middleware_isolation_assess__c": "<todo>",
    "flow_control_evaluation__c": "",
    "isolation_optimization__c": "",
    "ext_service_dependency__c": "<todo>",
    "ext_middleware_dependency__c": "<todo>",
    "dependency_optimization__c": "",
    "business_metric_evaluation__c": "",
    "unique_metric_tasks__c": "",
    "requestId": "d3bf76ca80cb4c19a5bc0a6082d51508"
      },
      "optionInfo": {
			  "supportValidationResult": true,
        "useValidationRule": true,
        "isDuplicateSearch": false,
        "skipFuncValidate": true
      }
    }
`
	return template.ParseTemplate(tepl, p)
}

func getAppObjectId(app string) string {
	m := map[string]string{
		"fs-huawei-kit-web":                            "6762bdab9d7242000187f20f",
		"fs-k8s-node-vip":                              "6757ba185fd40600018402f6",
		"fs-qywx-web":                                  "67530891eb81720001082fab",
		"fs-paas-app-udobj-02":                         "6752a6b94bda540001b22ed2",
		"fs-crm-all":                                   "66eaa0f47a111e000101e835",
		"fs-open-custom-hisense-decrypt-file":          "66b336b037f5c700015d794a",
		"fs-open-qywx-all":                             "669a2405db0eed000115060c",
		"fs-file-server":                               "6606ed26ba829b0001244b17",
		"fs-sync-data-all":                             "65bd00a84590b200019a44c9",
		"fs-stone-auth":                                "6595448ef36d6800016fd559",
		"fs-paas-ai-provider":                          "65042b500960070001c5a2bf",
		"fs-manufacturing-task":                        "64ede814743c300001576d32",
		"fs-ai-detector-provider":                      "64d59a785b3139000161142b",
		"fs-ui-paas-cloud":                             "64ba2d90bf1ba60001a3afd3",
		"fs-oms-provider":                              "64a78e5874aa43000113de56",
		"fs-app-view":                                  "64a55c12cb9f980001f86a38",
		"erp-connector-proxy":                          "6492de23acbc7f00011b2e2b",
		"fs-crm-fmcg-sales":                            "649182bf2acf730001ecd9f4",
		"fs-enterprise-relation-biz":                   "6488698d84a50a00010154d1",
		"fs-mq-bus":                                    "645e61e0ab1a1c00018648e7",
		"fs-erp-sync-data-file":                        "645bb348de8370000104e36f",
		"fs-mq-sort":                                   "6454fb31f9877c00017d48d4",
		"fs-crm-manufacturing":                         "64491cbfe02b3c0001b4ed61",
		"fs-erp-order-contacts-proxy":                  "643907b7c85c8d000111ae9e",
		"fs-feishu-provider":                           "643907782068ef00018e26a2",
		"fs-feishu-web":                                "643907432068ef00018da32f",
		"fs-open-app-center-all":                       "641d8da9374f090001c72af2",
		"fs-crm-fmcg-wq":                               "641c1f6b20ab6a0001ad9bd9",
		"fs-fsc-cgi":                                   "641438a37157c90001373994",
		"fs-enterprise-relation-biz-login":             "640b028ddb31fa0001520dd7",
		"fs-enterprise-relation-rest":                  "6401d4c564ca490001d43d21",
		"fs-enterprise-relation-http":                  "6401d4b564ca490001d41ec8",
		"fs-user-login-cgi":                            "631fee512d87620001604c10",
		"fs-access-proxy":                              "631fee512d87620001604c0d",
		"fs-access-proxy-provider":                     "631fee512d87620001604c0a",
		"fs-message-server-gray":                       "631ad25048f2da0001a68d81",
		"fs-qixin-provider":                            "631ad25048f2da0001a68d7a",
		"fs-stone-dataserver":                          "631ad24b93a2d40001287b7e",
		"fs-eservice-web":                              "631ad24b93a2d40001287b77",
		"fs-qixin-web":                                 "631ad24b93a2d40001287b6d",
		"fs-polling-provider":                          "631ad24893a2d40001287832",
		"fs-warehouse-cross":                           "631ad24893a2d4000128782d",
		"fs-dingtalk-cloud":                            "631ad24648f2da0001a6899a",
		"fs-k3cloud-web":                               "631ad24393a2d400012874af",
		"cctrl-center":                                 "631ad24393a2d400012874a5",
		"fs-user-login-biz":                            "631ad24393a2d4000128749d",
		"action-router-service":                        "631ad24148f2da0001a6865a",
		"fs-organization-provider-4orgbiz":             "631ad24148f2da0001a68655",
		"fs-erp-sync-data-custom":                      "631ad24148f2da0001a68650",
		"weex-console-service":                         "631ad24148f2da0001a6864f",
		"fs-plat-org-adapter-provider":                 "631ad24148f2da0001a68643",
		"fs-plat-organization-cloud":                   "631ad23c48f2da0001a67f69",
		"fs-qixin-file":                                "631ad24148f2da0001a68641",
		"qywx-message-send-provider":                   "631ad24148f2da0001a6863b",
		"fs-paas-bizconf-web":                          "631ad24148f2da0001a68638",
		"async-job-import":                             "631ad24148f2da0001a68637",
		"fs-organziation-adapter-4orgbiz":              "631ad24148f2da0001a68633",
		"cloud-mq-proxy":                               "631ad23e48f2da0001a682fd",
		"fs-pod-service":                               "631ad23e48f2da0001a682eb",
		"fs-plat-polling-cloud":                        "631ad23e48f2da0001a682e4",
		"fs-organization-provider-4data-auth":          "631ad23e48f2da0001a682d9",
		"fs-active-session-manage":                     "631ad23e48f2da0001a682d7",
		"fast-notifier":                                "631ad23e48f2da0001a682d3",
		"fs-paas-sysmanage-compose":                    "631ad23c48f2da0001a67f6b",
		"fs-paas-job-schedule":                         "631ad23c48f2da0001a67f6a",
		"async-job-export":                             "631ad23c48f2da0001a67f62",
		"fs-open-msg-all":                              "631ad23c48f2da0001a67f60",
		"fs-message-server":                            "631ad23c48f2da0001a67f5a",
		"fs-message-server-web":                        "631ad23c48f2da0001a67f59",
		"fs-scheduler-task-provider":                   "631ad23c48f2da0001a67f58",
		"config-service":                               "631ad23c48f2da0001a67f55",
		"fs-paas-web":                                  "631ad23c48f2da0001a67f52",
		"fs-flow-inspection":                           "631ad23c48f2da0001a67f50",
		"fs-qixin-cloud":                               "631ad23c48f2da0001a67f4f",
		"fs-paas-license-surrogate":                    "631ad23c48f2da0001a67f4c",
		"fs-metadata-rest-cloud":                       "631ad23c48f2da0001a67f46",
		"fs-paas-app-udobj-rest4realtime":              "631ad23c48f2da0001a67f45",
		"fs-metadata-provider":                         "631ad23c48f2da0001a67f44",
		"fs-marketing-task":                            "631ad23c48f2da0001a67f40",
		"fs-bi-org":                                    "631ad23948f2da0001a67bd4",
		"fs-plat-fap":                                  "631ad23948f2da0001a67bd2",
		"fs-dingtalk-provider":                         "631ad23948f2da0001a67bd0",
		"fs-dingtalk-web":                              "631ad23948f2da0001a67bcf",
		"fs-stone-proxy":                               "631ad23948f2da0001a67bc9",
		"fs-apibus-global":                             "631ad23948f2da0001a67bc7",
		"fs-apibus-ncrm":                               "631ad23948f2da0001a67bc6",
		"fs-apibus-paas":                               "631ad23948f2da0001a67bc5",
		"fs-stone-cgi":                                 "631ad23948f2da0001a67bc4",
		"fs-metadata-data":                             "631ad23948f2da0001a67bc1",
		"fs-fsc4stone":                                 "631ad23948f2da0001a67bc0",
		"i18n-service":                                 "631ad23948f2da0001a67bbd",
		"fs-hubble-cloud":                              "631ad23948f2da0001a67bba",
		"fs-erp-sync-data-task":                        "631ad23948f2da0001a67bb9",
		"fs-stage-propeller-processor":                 "631ad23948f2da0001a67bb8",
		"fs-hubble-index":                              "631ad23948f2da0001a67bb5",
		"fs-crm-workflow-cloud":                        "631ad23948f2da0001a67baf",
		"fs-stage-propeller-cloud":                     "631ad23948f2da0001a67bae",
		"fs-polling-cgi":                               "631ad23948f2da0001a67bad",
		"fs-paas-metadata-dataloader":                  "631ad23948f2da0001a67baa",
		"fs-bi-export":                                 "631ad23948f2da0001a67ba7",
		"fs-todo":                                      "631ad23948f2da0001a67ba6",
		"fs-sail-server":                               "631ad23948f2da0001a67ba5",
		"fs-user-extension-provider":                   "631ad23948f2da0001a67ba4",
		"fs-qixin-biz-web":                             "631ad23748f2da0001a67837",
		"fs-bpm-after-action":                          "631ad23748f2da0001a67836",
		"fs-workflow-processor":                        "631ad23748f2da0001a67835",
		"fs-bi-compose":                                "631ad23748f2da0001a67834",
		"fs-fmcg-service":                              "631ad23748f2da0001a67833",
		"fs-stone4cloud":                               "631ad23748f2da0001a67832",
		"fs-organization-biz":                          "631ad23748f2da0001a67831",
		"fs-organization-adapter":                      "631ad23748f2da0001a67830",
		"fs-open-qywx-save-message":                    "631ad23748f2da0001a6782f",
		"fs-paas-app-udobj-rest4flow":                  "631ad23748f2da0001a6782d",
		"fs-paas-rule":                                 "631ad23748f2da0001a6782b",
		"fs-online-consult-base":                       "631ad23748f2da0001a6782a",
		"fs-crm-import-manufacturing":                  "631ad23748f2da0001a67829",
		"open-api-gateway-web":                         "631ad23748f2da0001a67828",
		"fs-plat-webhook-provider":                     "631ad23748f2da0001a67824",
		"qywx-account-bind-provider":                   "631ad23748f2da0001a67821",
		"fs-bi-cloud":                                  "631ad23748f2da0001a67820",
		"fs-flow":                                      "631ad23748f2da0001a6781e",
		"data-auth-service-bi":                         "631ad23748f2da0001a6781a",
		"fs-eservice-mq-listener":                      "631ad23748f2da0001a67819",
		"fs-plat-service-provider":                     "631ad23748f2da0001a67818",
		"data-auth-worker":                             "631ad23748f2da0001a67816",
		"data-auth-service":                            "631ad23748f2da0001a67815",
		"tenant-sandbox":                               "631ad23748f2da0001a67814",
		"fs-paas-app-udobj-rest":                       "631ad23748f2da0001a67813",
		"fs-plat-social-cloud":                         "631ad23748f2da0001a67812",
		"fs-marketing-statistic-provider":              "631ad23748f2da0001a67811",
		"fs-bi-uitype":                                 "631ad23748f2da0001a6780f",
		"fs-online-consult-web":                        "631ad23493a2d4000128709c",
		"fs-marketing-statistic":                       "631ad23493a2d4000128709b",
		"i18n-setting":                                 "631ad23493a2d4000128709a",
		"fs-bi-metadata":                               "631ad23493a2d40001287097",
		"fs-paas-auth-provider":                        "631ad23493a2d40001287090",
		"fs-bpm-processor":                             "631ad23493a2d4000128708f",
		"fs-paas-license":                              "631ad23493a2d4000128708c",
		"fs-metadata-rest":                             "631ad23493a2d4000128708a",
		"fs-flow-processor":                            "631ad23493a2d40001287088",
		"fs-stone-fileserver":                          "631ad23493a2d40001287083",
		"checkins-office-v2-server":                    "631ad23493a2d40001287082",
		"fs-stone-metaserver":                          "631ad23493a2d40001287080",
		"paas-pg-scanner":                              "631ad23493a2d4000128707b",
		"fs-stone-cloud":                               "631ad23493a2d4000128707a",
		"fs-crm-recycling-task":                        "631ad23493a2d40001287075",
		"fs-crm-task-lto-sfa":                          "631ad23493a2d40001287070",
		"fs-crm-workflow":                              "631ad23293a2d40001286b73",
		"fs-crm-import-sfa":                            "631ad23293a2d40001286b72",
		"fs-crm-workflow-processor":                    "631ad23293a2d40001286b71",
		"fs-marketing-web-kis":                         "631ad23293a2d40001286b69",
		"fs-webpage-customer-provider":                 "631ad23293a2d40001286b67",
		"fs-user-extension-biz":                        "631ad23293a2d40001286b65",
		"fs-bpm":                                       "631ad23293a2d40001286b64",
		"fs-stage-propeller":                           "631ad23293a2d40001286b63",
		"fs-bpm-cloud":                                 "631ad23293a2d40001286b62",
		"fs-crm-task-web":                              "631ad23293a2d40001286b5e",
		"fs-crm-fmcg-service":                          "631ad23293a2d40001286b5d",
		"fs-hubble-query":                              "631ad23293a2d40001286b5c",
		"fs-paas-workflow-processor":                   "631ad23293a2d40001286b5b",
		"fs-paas-workflow":                             "631ad23293a2d40001286b5a",
		"fs-paas-calculate-task":                       "631ad23293a2d40001286b59",
		"fs-crm":                                       "631ad23293a2d40001286b58",
		"fs-paas-workflow-cloud":                       "631ad23293a2d40001286b57",
		"fs-organization-provider":                     "631ad23293a2d40001286b56",
		"fs-workflow":                                  "631ad23293a2d40001286b54",
		"fs-refresh-es-data":                           "631ad23293a2d40001286b4d",
		"fs-crm-import":                                "631ad23293a2d40001286b4c",
		"fs-erp-oa":                                    "631ad23293a2d40001286b4a",
		"fs-bi-stat":                                   "631ad23293a2d40001286b47",
		"fs-marketing-qywx":                            "631ad23048f2da0001a6723d",
		"fs-marketing-provider":                        "631ad23048f2da0001a6723c",
		"fs-social-feeds":                              "631ad23048f2da0001a6723b",
		"paas-db-operator":                             "631ad23048f2da0001a67239",
		"fs-appserver-checkins-v2":                     "631ad23048f2da0001a67237",
		"fs-uc-provider":                               "631ad23048f2da0001a67234",
		"fs-plat-user-login-provider":                  "631ad23048f2da0001a67232",
		"paas-leica-sync":                              "631ad23048f2da0001a67231",
		"fs-dingtalk-all":                              "631ad23048f2da0001a6722d",
		"fs-crm-sfa":                                   "631ad23048f2da0001a6722c",
		"fs-paas-gnomon-executor":                      "631ad23048f2da0001a6722b",
		"fs-paas-function-service-background-provider": "631ad23048f2da0001a67229",
		"fs-crm-task-sfa":                              "631ad23048f2da0001a67228",
		"fs-marketing":                                 "631ad23048f2da0001a67227",
		"fs-erp-sync-data":                             "631ad23048f2da0001a67226",
		"fs-cep-provider":                              "631ad23048f2da0001a67225",
		"fs-erp-sync-data-web":                         "631ad23048f2da0001a67224",
		"fs-plat-app-view-cgi":                         "631ad23048f2da0001a67223",
		"fs-feeds-biz":                                 "631ad23048f2da0001a67222",
		"qywx-account-sync-provider":                   "631ad23048f2da0001a67220",
		"qywx-event-handler-web":                       "631ad23048f2da0001a6721f",
		"fs-plat-user-login-cgi":                       "631ad23048f2da0001a6721e",
		"fs-open-dingtalk":                             "631ad23048f2da0001a67219",
		"fs-plat-login-cloud":                          "631ad23048f2da0001a67216",
		"fs-sail-order":                                "631ad23048f2da0001a67215",
		"fs-paas-app-udobj":                            "631ad23048f2da0001a67214",
		"fs-fmcg-sales-cgi":                            "631ad23048f2da0001a67213",
		"fs-paas-function-service-runtime":             "631ad23048f2da0001a67212",
		"fs-paas-function-service-runtime-provider":    "631ad23048f2da0001a67211",
		"fs-open-custom":                               "631ad23048f2da0001a6720f",
		"fs-feeds-provider":                            "631ad23048f2da0001a6720d",
		"fs-bi-udf-report":                             "631ad23048f2da0001a6720c",
	}
	return m[app]
}

func getAutoScaleDesc(cluster, namespace, app string) string {
	clu := settings.GetSetting().GetCluster(cluster)
	if clu == nil {
		return ""
	}

	if podAutoscalers, err := pascli.ListPodAutoScalerFromK8sWithCache(cluster, "", 30*time.Minute); err == nil {
		for _, it := range podAutoscalers.Items {
			if it.Namespace == namespace && it.Name == app {
				return fmt.Sprintf("环境：%s，CPU阈值：%s，持续时间：%d 秒，扩容到： %d", namespace, it.Spec.Triggers[0].Metadata["value"], it.Spec.ScaleUp.StabilizationWindowSeconds, it.Spec.MaxReplicaCount)
			}
		}
	}
	return ""
}

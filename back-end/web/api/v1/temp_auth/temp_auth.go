package temp_auth

import (
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/temp_auth_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type TempAuthSearch struct {
	Approved string `form:"approved"`
	param.PageSearch
}

func Create(c *gin.Context) {
	var p dto.TempAuth
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	p.User = user.RealName
	p.Authorizer = user.RealName

	log_service.Create(user.RealName, "临时授权-添加", p.App, p)

	entity := p.ToModel()
	if entity.EndHour == "24:00" {
		entity.EndHour = "23:59"
	}

	if err := temp_auth_service.Create(&entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	web.SuccessJson(c, nil)
}

func TempAuthAdmin(c *gin.Context) {
	web.SuccessJson(c, perm_service.ListTempAuthAdmin())
}

func Audit(c *gin.Context) {
	id := c.Query("id")
	approved := c.DefaultQuery("approved", "true")
	if id == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id不能为空")
		return
	}
	idInt, err := strconv.ParseInt(id, 10, 32)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id格式错误")
		return
	}
	entity, err := temp_auth_service.FindById(uint(idInt))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) && !perm_service.IsTempAuthAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "无操作权限")
		return
	}

	entity.Approved = strings.ToUpper(approved) == "TRUE"
	entity.Approver = user.RealName
	if err := temp_auth_service.Update(&entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func Search(c *gin.Context) {
	var p TempAuthSearch
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}

	data := make(map[string]interface{})
	var approved *bool
	if p.Approved != "" {
		isApproved := strings.EqualFold(p.Approved, "true")
		approved = &isApproved
	}
	if entities, err := temp_auth_service.Search(p.Keyword, approved, p.Page, p.Limit); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		data["data"] = dto.ToTempAuthList(entities)
		data["count"] = temp_auth_service.Count(p.Keyword)
	}
	web.SuccessJson(c, data)
}

func DeleteById(c *gin.Context) {
	var (
		id  int
		err error
	)
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "你没有权限")
		return
	}
	if id, err = strconv.Atoi(c.Query("id")); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	log_service.Create(user.RealName, "临时授权-删除", "-", id)
	if err := temp_auth_service.DeleteById(uint(id)); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

package user

import (
	"fmt"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/org_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/user_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"strings"
	"time"
)

const userNamesCacheKey = "userNames"

func GetInfo(c *gin.Context) {
	//能进入到这， 说明过了权限filter
	user, _ := auth.GetUser(c)
	web.SuccessJson(c, user)
}

func IsAdmin(c *gin.Context) {
	user, _ := auth.GetUser(c)
	web.SuccessJson(c, perm_service.IsAdmin(user))
}

func AllNames(c *gin.Context) {
	if data, found := cache.GetStruct(key.Pre().USER.Key(userNamesCacheKey), []string{}); found {
		web.SuccessJson(c, data)
		return
	}
	data := make([]string, 0, 500)
	if entities, err := user_service.All(); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		for _, it := range entities {
			data = append(data, it.RealName)
		}
	}
	cache.SetStruct(key.Pre().USER.Key(userNamesCacheKey), data, 5*time.Minute)
	web.SuccessJson(c, data)
}

func ListByRealName(c *gin.Context) {
	realName := c.Query("realName")
	fmt.Println(realName)
	user, err := user_service.ListByRealName(realName)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, user)
}

func UpdateHistoryApp(c *gin.Context) {
	app := c.Query("app")
	if app != "" {
		if app_service.Exist(app) {
			apps := []string{app}
			if user, f := auth.GetUser(c); f {
				if user.RecentApps != nil {
					for _, it := range user.RecentApps {
						if !strslice.Find(apps, it) {
							apps = append(apps, it)
						}
					}
					if len(apps) > 20 {
						apps = apps[:20]
					}
				}
				user.RecentApps = apps
				if err := auth.UpdateUserInfo(c, user); err != nil {
					log.Warn("session update user fail, name:", user.Username, err.Error())
				}
				if err := user_service.UpdateRecentApp(user.Username, apps); err != nil {
					log.Warn("user recent apps update fail, name: ", user.Username, err.Error())
				}
			}
		}
	}
	web.SuccessJson(c, nil)
}

func ResignedAnalysis(c *gin.Context) {
	allUsers, err := getAllUsersOfSystem()
	if err != nil {
		log.Error("Failed to get all users: ", err.Error())
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	resignUsers := make([]string, 0, 50)
	for _, user := range allUsers {
		isResigned, err := user_service.UserIsResigned(user)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		if isResigned {
			resignUsers = append(resignUsers, user)
		}
	}
	web.SuccessJson(c, resignUsers)
}

func getAllUsersOfSystem() ([]string, error) {
	users := make(map[string]bool)

	// cms users
	cmsOwners, err := cmdb_service.AllAppOwners()
	if err != nil {
		return nil, fmt.Errorf("failed to load CMS app owners: %v", err)
	}

	for _, item := range cmsOwners {
		owners := append(append(make([]string, 0, 200), item.Owners...), item.MainOwner...)
		for _, owner := range owners {
			owner = strings.TrimSpace(owner)
			if owner != "" {
				users[owner] = true
			}
		}
	}

	// org users
	orgs, err := org_service.FindAll()
	if err != nil {
		return nil, fmt.Errorf("failed to load orgs: %v", err)
	}

	for _, org := range orgs {
		for _, user := range org.Users {
			if user != "" {
				users[user] = true
			}
		}
	}

	// app admins
	apps, err := app_service.FindAll()
	if err != nil {
		return nil, fmt.Errorf("failed to load publish server app data: %v", err)
	}
	for _, item := range apps {
		for _, admin := range item.Admins {
			if admin != "" {
				users[admin] = true
			}
		}
	}

	// Convert map to slice
	result := make([]string, 0, len(users))
	for user := range users {
		result = append(result, user)
	}
	return result, nil
}

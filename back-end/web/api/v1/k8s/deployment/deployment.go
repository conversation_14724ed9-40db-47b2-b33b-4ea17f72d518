package deployment

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	k8s_templates "fs-k8s-app-manager/k8s-templates"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	k8s_client "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/executor/task_executor"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/task_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/k8s"
	mapset "github.com/deckarep/golang-set/v2"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"sort"
	"strconv"
	"strings"
	"time"
)

type ImagePreheatParam struct {
	Cluster             string   `form:"cluster" binding:"required"`
	Namespaces          []string `form:"namespaces" binding:"required"`
	BaseImages          []string `form:"baseImages" binding:"-"`
	AppImageRecentHours int32    `form:"appImageRecentHours" binding:"-"`
	ImagePullParallel   int32    `form:"imagePullParallel" binding:"-"`
}

type UpdateResourceParam struct {
	Cluster       string  `form:"cluster" binding:"required"`
	Namespace     string  `form:"namespace" binding:"required"`
	App           string  `form:"app" binding:"required"`
	LimitCPU      float64 `form:"limitCPU" binding:"required"`
	LimitMemory   int64   `form:"limitMemory" binding:"required"`
	RequestCPU    float64 `form:"requestCPU" binding:"required"`
	RequestMemory int64   `form:"requestMemory" binding:"required"`
}

type SearchResult struct {
	dto.Deployment
	PipelineReplicas int32 `json:"pipelineReplicas"`
}

func List(c *gin.Context) {
	var p k8s.NamespaceParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	namespaces := make([]string, 0, 100)
	if strings.EqualFold(p.Namespace, "*") {
		namespaces = settings.GetSetting().GetClusterNamespaces(p.Cluster)
	} else {
		namespaces = append(namespaces, p.Namespace)
	}

	data := make([]dto.Deployment, 0, 500)
	pipeReplicasMap := map[string]int32{}
	for _, ns := range namespaces {
		items, err := k8s_service.ListDeployment(p.Cluster, ns)
		if pipes, err := pipeline_service.FindByClusterAndNamespace(p.Cluster, ns); err == nil {
			for _, pipe := range pipes {
				pipeReplicasMap[fmt.Sprintf("%s/%s/%s", pipe.Cluster, pipe.Namespace, pipe.App)] = int32(pipe.Replicas)
			}
		}
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		data = append(data, items...)
	}

	data2 := make([]SearchResult, 0, len(data))
	for _, item := range data {
		pipeReplica := int32(-1)
		if v, found := pipeReplicasMap[fmt.Sprintf("%s/%s/%s", item.Cluster, item.Namespace, item.Name)]; found {
			pipeReplica = v
		}
		data2 = append(data2, SearchResult{
			Deployment:       item,
			PipelineReplicas: pipeReplica,
		})
	}
	web.SuccessJson(c, data2)
}

func UpdateResource(c *gin.Context) {
	var p UpdateResourceParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	log_service.Create(auth.GetRealName(c), "应用-资源临时调整", p.App, p)
	err := k8s_service.UpdateDeploymentResource(p.Cluster, p.Namespace, p.App, p.LimitCPU, p.LimitMemory, p.RequestCPU, p.RequestMemory)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}
func DeleteImagePreheatJobs(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有操作权限")
	}

	cluster := c.Query("cluster")
	clu := settings.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("集群%s不存在", cluster))
		return
	}
	job := c.Query("job")
	if job == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "job名不能为空")
		return
	}
	namespace := c.DefaultQuery("namespace", "kube-public")
	err := kubectl.DeleteJob(cluster, namespace, job)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}
func ImagePreheatJobs(c *gin.Context) {
	cluster := c.Query("cluster")
	clu := settings.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("集群%s不存在", cluster))
		return
	}
	namespace := c.DefaultQuery("namespace", "kube-public")
	app := "fs-image-preheat"
	pods, err := k8s_client.GetPodList(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data := pods.Items
	sort.Slice(data, func(i, j int) bool {
		return strings.Compare(data[i].CreationTimestamp.String(), data[j].CreationTimestamp.String()) > 0
	})
	web.SuccessJson(c, data)
}

func ImagePreheat(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有操作权限")
	}

	var p ImagePreheatParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	clu := settings.GetSetting().GetCluster(p.Cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("集群%s不存在", p.Cluster))
		return
	}
	if clu.ImageRegistryProxy == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "集群未配置镜像仓库代理地址，不支持镜像预热")
		return
	}
	namespaceImages := make([]string, 0, 100)
	if len(p.Namespaces) > 0 {
		for _, cluster := range settings.GetSetting().Clusters {
			//只查找启用了的纷享云集群环境下的镜像
			if !cluster.Enable || !cluster.IsFxiaokeCloud() {
				continue
			}
			for _, ns := range p.Namespaces {
				if deps, err := k8s_client.GetDeploymentList(cluster.Name, ns); err == nil {
					for _, dep := range deps.Items {
						if *dep.Spec.Replicas < 1 {
							continue
						}
						for _, c := range dep.Spec.Template.Spec.InitContainers {
							namespaceImages = append(namespaceImages, clu.NormalImage2ProxyImage(c.Image))
						}
					}
				}
			}
		}
	}

	tasks, err := task_service.SearchAfterTime(string(models.TASK_TYPE_BUILD),
		[]string{string(models.TASK_PHASE_SUCCESS)},
		time.Now().Add(-time.Duration(p.AppImageRecentHours)*time.Hour))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	envs := datatype.Envs{}
	imagePullParallel := p.ImagePullParallel
	if imagePullParallel == 0 {
		imagePullParallel = 1
	}
	envs = append(envs, datatype.Env{
		Name:  "IMAGE_PULL_PARALLEL",
		Value: fmt.Sprintf("%d", imagePullParallel),
	})
	baseImages := make([]string, 0, 30)
	for _, it := range p.BaseImages {
		baseImages = append(baseImages, clu.NormalImage2ProxyImage(it))
	}
	appImages := mapset.NewSet[string]()
	for _, it := range tasks {
		var taskParams task_executor.BuildParam
		if err := mapstructure.Decode(it.Params, &taskParams); err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		//复制云的镜像不需要预热
		if taskParams.ParentPom == "fxiaoke-parent-pom-forceecrm-rc" {
			continue
		}
		appImages.Add(clu.NormalImage2ProxyImage(taskParams.ArtifactImage))
	}

	images := make([]string, 0, 500)
	images = append(images, baseImages...)
	images = append(images, namespaceImages...)
	images = append(images, appImages.ToSlice()...)
	images = strslice.RemoveDuplicates(images)
	for idx, image := range images {
		envs = append(envs, datatype.Env{
			Name:  fmt.Sprintf("IMAGE_PATH_%04d", idx+1),
			Value: image,
		})
	}

	params := make(map[string]interface{})
	params["Cluster"] = p.Cluster
	params["Namespace"] = "kube-public"
	params["App"] = "fs-image-preheat"
	params["RecentHours"] = p.AppImageRecentHours
	params["JobName"] = "fs-image-preheat-" + time.Now().Format("200601021504")
	params["Image"] = config.Conf.Harbor.Host + "/base/fs-image-preheat:v1.1"
	params["Envs"] = envs

	yaml, err := k8s_templates.BuildContentWithFilename("fs-image-preheat-job.yaml.tmpl", params)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(user.RealName, "Image-Preheat", p.Cluster, yaml)
	if err = kubectl.Create(p.Cluster, "kube-public", yaml, false); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, yaml)
}
func ListAppReplicaSet(c *gin.Context) {
	var p k8s.DeploymentParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	items, err := k8s_service.ListAppReplicaSet(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	//场景说明： 系统前期，service与pod之间的端口通过端口号关联；系统后期，service与pod之间的端口通过端口名关联
	//如果service与pod是通过端口名关联的， 但回滚的ReplicaSet没有配置端口名，则回滚后会导致无法Pod无法正常访问
	//处理规则： 不允许回滚到没有配置端口名的ReplicaSet
	data := make([]dto.ReplicaSet, 0, len(items.Items))
	for _, item := range items.Items {
		portHasName := false
		for _, port := range item.Spec.Template.Spec.Containers[0].Ports {
			if port.Name == "http" {
				portHasName = true
				break
			}
		}
		if portHasName {
			data = append(data, *k8s_service.ParseReplicaSetDTO(&item))
		}
	}
	web.SuccessJson(c, data)
}

func Rollback(c *gin.Context) {
	var p RollbackParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有应用权限")
	}

	if err := k8s_service.Rollout(p.Cluster, p.Namespace, p.App, strconv.Itoa(int(p.Revision))); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      user.RealName,
		ResourceType: "app",
		ResourceId:   p.App,
		Title:        "应用版本回滚",
		Message:      fmt.Sprintf("版本回滚到: %s", p.DeployTag),
		Level:        "info",
		Extra:        "",
	})
	log_service.Create(user.RealName, "应用-回滚", p.App, p)
	event_service.Create(user.RealName, event_service.BuildAppKey(p.Cluster, p.Namespace, p.App), "回滚服务到版本 "+p.DeployTag)
	time.Sleep(2 * time.Second)
	web.SuccessJson(c, "success")
}
func Redeploy(c *gin.Context) {
	var p k8s.RedeployParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "你没有应用操作权限")
		return
	}
	if err := k8s_service.AppRedeploy(p.Cluster, p.Namespace, p.App, strconv.Itoa(p.MaxSurge)+"%"); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "服务-重启", p.App, p)
	event_service.Create(user.RealName, event_service.BuildAppKey(p.Cluster, p.Namespace, p.App), "手动重启服务")
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      user.RealName,
		ResourceType: "app",
		ResourceId:   p.App,
		Title:        "应用重启",
		Message:      fmt.Sprintf("%s 手动重启了 %s 服务", user.RealName, p.App),
		Level:        "warn",
		Extra:        "",
	})
	time.Sleep(2 * time.Second)
	web.SuccessJson(c, "success")
}

func UseRecreateDeployStrategy(c *gin.Context) {
	var p k8s.DeploymentParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		panic("你没有应用权限")
	}
	if err := k8s_service.UpdateDeployStrategyToRecreate(p.Cluster, p.Namespace, p.App); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "服务-使用重建升级", p.App, p)
	event_service.Create(user.RealName, event_service.BuildAppKey(p.Cluster, p.Namespace, p.App), "使用重建模式升级服务")
	time.Sleep(1 * time.Second)
	web.SuccessJson(c, "success")
}
func Scale(c *gin.Context) {
	var p ScaleParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有操作权限，请联系应用管理员")
	}
	oldReplicas := int32(-1)
	if dep, err := k8s_service.GetDeploymentDTO(p.Cluster, p.Namespace, p.App); err == nil {
		oldReplicas = dep.Replicas
	}
	if err := kubectl.Scale(p.Cluster, p.Namespace, p.App, p.Replicas); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	eventTitle := "应用手动扩容"
	title := "服务-手动扩容"
	if oldReplicas > p.Replicas {
		eventTitle = "应用手动缩容"
		title = "服务-手动缩容"
	}

	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      user.RealName,
		ResourceType: "app",
		ResourceId:   p.App,
		Title:        eventTitle,
		Message:      fmt.Sprintf("操作人: %s | 副本数: %d → %d", user.RealName, oldReplicas, p.Replicas),
		Level:        "warn",
		Extra:        "",
	})
	event_service.Create(user.RealName, event_service.BuildAppKey(p.Cluster, p.Namespace, p.App),
		fmt.Sprintf("%s，副本数: %d → %d", title, oldReplicas, p.Replicas))
	log_service.Create(user.RealName, title, p.App, p)
	time.Sleep(2 * time.Second)
	web.SuccessJson(c, "success")
}

func Detail(c *gin.Context) {
	var p k8s.DeploymentParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data, err := k8s_service.GetDeploymentDTO(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func DetailWhole(c *gin.Context) {
	var p k8s.DeploymentParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data, err := k8s_service.DeploymentDetail(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

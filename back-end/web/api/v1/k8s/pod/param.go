package pod

import "fs-k8s-app-manager/web/api/v1/k8s"

type FileParam struct {
	k8s.PodParam
	Path     string `form:"path" binding:"required"`
	FileSize int64  `form:"fileSize" binding:"-"`
}

type ArchiveParam struct {
	k8s.PodParam
	Dir   string   `form:"dir" binding:"required"`
	Files []string `form:"files" binding:"required"`
}

type StdoutLogParam struct {
	k8s.PodParam
	Container string `form:"container"`
	TailLines int64 `form:"tailLines"`
	Previous  bool  `form:"previous"`
}

type EventReportParam struct {
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Type      string `json:"type"`
	Reason    string `json:"reason"`
	Message   string `json:"message"`
	Action    string `json:"action"`
}
type BootTimeReportParam struct {
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Value     int    `json:"value"`
}

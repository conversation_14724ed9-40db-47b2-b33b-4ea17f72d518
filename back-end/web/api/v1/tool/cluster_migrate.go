package tool

import (
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/client/clickhouse"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"io"
	"sort"
	"strconv"
	"strings"
	"time"
)

func PipelineBatchClone(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := string(body)
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 200 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多200个发布流程")
		return
	}
	ret := make(map[string]interface{})
	data := make([]dto.Pipeline, 0, 200)
	msg := make([]string, 0, 200)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 3 {
			log.Warnf("line part not equals 3, line: %s", line)
			msg = append(msg, fmt.Sprintf("%s: PipelineNotFound", line))
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]

		if cluster != "k8s1" {
			msg = append(msg, fmt.Sprintf("%s: ClusterOnlySupportK8s1", line))
			continue
		}

		srcPipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			msg = append(msg, fmt.Sprintf("%s: PipelineNotFound", line))
			continue
		}
		p, err := dto.ParsePipeline(srcPipe)
		if err != nil {
			msg = append(msg, fmt.Sprintf("%s: PipelineParseFail", line))
			continue
		}
		if pipeline_service.ExistInEnv("k8s0", namespace, app) {
			msg = append(msg, fmt.Sprintf("%s: PipelineExistInK8s0", line))
			continue
		}
		data = append(data, *p)
	}
	ret["message"] = strings.Join(msg, "\n")
	ret["data"] = data
	web.SuccessJson(c, ret)
}

func PipelineReplicaQuery(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := string(body)
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 200 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多200个发布流程")
		return
	}
	ret := make(map[string]interface{})
	data := make([]string, 0, 200)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 3 {
			log.Warnf("line part not equals 3, line: %s", line)
			data = append(data, fmt.Sprintf("%s: LineFormatError", line))
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]

		pipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			data = append(data, fmt.Sprintf("%s: PipelineNotFound", line))
			continue
		}

		data = append(data, fmt.Sprintf("%s/%s/%s/%d", pipe.Cluster, pipe.Namespace, pipe.App, pipe.Replicas))
	}
	ret["data"] = data
	web.SuccessJson(c, ret)
}

func PipelineReplicaUpdate(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := string(body)
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 200 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多200个发布流程")
		return
	}
	ret := make(map[string]interface{})
	data := make([]string, 0, 200)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 4 {
			log.Warnf("line part not equals 4, line: %s", line)
			data = append(data, fmt.Sprintf("%s: LineFormatError", line))
			continue
		}
		cluster := parts[0]
		namespace := parts[1]
		app := parts[2]
		replica, err := strconv.ParseInt(parts[3], 10, 64)
		if err != nil {
			data = append(data, fmt.Sprintf("%s: ReplicaParseError ", line))
			continue
		}

		pipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			data = append(data, fmt.Sprintf("%s: PipelineNotFound", line))
			continue
		}
		pipe.Replicas = uint(replica)
		err = pipeline_service.SaveOrUpdate(&pipe)
		if err != nil {
			data = append(data, fmt.Sprintf("%s: PipelineUpdateFail", line))
			continue
		}
		data = append(data, fmt.Sprintf("%s: Success", line))
	}
	ret["data"] = data
	web.SuccessJson(c, ret)
}

func SearchPipeline(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := strings.TrimSpace(string(body))
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 200 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多200个发布流程")
		return
	}

	appItems := make([]string, 0, 200)
	ret := make(map[string]interface{})
	pipes := make([]models.Pipeline, 0, 200)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 2 {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "数据格式有误")
			return
		}
		namespace := parts[0]
		app := parts[1]
		appItems = append(appItems, namespace+"@"+app)
	}

	k8s0Pipes, err := pipeline_service.FindByCluster("k8s0")
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	k8s1Pipes, err := pipeline_service.FindByCluster("k8s1")
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	for _, p := range k8s0Pipes {
		if !strslice.Find(appItems, p.Namespace+"@"+p.App) {
			continue
		}
		pipes = append(pipes, p)
	}
	for _, p := range k8s1Pipes {
		if !strslice.Find(appItems, p.Namespace+"@"+p.App) {
			continue
		}
		pipes = append(pipes, p)
	}
	sort.Slice(pipes, func(i, j int) bool {
		e1 := pipes[i]
		e2 := pipes[j]
		sortKey1 := e1.App + "|" + e1.Namespace + "|" + e1.Cluster
		sortKey2 := e2.App + "|" + e2.Namespace + "|" + e2.Cluster
		return strings.Compare(sortKey1, sortKey2) < 0
	})

	data, err := dto.ParsePipelineList(pipes)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	ret["data"] = data
	web.SuccessJson(c, ret)
}

func recoverPanic(c *gin.Context) {
	if r := recover(); r != nil {
		var msg string
		switch v := r.(type) {
		case string:
			msg = v
		case error:
			msg = v.Error()
		default:
			msg = "unknown panic message"
		}
		web.FailJson(c, web.CODE_SERVER_ERROR, msg)
		return
	}
}

func TrafficAnalysis(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := strings.TrimSpace(string(body))
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 200 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多200个发布流程")
		return
	}

	dayStr := c.DefaultQuery("day", "7")
	day, err := strconv.Atoi(dayStr)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "day is not a number")
		return
	}

	appItems := make([][]string, 0, 200)
	ret := make([]map[string]interface{}, 0, 200)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 2 {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "数据格式有误")
			return
		}
		namespace := parts[0]
		app := parts[1]
		appItems = append(appItems, []string{app, namespace})
	}

	startTime := time.Now().Add(-time.Hour * 24 * time.Duration(day))
	for _, item := range appItems {
		app := item[0]
		namespace := item[1]
		k8s0TomcatTraffic := "-"
		if count, err := clickhouse.TomcatTrafficCount("foneshare-k8s0", namespace, app, startTime); err != nil {
			k8s0TomcatTraffic = "error: " + err.Error()
		} else {
			k8s0TomcatTraffic = fmt.Sprintf("%d", count)
		}
		k8s1TomcatTraffic := "-"
		if count, err := clickhouse.TomcatTrafficCount("foneshare-k8s1", namespace, app, startTime); err != nil {
			k8s1TomcatTraffic = "error: " + err.Error()
		} else {
			k8s1TomcatTraffic = fmt.Sprintf("%d", count)

		}
		ret = append(ret, map[string]interface{}{
			"app":               app,
			"namespace":         namespace,
			"owner":             cmdb_service.GetMainOwnerNames(app, ","),
			"k8s0TomcatTraffic": k8s0TomcatTraffic,
			"k8s1TomcatTraffic": k8s1TomcatTraffic,
		})
	}
	web.SuccessJson(c, ret)
}

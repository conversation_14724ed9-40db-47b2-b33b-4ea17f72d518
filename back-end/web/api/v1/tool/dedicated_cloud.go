package tool

import (
	"bytes"
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"io"
	"net/http"
	"strings"
	"text/template"
)

const emailMessage = `比对差异如下:
应用|负责人
{{ range $i, $appStatusAndOwner := . }}
{{- range $app, $status := $appStatusAndOwner }}
{{ $app }}|{{ $status.Owner }}｜
{{- range $i, $cluster := $status.AppStatus }}{{ $cluster.Cluster }}/{{ $cluster.Namespace }}|{{ $cluster.Version }}|
{{- end }}
{{- end }}
{{- end  }}
`

func CheckVersionOfDedicatedCloudApp() {
	//data := make([]map[string]interface{}, 0, len(apps))
	setting := settings.GetSetting()
	// 存放app名称，去重
	apps := make(map[string]struct{}, 190)

	cluster := setting.CheckVersionOfDedicatedCloud

	// 查询要比对环境的所有app，并去重
	for _, c := range cluster {
		pipelines, err := pipeline_service.FindByClusterAndNamespace(c.Cluster, c.Namespace)
		if err != nil {
			log.Errorf("get pipelines err: %s", err)
			return
		}
		for _, p := range pipelines {
			if _, ok := apps[p.App]; !ok {
				apps[p.App] = struct{}{}
			}
		}
	}
	type appStatusAndOwner struct {
		AppStatus []k8s_service.AppStatus
		Owner     string
	}
	//
	appStatusList := make([]map[string]appStatusAndOwner, 3)

	for app := range apps {

		// 存放各个 namespace app status
		appStatusTmp := make(map[string]appStatusAndOwner)

		statusList := make([]k8s_service.AppStatus, 0)
		// 遍历 namespace，获取 app status
		for _, c := range cluster {
			status := k8s_service.GetAppStatus(c.Cluster, c.Namespace, app)
			if status == nil {
				continue
			}
			statusList = append(statusList, *status)
		}
		appStatusTmp[app] = appStatusAndOwner{
			AppStatus: statusList,
			Owner:     "@" + cmdb_service.GetMainOwnerNames(app, " @"),
		}
		// 比对各个 namespace app version
		var version string
		for _, status := range appStatusTmp[app].AppStatus {
			// version 为空，先赋值继续下一次循环
			if version == "" {
				version = status.Version
				continue
			}
			// version 结果不一致，添加到 appStatusList
			if status.Version != version {
				appStatusList = append(appStatusList, appStatusTmp)
				break
			}
		}
	}
	t, err := template.New("message template").Parse(emailMessage)
	if err != nil {
		log.Errorf("parse message template err: %s", err)
		return
	}
	var templateTemp bytes.Buffer
	if err := t.Execute(&templateTemp, appStatusList); err != nil {
		log.Errorf("execute template err: %s", err)
		return
	}
	if err := sendResult(templateTemp.String()); err != nil {
		log.Errorf("send email err: %s", err)
		return
	}
	return
}

func sendResult(ret string) error {

	client := &http.Client{}
	var data = strings.NewReader(fmt.Sprintf("tos=%s&subject=%s&content=%s",
		"<EMAIL>",
		"专属云应用运行版本比对结果",
		ret))
	req, err := http.NewRequest("POST", "http://172.17.0.35/phpmail/postmail.php", data)
	if err != nil {
		log.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	return nil
}

func CheckVersionOfDedicatedCloudAppApi(c *gin.Context) {
	web.FailJson(c, web.CODE_SERVER_ERROR, "接口已废弃")
	//CheckVersionOfDedicatedCloudApp()
	//web.SuccessJson(c, nil)
}

package harbor_tool

import (
	"errors"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/web/api/v1/tool/git"
	"strings"
)

func BuildRepository(gitUrl, module, parentPom string) string {
	items := make([]string, 0, 5)
	if parentPom != "" {
		items = append(items, parentPom)
	}
	items = append(items, strings.ToLower(git.GetProjectGroup(gitUrl)))
	items = append(items, strings.ToLower(git.GetProjectName(gitUrl)))
	if module != "" {
		items = append(items, strings.ToLower(module))
	}
	return strings.Join(items, "/")
}

func BuildArtifactFullName(gitUrl, module, parentPom, imageTag string) string {
	repo := BuildRepository(gitUrl, module, parentPom)
	imageName := config.Conf.Harbor.Host + "/" + config.Conf.Harbor.ArtifactProject + "/" + repo
	return imageName + ":" + imageTag
}

// ParseMetaFromArtifactFullName 从镜像中解析出host,project,repo,tag
func ParseMetaFromArtifactFullName(artifactFullName string) (host, project, repo, tag string, err error) {
	items := strings.Split(artifactFullName, "/")
	if len(items) < 3 {
		err = errors.New("invalid artifact full name: " + artifactFullName)
		return
	}
	lastItemParts := strings.Split(items[len(items)-1], ":")
	if len(lastItemParts) != 2 {
		err = errors.New("invalid artifact full name: " + artifactFullName)
		return
	}
	host = items[0]
	project = items[1]
	tag = lastItemParts[1]
	repo = strings.Split(strings.Join(items[2:], "/"), ":")[0]
	return
}

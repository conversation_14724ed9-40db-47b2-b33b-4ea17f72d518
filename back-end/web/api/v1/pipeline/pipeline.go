package pipeline

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/client/kubectl/pascli"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/numslice"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"io"
	errors2 "k8s.io/apimachinery/pkg/api/errors"
	"sort"
	"strconv"
	"strings"
	"time"
)

type SearchParams struct {
	param.PageSearch
	Cluster   string `form:"cluster"`
	Namespace string `form:"namespace"`
	App       string `form:"app"`
	JavaOpts  string `form:"javaOpts"`
	Image     string `form:"image"`
	Status    string `form:"status"`
}

type SyncParams struct {
	PipelineSrcId   uint     `form:"pipelineSrcId" binding:"required"`
	PipelineDestIds []uint   `form:"pipelineDestIds" binding:"required"`
	Columns         []string `form:"columns" binding:"required"`
}
type UpdateStatusRequest struct {
	ID     uint   `json:"id"`
	Status string `json:"status"`
}

func FindByApp(c *gin.Context) {
	app := c.Params.ByName("app")
	if !app_service.Exist(app) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "app not found, name: "+app)
		return
	}

	entities, err := pipeline_service.FindByApp(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	pipes, err := dto.ParsePipelineList(entities)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for idx, _ := range pipes {
		item := &pipes[idx]
		if cluster := settings.GetSetting().GetCluster(item.Cluster); cluster != nil {
			item.ExtraAttr.CloudCategory = string(cluster.CloudCategory)
			item.ExtraAttr.CloudCategoryDesc = cluster.CloudCategoryDesc()
			item.ExtraAttr.ClusterSummary = cluster.Description
			item.ExtraAttr.ClusterLabels = cluster.Labels
			if nsDesc := item.NamespaceDesc(); nsDesc != "" {
				item.ExtraAttr.ClusterSummary += "-" + nsDesc
			}
		}
	}

	if len(pipes) > 1 {
		sort.Slice(pipes, func(i, j int) bool {
			itemI := settings.GetSetting().GetEnvSortValue(pipes[i].Cluster, pipes[i].Namespace)
			itemJ := settings.GetSetting().GetEnvSortValue(pipes[j].Cluster, pipes[j].Namespace)
			return itemI < itemJ
		})
	}

	web.SuccessJson(c, pipes)
}
func FindByEnv(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	if cluster == "" || namespace == "" || app == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster、namespace、app is required")
		return
	}

	entity, err := pipeline_service.FirstInEnv(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	pipe, err := dto.ParsePipeline(entity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if cluster := settings.GetSetting().GetCluster(pipe.Cluster); cluster != nil {
		pipe.ExtraAttr.CloudCategory = string(cluster.CloudCategory)
		pipe.ExtraAttr.CloudCategoryDesc = cluster.CloudCategoryDesc()
		pipe.ExtraAttr.ClusterSummary = cluster.Description
		pipe.ExtraAttr.ClusterLabels = cluster.Labels
		if nsDesc := pipe.NamespaceDesc(); nsDesc != "" {
			pipe.ExtraAttr.ClusterSummary += "-" + nsDesc
		}
	}
	web.SuccessJson(c, pipe)
}

func FindByStatus(c *gin.Context) {
	status := c.Query("status")
	if status == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "status param miss")
		return
	}
	if !constant.PipelineStatusIsValid(status) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "status value is invalid")
		return
	}
	entities, err := pipeline_service.FindByStatus(status)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data, err := dto.ParsePipelineList(entities)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func UpdateStatus(c *gin.Context) {
	var req UpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "只有系统管理员才能执行当前操作")
		return
	}
	if req.ID == 0 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id is invalid")
		return
	}
	if !constant.PipelineStatusIsValid(req.Status) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "status is invalid")
		return
	}
	if err := pipeline_service.UpdateStatus(req.ID, req.Status); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func FindById(c *gin.Context) {
	var p param.Id
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	entity, err := pipeline_service.FindById(p.Id)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data, err := dto.ParsePipeline(entity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func SearchByProperties(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	lines := strings.TrimSpace(string(body))
	lineItems := strings.Split(lines, "\n")
	if strings.TrimSpace(lines) == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "内容不能为空")
		return
	}
	if len(lineItems) > 300 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "一次最多300个发布流程")
		return
	}

	pipes := make([]models.Pipeline, 0, 300)
	for _, line := range lineItems {
		line = strings.TrimSpace(line)
		parts := strings.Split(line, "/")
		if len(parts) != 3 {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "数据格式有误, value: "+line)
			return
		}
		pipe, err := pipeline_service.FirstInEnv(parts[0], parts[1], parts[2])
		if err != nil {
			log.Warnf("pipeline not found, cluster: %s, namespace: %s, app: %s", parts[0], parts[1], parts[2])
			continue
		}
		pipes = append(pipes, pipe)
	}
	data, err := dto.ParsePipelineList(pipes)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}
func Search(c *gin.Context) {
	var p SearchParams
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	count, entities, err := pipeline_service.Search2(p.Cluster, p.Namespace, p.App, p.Image, p.JavaOpts, p.Status, p.Page, p.Limit)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data := make(map[string]interface{})
	data["count"] = count
	if v, err := dto.ParsePipelineList(entities); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	} else {
		data["data"] = v
	}
	web.SuccessJson(c, data)
}

func All(c *gin.Context) {
	entities, err := pipeline_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data, err := dto.ParsePipelineList(entities)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func InitNew(c *gin.Context) {
	paramApp := c.Query("app")
	if paramApp == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "app param miss")
		return
	}
	app, err := app_service.FindByName(paramApp)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("app [%s] not found", paramApp))
		return
	}

	def := settings.GetSetting().PipelineDefault

	pipeline := dto.Pipeline{
		ID:                   0,
		App:                  app.Name,
		Cluster:              def.Cluster,
		Namespace:            def.Namespace,
		BaseImage:            def.BaseImage,
		AppModules:           make([]datatype.AppModule, 0, 0),
		Replicas:             def.Replicas,
		DeployStrategy:       def.DeployStrategy,
		Resources:            def.Resources,
		LivenessProbe:        def.LivenessProbe,
		ReadinessProbe:       def.ReadinessProbe,
		StartupProbe:         def.StartupProbe,
		Schedule:             def.Schedule,
		PVC:                  def.PVC,
		Envs:                 def.Envs,
		Ports:                def.Ports,
		PartnerApps:          def.PartnerApps,
		ExclusiveApps:        def.ExclusiveApps,
		PreStopWebhook:       def.PreStopWebhook,
		PreStopRetainSeconds: def.PreStopRetainSeconds,
		Options:              def.Options,
		EolinkerIDs:          def.EolinkerIDs,
		Remark:               def.Remark,
		ExtInitContainer:     def.ExtInitContainer,
	}
	//k8s1集群版本比较老，不支持startupProbe
	if pipeline.Cluster == "k8s1" {
		pipeline.StartupProbe.Enable = false
	}
	//如果启用了startupProbe，则缩短readinessProbe的初始延迟时间
	if pipeline.StartupProbe.Enable {
		if pipeline.ReadinessProbe.InitialDelaySeconds > 20 {
			pipeline.ReadinessProbe.InitialDelaySeconds = 20
		}
	}

	pipeline.ExclusiveApps = append(pipeline.ExclusiveApps, app.Name)
	web.SuccessJson(c, pipeline)
}

func validatePipeline(p models.Pipeline, user models.User) error {
	if _, err := app_service.FindByName(p.App); err != nil {
		return errors.New(fmt.Sprintf("找不到app: %s, msg: %s", p.App, err.Error()))
	}
	isAdmin := perm_service.IsAdmin(user)
	// 编辑流程且用户不是管理员判断资源是否变更了
	if p.ID > 0 && !isAdmin {
		oldPipeline, err := pipeline_service.FirstInEnv(p.Cluster, p.Namespace, p.App)
		if err != nil {
			return err
		}
		if p.Resources.LimitCPU > oldPipeline.Resources.LimitCPU ||
			p.Resources.RequestCPU > oldPipeline.Resources.RequestCPU ||
			p.Resources.LimitMemory > oldPipeline.Resources.LimitMemory ||
			p.Resources.RequestMemory > oldPipeline.Resources.RequestMemory ||
			p.Replicas > oldPipeline.Replicas {
			return errors.New("如果需要增加实例数、CPU、内存值，请发审批。审批模板：" +
				"http://wiki.firstshare.cn/pages/viewpage.action?pageId=176142178")
		}

	}
	if p.Namespace != "foneshare" && strings.Contains(p.Namespace, "foneshare") {
		for _, it := range p.Envs {
			if it.Name == "JAVA_OPTS" {
				if !strings.Contains(it.Value, "-Dprocess.profile.candidates=foneshare") {
					return errors.New("foneshare-xxx 环境必须要包含 -Dprocess.profile.candidates=foneshare")
				}
				if !isAdmin && strings.Contains(it.Value, "-XX:+HeapDumpBeforeFullGC") {
					return errors.New("JVM参数禁止包含 HeapDumpBeforeFullGC")
				}
				if !isAdmin && strings.Contains(it.Value, "-XX:HeapDumpPath") {
					return errors.New("JVM参数禁止包含 HeapDumpPath")
				}
			}
		}
	}

	if p.LivenessProbe.Enable {
		if p.LivenessProbe.InitialDelaySeconds < 1000 {
			return errors.New("存活检查(liveness)的初始延迟时间必须 ≥ 1000秒")
		}
	}
	if p.ReadinessProbe.Enable {
		if p.ReadinessProbe.InitialDelaySeconds > 600 {
			return errors.New("就绪检查(liveness)的初始延迟时间必须 ≤ 600秒")
		}
	}
	if p.StartupProbe.Enable {
		if p.StartupProbe.InitialDelaySeconds > 60 {
			return errors.New("启动检查(startup)的初始延迟时间必须 ≤ 60秒")
		}
	}
	if p.Replicas > 50 {
		return errors.New("实例副本数不能超过50")
	}
	if p.Resources.LimitCPU < p.Resources.RequestCPU {
		return errors.New("[请求CPU] 不能大于 [最大CPU]")
	}
	if p.Resources.LimitMemory < p.Resources.RequestMemory {
		return errors.New("[请求内存] 不能大于 [最大内存]")
	}
	if p.Resources.LimitCPU > 10 || p.Resources.RequestCPU > 10 {
		return errors.New("CPU资源不能超过10")
	}
	if p.Resources.LimitMemory > 30720 || p.Resources.RequestMemory > 30720 {
		return errors.New("内存资源不能超过30G")
	}
	//端口校验
	portNames := make([]string, 0)
	portValues := make([]int, 0)
	for _, it := range p.Ports {
		if it.Name == "" {
			return errors.New("有端口名未填写")
		}
		if it.Value == 0 {
			return errors.New("有端口值未填写")
		}
		// 8090 已被基础镜像下的 jvm-exporter agent使用
		if (it.Value == constant.JMX_AGENT_METRICS_PORT || it.Value == constant.SPRING_BOOT_ACTUATOR_PORT) && it.Type != constant.PORT_TYPE_SYSTEM {
			return errors.New(strconv.Itoa(int(it.Value)) + "端口为系统保留")
		}

		if strslice.Find(portNames, it.Name) {
			return errors.New(fmt.Sprintf("端口名出现重复：%s", it.Name))
		} else {
			portNames = append(portNames, it.Name)
		}
		if _, ok := numslice.FindInt(portValues, int(it.Value)); ok {
			return errors.New(fmt.Sprintf("端口值出现重复：%d", it.Value))
		} else {
			portValues = append(portValues, int(it.Value))
		}
	}
	for _, it := range p.Envs {
		if it.Name == "" {
			return errors.New("有环境变量名未填写")
		}
	}
	if _, ok := numslice.FindInt(portValues, 80); !ok {
		return errors.New("缺失系统需要的80端口")
	}

	//应用模块校验
	ctxValues := make([]string, 0, 0)
	if p.AppModules == nil || len(p.AppModules) == 0 {
		return errors.New("没有配置部署模块")
	}
	for _, it := range p.AppModules {
		if !strings.HasPrefix(it.ContextPath, "/") {
			return errors.New("部署模块的ContextPath必须以斜线开始")
		}
		if strings.Count(it.ContextPath, "/") > 1 {
			return errors.New("部署模块的ContextPath只能有一个斜线")
		}
		if strslice.Find(ctxValues, it.ContextPath) {
			return errors.New(fmt.Sprintf("部署模块ContextPath出现重复：%s", it.ContextPath))
		} else {
			ctxValues = append(ctxValues, it.ContextPath)
		}
	}

	for _, it := range p.PartnerApps {
		if strings.TrimSpace(it) == "" {
			return errors.New("伙伴应用名称不能为空")
		}
	}
	for _, it := range p.ExclusiveApps {
		if strings.TrimSpace(it) == "" {
			return errors.New("排斥应用名称不能为空")
		}
	}

	if p.PVC.Enable && (p.PVC.Name == "" || p.PVC.MountPath == "") {
		return errors.New("持久存储参数缺失")
	}

	clu := settings.GetSetting().GetCluster(p.Cluster)
	if clu == nil {
		return errors.New("找不到集群信息, name: " + p.Cluster)
	}

	if clu.Version < "1.16" && p.StartupProbe.Enable {
		return errors.New(fmt.Sprintf("当前集群版本号(%s)小于1.16， 不支持【启动检查】类型的健康检查", clu.Version))
	}

	if !clu.Apm.Enable && p.Options.SkyWalkingAgent {
		return errors.New("当前环境不支持开启性能监控（apm）")
	}

	if !strslice.Find(clu.BaseImages, p.BaseImage) {
		return errors.New("基础镜像不属于当前环境，请重新选择镜像")
	}

	return nil

}
func CreateOrUpdate(c *gin.Context) {
	var p dto.Pipeline
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "你没有应用权限")
		return
	}

	//部署模块排序，便于批量发布时的比较
	sort.Slice(p.AppModules, func(i, j int) bool {
		item1 := p.AppModules[i]
		item2 := p.AppModules[j]
		return item1.ToString() < item2.ToString()
	})

	entity, err := dto.ConvertPipeline(p)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	if err := apply(entity, user); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	// 更新 pas （自动扩缩容）的 最小副本数
	err = updatePASMinReplicas(entity.Cluster, entity.Namespace, entity.App, entity.Replicas)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, "发布流程修改成功，但自动扩缩容的最小副本数更新失败，请联系管理员, err: "+err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func updatePASMinReplicas(cluster, namespace, app string, minReplicaCount uint) error {
	pasExist, err := pascli.ExistPodAutoScaler(cluster, namespace, app)
	if err != nil {
		return err
	}
	if pasExist {
		_, err = pascli.UpdatePodAutoScalerMinReplicas(cluster, namespace, app, minReplicaCount)
		if err != nil {
			return err
		}
	}
	return nil
}

func apply(entity *models.Pipeline, user models.User) error {
	err := validatePipeline(*entity, user)
	if err != nil {
		return err
	}
	isEdit := entity.ID > 0
	//新建模式
	if isEdit {
		logOperate := "发布流程-编辑"
		dbEntity, _ := pipeline_service.FindById(entity.ID)
		if dbEntity.Status != constant.PIPELINE_STATUS_ENABLED && entity.Status == constant.PIPELINE_STATUS_ENABLED {
			logOperate = "发布流程-审核"
			if !perm_service.IsAdmin(user) {
				return errors.New("只有系统管理员才能执行当前操作")
			}
		}
		entity.UpdatedAt = time.Now()
		log_service.Create(user.RealName, logOperate, entity.App, entity)
	} else {
		if strings.Contains(entity.Namespace, "foneshare") && entity.Cluster == "k8s1" {
			return errors.New("foneshare环境不允许在k8s1集群新增发布流程")
		}
		if exist := pipeline_service.ExistInEnv(entity.Cluster, entity.Namespace, entity.App); exist {
			return fmt.Errorf("当前环境下已经存在发布流程：%s (%s)", entity.Namespace, entity.Cluster)
		}
		// 如果状态为空，默认是待审核状态
		if entity.Status == "" {
			entity.Status = constant.PIPELINE_STATUS_AUDIT
		}
		entity.Author = user.RealName
		log_service.Create(user.RealName, "发布流程-创建", entity.App, entity)
	}

	//场景：为了改造容器使用非root账号运行，tomcat http端口从80调整到8080
	//规则： 如果试用8080端口的镜像，则给发布流程添加一个8080端口
	if strings.HasSuffix(entity.BaseImage, "-port8080") {
		exist8080 := false
		for _, it := range entity.Ports {
			if it.Value == 8080 {
				exist8080 = true
				break
			}
		}
		if !exist8080 {
			entity.Ports = append(entity.Ports, datatype.Port{
				Name:  "tomcat",
				Value: 8080,
				Type:  constant.PORT_TYPE_SYSTEM,
			})
		}
	}
	//系统端口排在前面
	sort.Slice(entity.Ports, func(i, j int) bool {
		pi := entity.Ports[i]
		pj := entity.Ports[j]
		if pi.Type == constant.PORT_TYPE_SYSTEM && pj.Type == constant.PORT_TYPE_USER {
			return true
		} else if pi.Type == constant.PORT_TYPE_USER && pj.Type == constant.PORT_TYPE_SYSTEM {
			return false
		}
		return true
	})
	//todo: 同步修改自动扩缩容的最小副本数
	return pipeline_service.SaveOrUpdate(entity)
}

func Sync(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}

	var p SyncParams
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	srcPipe, err := pipeline_service.FindById(p.PipelineSrcId)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	destPipes, err := pipeline_service.FindByIds(p.PipelineDestIds...)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	log_service.Create(user.RealName, "发布流程-批量同步配置", srcPipe.App, p)

	for _, destPipe := range destPipes {
		if strslice.Find(p.Columns, "选项开关") {
			destPipe.Options = srcPipe.Options
		}
		if strslice.Find(p.Columns, "部署模块") {
			destPipe.AppModules = srcPipe.AppModules
		}
		if strslice.Find(p.Columns, "CPU") {
			destPipe.Resources.RequestCPU = srcPipe.Resources.RequestCPU
			destPipe.Resources.LimitCPU = srcPipe.Resources.LimitCPU
		}
		if strslice.Find(p.Columns, "内存") {
			destPipe.Resources.RequestMemory = srcPipe.Resources.RequestMemory
			destPipe.Resources.LimitMemory = srcPipe.Resources.LimitMemory
		}
		if strslice.Find(p.Columns, "副本数") {
			destPipe.Replicas = srcPipe.Replicas
		}
		if strslice.Find(p.Columns, "流程状态") {
			destPipe.Status = srcPipe.Status
		}
		if strslice.Find(p.Columns, "基础镜像") {
			destPipe.BaseImage = srcPipe.BaseImage
		}
		if strslice.Find(p.Columns, "Webhook") {
			destPipe.Webhook = srcPipe.Webhook
		}
		if strslice.Find(p.Columns, "端口") {
			destPipe.Ports = srcPipe.Ports
		}
		err := pipeline_service.SaveOrUpdate(&destPipe)
		if err != nil {
			log.Errorf("sync pipeline fail, err: %v", err)
		}
	}
	web.SuccessJson(c, nil)
}
func Offline(c *gin.Context) {
	var (
		id  int
		err error
	)
	if id, err = strconv.Atoi(c.Query("id")); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	pipelineInfo, err := pipeline_service.FindById(uint(id))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}
	log_service.Create(auth.GetRealName(c), "发布流程-下线", pipelineInfo.App, pipelineInfo)

	dep, err := k8s_service.DeploymentDetail(pipelineInfo.Cluster, pipelineInfo.Namespace, pipelineInfo.App)
	if err != nil && !errors2.IsNotFound(err) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "Deployment获取失败")
		return
	}
	if dep != nil && dep.Spec.Replicas != nil && *dep.Spec.Replicas != 0 {
		web.FailJson(c, web.CODE_SERVER_ERROR, "请先把服务的运行副本缩容为0，再执行下线操作")
		return
	}

	if err := pipeline_service.DeleteById(uint(id)); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	web.SuccessJson(c, nil)
}

func GetClonePipeline(c *gin.Context) {
	var (
		p dto.GetClonePipelineReq
	)
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	sourcePipelines, err := pipeline_service.FindByClusterAndNamespace(p.SourceCluster, p.SourceNamespace)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	targetPipelines, err := pipeline_service.FindByClusterAndNamespace(p.TargetCluster, p.TargetNamespace)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	// 比对源namespace和目标namespace,获取源集群和目标集群之差
	pipelines := make([]models.Pipeline, 0)
	existPipelines := make(map[string]struct{})
	for _, p := range targetPipelines {
		existPipelines[p.App] = struct{}{}
	}
	for _, p := range sourcePipelines {
		if _, ok := existPipelines[p.App]; !ok {
			pipelines = append(pipelines, p)
		}
	}

	type pipelineInfo struct {
		ID     uint   `json:"id"`
		App    string `json:"app"`
		Status string `json:"status"`
	}
	var resp []pipelineInfo
	for _, v := range pipelines {
		resp = append(resp, pipelineInfo{
			App:    v.App,
			ID:     v.ID,
			Status: v.Status,
		})
	}

	web.SuccessJson(c, resp)
	return
}

func ClonePipelineByNamespace(c *gin.Context) {
	var (
		p dto.ClonePipelineReq
	)
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有系统管理员才能执行当前操作")
		return
	}

	pipeline, err := pipeline_service.FindByIds(p.IDs...)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	ret := make([]string, 0, len(pipeline))
	for _, v := range pipeline {
		// 差异信息变更
		v.ID = 0
		v.CreatedAt = time.Time{}
		v.UpdatedAt = time.Time{}
		v.Cluster = p.TargetCluster
		v.Namespace = p.TargetNamespace
		for idx, it := range v.Envs {
			switch it.Name {
			case "CATALINA_OPTS":
				processName := settings.GetSetting().GetProcessName(p.TargetNamespace, v.App)
				v.Envs[idx].Value = fmt.Sprintf("-Dprocess.profile=%s -Dspring.profiles.active=%s -Dprocess.name=%s -Dapp.name=%s",
					p.TargetNamespace, p.TargetNamespace, processName, v.App)
			case "K8S_PROCESS_NAME":
				v.Envs[idx].Value = settings.GetSetting().GetProcessName(p.TargetNamespace, v.App)
			}
		}
		if err := apply(&v, user); err != nil {
			if strings.Contains(err.Error(), "当前环境下已经存在发布流程") {
				log.Info(err.Error())
				ret = append(ret, fmt.Sprintf("%s/%s: 发布流程已存在", v.Cluster, v.Namespace))
				continue
			}
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		ret = append(ret, fmt.Sprintf("%s/%s: Success", v.Cluster, v.Namespace))
	}

	web.SuccessJson(c, ret)
}

func GetDedicatedCloudPublishPipeline(c *gin.Context) {
	web.FailJson(c, web.CODE_SERVER_ERROR, "功能已弃用，详情请联系 吴志辉 ")
	//var (
	//	req dto.GetDedicatedCloudPipelineReq
	//)
	//if settings.GetSetting().Maintain.Open {
	//	web.FailJson(c, web.CODE_SERVER_ERROR, settings.GetSetting().Maintain.Desc)
	//}
	//
	//if err := c.ShouldBind(&req); err != nil {
	//	web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
	//	return
	//}
	//entities, err := pipeline_service.FindByClusterAndNamespace(req.SourceCluster, req.SourceNamespace)
	//if err != nil {
	//	web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
	//	return
	//}
	//data := make([]dto.DedicatedCloudPipeline, 0)
	//for _, item := range entities {
	//	var dp dto.DedicatedCloudPipeline
	//	// 转为dto.pipelne
	//	p, err := dto.ParsePipeline(item)
	//	if err != nil {
	//		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
	//		return
	//	}
	//	// 获取目标pipeline id
	//	if targetPipe, err := pipeline_service.FirstInEnv(req.TargetCluster, req.TargetNamespace, p.App); err == nil {
	//		dp.TargetPipeline.ID = targetPipe.ID
	//	}
	//	// 添加extra attr
	//	if cluster := settings.GetSetting().GetCluster(p.Cluster); cluster != nil {
	//		p.ExtraAttr.DedicatedCloud = cluster.IsDedicatedCloud()
	//		p.ExtraAttr.ClusterSummary = cluster.Description
	//		if nsDesc := p.NamespaceDesc(); nsDesc != "" {
	//			p.ExtraAttr.ClusterSummary += "-" + nsDesc
	//		}
	//	}
	//	if dep, err := k8s_service.GetDeploymentDTO(p.Cluster, p.Namespace, p.App); err == nil {
	//		p.AddExtraAttr(dep)
	//	}
	//	dp.SourcePipeline = *p
	//	// 添加目标extra attr
	//	if dep, err := k8s_service.GetDeploymentDTO(req.TargetCluster, req.TargetNamespace, p.App); err == nil {
	//		dp.TargetPipeline.ExtraAttr.AddExtraAttr(dep)
	//	}
	//	data = append(data, dp)
	//}
	//web.SuccessJson(c, data)
	//return
}

func ReviewResource(c *gin.Context) {

}

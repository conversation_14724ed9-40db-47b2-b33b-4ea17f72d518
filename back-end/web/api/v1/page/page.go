package page

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	k8s_cli "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
	"net"
	"net/http"
	netUrl "net/url"
	"strings"
	"time"
)

func Redirect(c *gin.Context) {
	var url string
	var err error
	switch c.Query("type") {
	case "jenkins":
		url, err = jenkins(c)
	case "grafana":
		url, err = grafana(c)
	case "clickhouse":
		url, err = clickhouse(c)
	case "webShell":
		url, err = webShell(c)
	case "webShellWithFirstPod":
		url, err = webShellWithFirstPod(c)
	case "owner":
		url, err = owner(c)
	case "cmsEdit":
		url, err = cmsEdit(c)
	case "cmsBatchEdit":
		url, err = cmsBatchEdit(c)
	case "eolinker":
		url, err = eolinker(c)
	default:
		err = errors.New("parent[type] value is invalid")
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
	} else {
		log.Infof("page redirect, type: %s, url: %s", c.Query("type"), url)
		c.Redirect(http.StatusFound, url)
	}
}

func jenkins(c *gin.Context) (string, error) {
	buildId := c.Query("buildId")
	job := c.Query("job")
	if strslice.Find([]string{buildId, job}, "") {
		return "", errors.New("缺失参数")
	}
	conf := config.Conf.Jenkins
	url := fmt.Sprintf("%s/blue/organizations/jenkins/%s/detail/%s/%s/pipeline",
		conf.Host, job, job, buildId)
	return url, nil
}

func grafana(c *gin.Context) (string, error) {
	cluster := c.Query("cluster")
	app := c.Query("app")
	namespace := c.Query("namespace")
	pod := c.Query("pod")
	theme := c.Query("theme")
	kiosk := c.Query("kiosk")
	dashboard := c.DefaultQuery("dashboard", "jvm-monitor")
	if strslice.Find([]string{app, namespace}, "") {
		return "", errors.New("缺失参数")
	}
	conf := settings.GetSetting().GetCluster(cluster).ThirdServices
	if conf.Grafana.Host == "" {
		return "", errors.New("当前环境暂未部署Grafana监控平台")
	}
	url := ""
	if dashboard == "jvm-monitor" {
		url = fmt.Sprintf("%s/d/pod-jvm-monitor/pod-jvm-monitor?var-app=%s&var-namespace=%s&var-pod=%s&var-datasource=%s",
			conf.Grafana.Host, app, namespace, pod, conf.Grafana.PrometheusDS)
	} else if dashboard == "pod-auto-scaler" {
		url = fmt.Sprintf("%s/d/fs_k8s_app_scaler?var-app=%s&var-namespace=%s&var-cluster=%s&var-datasource=%s",
			conf.Grafana.Host, app, namespace, cluster, conf.Grafana.ClickHouseDS)
	} else if dashboard == "pyroscope" {
		url = fmt.Sprintf("%s/d/ad1b2b2a-8611-4211-b38c-6a02dfaecc1d?var-env=foneshare&var-pod=%s",
			conf.Grafana.Host, pod)
	}
	if theme != "" {
		url += "&theme=" + theme
	}
	if kiosk != "" {
		url += "&kiosk=" + kiosk
	}
	return url, nil
}

func getClickVisualTableId(clickvisualHost, instance, database, table, datasource string) (int, error) {
	url := fmt.Sprintf("%s/api/v1/table/id?instance=%s&database=%s&table=%s&datasource=%s", clickvisualHost, instance, database, table, datasource)
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	resp, err := client.R().Get(url)
	if err != nil {
		return -1, errors.New(fmt.Sprintf("获取ClickVisual表ID失败, error: %s", err.Error()))
	}
	if resp.StatusCode() != 200 {
		return -1, errors.New(fmt.Sprintf("获取ClickVisual表ID失败, status code: %d, body: %s", resp.StatusCode(), string(resp.Body())))
	}

	var result map[string]interface{}
	// 解析 JSON 字符串
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		fmt.Println("Error decoding JSON:", err)
		return -1, err
	}

	// 提取 "data" 字段的值。 返回参考格式；{"code":0,"msg":"succ","data":236}
	if data, ok := result["data"].(float64); ok {
		return int(data), nil
	} else {
		return -1, errors.New("获取ClickVisual表ID失败,response: " + string(resp.Body()))
	}
}
func clickhouse(c *gin.Context) (string, error) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	pod := c.Query("pod")
	pageType := c.DefaultQuery("pageType", "query")
	if strslice.Find([]string{cluster, app, namespace}, "") {
		return "", errors.New("缺失参数")
	}
	logName := c.DefaultQuery("logName", "")

	clu := settings.GetSetting().GetCluster(cluster)
	if clu == nil {
		return "", errors.New("找不到集群配置")
	}
	//线上分享云的clickhouse日志做了拆分，这里进行特殊处理。后续考虑优化
	clickvisualInstance := "cluster01"
	if clu.IsFxiaokeCloud() && strslice.Find(clu.Namespaces, "foneshare") {
		clickvisualInstance = "cluster00"
		if logName == "app_log" {
			logName = "app_log_dist"
		} else if logName == "tomcat_access" {
			logName = "tomcat_access_dist"
		}
	}

	conf := settings.GetSetting().GetCluster(cluster).ThirdServices
	if conf.ClickVisual.Host == "" {
		return "", errors.New("当前环境暂未部署ClickHouse日志平台")
	}
	var tableId int
	tableId, err := getClickVisualTableId(conf.ClickVisual.Host, clickvisualInstance, "logger", logName, "ch")
	if err != nil {
		return "", err
	}
	if tableId < 1 {
		return "", errors.New("找不到ClickVisual表ID")
	}

	end := time.Now().Unix()
	start := end - 3600
	namespaceKey := "namespace"
	if strings.Contains(logName, "app_log") || strings.Contains(logName, "tomcat_access") {
		namespaceKey = "profile"
	}

	kw := netUrl.QueryEscape(fmt.Sprintf("app='%s' and %s='%s'", app, namespaceKey, namespace))
	if pod != "" {
		kw = netUrl.QueryEscape(fmt.Sprintf("app='%s' and %s='%s' and pod = '%s'", app, namespaceKey, namespace, pod))
	}
	url := fmt.Sprintf("%s/%s?tid=%d&index=4&tab=relative&start=%d&end=%d&page=1&queryType=rawLog&size=10&kw=%s",
		conf.ClickVisual.Host, pageType, tableId, start, end, kw)
	return url, nil
}

func owner(c *gin.Context) (string, error) {
	app := c.Query("app")
	if app == "" {
		return "", errors.New("缺失参数")
	}
	configPaths := strings.Split(config.Conf.CMS.CmdbConfigPath, "/")
	url := fmt.Sprintf("%s/?search=%s", config.Conf.CMS.WebHost, configPaths[1])
	go func() {
		time.Sleep(60 * time.Second)
		cmdb_service.ClearServiceOwnersCache()
	}()
	return url, nil
}

func cmsEdit(c *gin.Context) (string, error) {
	id := c.Query("cmsId")
	if id == "" {
		return "", errors.New("缺失参数")
	}
	url := fmt.Sprintf("%s/edit/config/%s", config.Conf.CMS.WebHost, id)
	return url, nil
}

func cmsBatchEdit(c *gin.Context) (string, error) {
	profile := c.Query("profile")
	oldContent := c.Query("oldContent")
	newContent := c.Query("newContent")
	if strslice.Find([]string{profile, oldContent, newContent}, "") {
		return "", errors.New("缺失参数")
	}
	url := fmt.Sprintf("%s/replace/preview/?profile=%s&src=%s&dst=%s", config.Conf.CMS.WebHost, profile, oldContent, newContent)
	return url, nil
}
func webShell(c *gin.Context) (string, error) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	pods := c.Query("pods")
	if strslice.Find([]string{cluster, namespace, pods}, "") {
		return "", errors.New("缺失参数")
	}
	conf := settings.GetSetting().ThirdServices
	items := make([]map[string]string, 0, 5)
	for _, it := range strings.Split(pods, ",") {
		pod := it
		if net.ParseIP(it) != nil {
			if v, err := k8s_service.GetPodByIP(cluster, namespace, it); err != nil {
				return "", err
			} else {
				pod = v.Name
			}
		}
		item := make(map[string]string)
		item["cluster"] = cluster
		item["namespace"] = namespace
		item["pod"] = pod
		item["container"] = k8s_util.GetAppName(pod)
		items = append(items, item)
	}
	param, _ := json.Marshal(items)
	url := fmt.Sprintf("%s/?param=%s",
		conf.WebShellHost, netUrl.QueryEscape(string(param)))
	return url, nil
}

func webShellWithFirstPod(c *gin.Context) (string, error) {
	webShellParams := c.Query("webShellParams")
	if webShellParams == "" {
		return "", errors.New("缺失参数")
	}

	type shellItem struct {
		Cluster   string `json:"cluster"`
		Namespace string `json:"namespace"`
		App       string `json:"app"`
	}
	var shellParams []shellItem
	err := json.Unmarshal([]byte(webShellParams), &shellParams)
	if err != nil {
		return "", errors.New("webShell参数解析失败, value: " + webShellParams + ", error: " + err.Error())
	}

	items := make([]map[string]string, 0, 30)
	for _, it := range shellParams {
		podList, err := k8s_cli.GetPodList(it.Cluster, it.Namespace, it.App)
		if err != nil {
			return "", err
		}
		if len(podList.Items) < 1 {
			return "", errors.New(fmt.Sprintf("%s/%s/%s 环境里找不到任何Pod", it.Cluster, it.Namespace, it.App))
		}
		item := make(map[string]string)
		item["cluster"] = it.Cluster
		item["envDesc"] = settings.GetSetting().GetCluster(it.Cluster).Description
		item["namespace"] = it.Namespace
		item["pod"] = podList.Items[0].Name
		item["container"] = k8s_util.GetAppName(item["pod"])
		items = append(items, item)
	}

	conf := settings.GetSetting().ThirdServices
	param, _ := json.Marshal(items)
	url := fmt.Sprintf("%s/?param=%s",
		conf.WebShellHost, netUrl.QueryEscape(string(param)))
	return url, nil
}

func eolinker(c *gin.Context) (string, error) {
	projectID := c.Query("projectID")
	if strslice.Find([]string{projectID}, "") {
		return "", errors.New("缺失参数")
	}
	conf := config.Conf.Eolinker
	url := fmt.Sprintf("%s/independent/#/home/<USER>/inside/report/timed_task?projectHashKey=%s&spaceKey=Ns7aIIHd8ab5307649b503da006ccd3a1ec58709b7090a2",
		conf.Host, projectID)
	return url, nil
}

package openapi

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/client/jenkins"
	k8s_cli "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kubectl"
	file2 "fs-k8s-app-manager/pkg/util/file"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/k8s"
	"github.com/gin-gonic/gin"
	"path/filepath"
	"strings"
	"time"
)

type ReportTaskParam struct {
	k8s.PodParam
	Jars []string `form:"jars"`
}
type DumpDownloadParam struct {
	k8s.PodParam
	Jars string `form:"jars"`
}

// ResetCoverageParam
// @Description 重置覆盖率参数结构体
type ResetCoverageParam struct {
	// k8s集群名
	Cluster string `form:"cluster" binding:"required" json:"cluster" example:"k8s0"`
	// k8s命名空间 （运行环境）
	Namespace string `form:"namespace" binding:"required" json:"namespace" example:"jacoco"`
	// 发布系统上的应用名
	App string `form:"app" binding:"required" example:"fs-crm-sfa"`
}

// DownloadCoverageParam
// @Description 覆盖率下载参数结构体
type DownloadCoverageParam struct {
	// k8s集群名
	Cluster string `form:"cluster" binding:"required" json:"cluster" example:"k8s0"`
	// k8s命名空间 （运行环境）
	Namespace string `form:"namespace" binding:"required" json:"namespace" example:"jacoco"`
	// 发布系统上的应用名
	App string `form:"app" binding:"required" json:"app" example:"fs-crm-sfa"`
	// 应用的部署模块， 格式为：git地址@模块名
	AppModule string `form:"appModule" binding:"required" json:"appModule" example:"https://git.firstshare.cn/sfa/fs-crm-sfa.git@fs-crm-web"`
	// 需要下载的jar包
	Jars []string `form:"jars[]" json:"jars" example:"fs-metadata-common.jar,fs-metadata-core.jar"`
}

func PodName(c *gin.Context) {
	cluster := c.Query("cluster")
	app := c.Query("app")
	namespace := "jacoco"
	if cluster == "" || app == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少查询参数: cluster, app")
		return
	}
	pods, err := k8s_service.ListPod(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pods) > 1 {
		web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("jacoco环境下存在多个pod, cluster: %s, namespace: %s, app: %s", cluster, namespace, app))
		return
	} else if len(pods) < 1 {
		web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("jacoco环境没有任何pod, cluster: %s, namespace: %s, app: %s", cluster, namespace, app))
		return
	}
	web.SuccessJson(c, pods[0].Name)
}

// ResetCoverage godoc
// @Summary 重置覆盖率
// @Tags jacoco
// @Accept json
// @Produce json
// @Param RequestBody	body	ResetCoverageParam	true "json格式的请求体"
// @Success     200 {object}    web.Response "操作成功"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /jacoco/coverage/reset [post]
func ResetCoverage(c *gin.Context) {
	var p ResetCoverageParam
	var response web.Response

	defer func(params *ResetCoverageParam, response *web.Response) {
		target := fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.App)
		log_service.Create("api", "jacoco-coverage-reset", target, map[string]interface{}{
			"params":   *params,
			"response": *response,
		})
	}(&p, &response)

	if err := c.ShouldBindJSON(&p); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if p.Namespace != "jacoco" {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, "k8sNamespace必须是jacoco")
		return
	}
	pods, err := k8s_service.ListPod(p.Cluster, p.Namespace, p.App)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pods) > 1 {
		response = web.FailJson2(c, web.CODE_SERVER_ERROR, fmt.Sprintf("jacoco环境下存在多个pod, cluster: %s, namespace: %s, app: %s", p.Cluster, p.Namespace, p.App))
		return
	} else if len(pods) < 1 {
		response = web.FailJson2(c, web.CODE_SERVER_ERROR, fmt.Sprintf("jacoco环境没有任何pod, cluster: %s, namespace: %s, app: %s", p.Cluster, p.Namespace, p.App))
		return
	}
	pod := pods[0]

	if output, err := kubectl.JacocoCoverageReset(p.Cluster, p.Namespace, pod.Name); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error()+",command output: "+output)
		return
	}
	response = web.SuccessJson2(c, nil)
}

// DownloadCoverage godoc
// @Summary 生成覆盖率文件并下载，此接口耗时较长，建议调用方配置超时时间为2分钟以上
// @Description 下载的文件名为：{podName}-jacoco.zip， 解压后的文件结构为：
// @Description <div>--------------------</div>
// @Description <pre>
// @Description classes              (存放class文件里目录，包含了部署模块的class文件）
// @Description jacoco.exec         （jacoco覆盖率文件）
// @Description commitID.txt        （部署模块运行的CommitID）
// @Description gitRef.txt          （部署模块运行的分支或者tag名）
// @Description jars                （存放jar包的目录，里面存放请求参数里指定的jar包）
// @Description └─jar001-1.0.0.jar  （示例的jar文件）
// @Description └─jar002-1.1.0.jar  （示例的jar文件）
// @Description </pre>
// @Description <div>--------------------</div>
// @Tags jacoco
// @Accept json
// @Produce octet-stream
// @Param RequestBody	body		DownloadCoverageParam	true "json格式的请求体"
// @Success 200 {file} file "下载的文件"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /jacoco/coverage/download [post]
func DownloadCoverage(c *gin.Context) {
	var p DownloadCoverageParam
	var response web.Response

	defer func(params *DownloadCoverageParam, response *web.Response) {
		target := fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.App)
		log_service.Create("api", "jacoco-coverage-download", target, map[string]interface{}{
			"params":   *params,
			"response": *response,
		})
	}(&p, &response)

	if err := c.ShouldBindJSON(&p); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if p.Namespace != "jacoco" {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, "k8sNamespace必须是jacoco")
		return
	}

	pods, err := k8s_cli.GetPodList(p.Cluster, p.Namespace, p.App)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pods.Items) > 1 {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, fmt.Sprintf("jacoco环境下存在多个pod, cluster: %s, namespace: %s, app: %s", p.Cluster, p.Namespace, p.App))
		return
	} else if len(pods.Items) < 1 {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, fmt.Sprintf("jacoco环境没有任何pod, cluster: %s, namespace: %s, app: %s", p.Cluster, p.Namespace, p.App))
		return
	}

	pod := pods.Items[0]
	deployModules, err := k8s_service.GetDeployModules(pod)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, "获取部署模块信息失败, err: "+err.Error())
		return
	}
	webapp := ""
	commitID := ""
	gitRef := ""
	for _, it := range deployModules {
		if strings.ReplaceAll(p.AppModule, "https:", "http:") == strings.ReplaceAll(fmt.Sprintf("%s@%s", it.GitUrl, it.Module), "https:", "http:") {
			webapp = strings.ReplaceAll(it.ContextPath, "/", "")
			if webapp == "" {
				webapp = "ROOT"
			}
			commitID = it.CommitID
			gitRef = it.Tag
		}
	}
	if webapp == "" {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, "未找到对应的部署模块")
		return
	}
	if commitID == "" {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, "未找到对应的commitID，请重新部署应用")
		return
	}
	if gitRef == "" {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, "未找到对应的gitRef")
		return
	}
	jars := "''"
	if len(p.Jars) > 0 {
		jars = strings.Join(p.Jars, ",")
	}
	outputFilePath := "/tmp/jacoco-coverage-" + time.Now().Format("20060102150405") + ".zip"

	if output, err := kubectl.JacocoCoverageBuild(p.Cluster, p.Namespace, pod.Name, webapp, jars, commitID, gitRef, outputFilePath); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error()+",command output: "+output)
		return
	}
	destFile := file2.AbsPath(filepath.Join(config.Conf.App.DownloadDir, "jacoco", pod.Name, filepath.Base(outputFilePath)))
	err = k8s_service.CopyFileFromPod(p.Cluster, p.Namespace, pod.Name, outputFilePath, destFile)
	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	c.FileAttachment(destFile, pod.Name+"-jacoco.zip")
	response = web.Response{
		Code:    200,
		Message: "success",
		Data:    nil,
	}
}
func Reset(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if err := kubectl.JacocoReset(p.Cluster, p.Namespace, p.Pod); err != nil {
		c.AbortWithStatusJSON(web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.CreateBySys(fmt.Sprintf("jacoco-reset"), p.Pod, p)
	web.SuccessJson(c, nil)
}

// Deprecated: 已废弃，等所有测试都迁移到了新的覆盖率平台后可下线
func ReportTask(c *gin.Context) {
	var p ReportTaskParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	// 获取pod信息
	pod, err := k8s_service.PodDetail(p.Cluster, p.Namespace, p.Pod)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	deployModules, err := k8s_service.GetDeployModules(pod)
	if deployModules == nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, "获取部署模块信息失败")
		return
	}
	if len(deployModules) != 1 {
		web.FailJson(c, web.CODE_SERVER_ERROR, "Jacoco测试当前只支持单模块部署的应用")
		return
	}

	jc, err := jenkins.CreateJenkinsClientDefault(config.Conf.Jenkins.JobJacoco)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	params := make(map[string]string)
	params["cluster"] = p.Cluster
	params["namespace"] = p.Namespace
	params["pod"] = p.Pod
	params["gitUrl"] = deployModules[0].GitUrl
	params["gitModule"] = deployModules[0].Module
	params["gitTag"] = deployModules[0].Tag
	if len(p.Jars) > 0 {
		params["jars"] = strings.Join(p.Jars, ",")
	} else {
		params["jars"] = ""
	}
	params["publishServer"] = "https://k8s-app.firstshare.cn"
	if strings.Contains(config.Conf.Jenkins.Host, "foneshare") {
		params["publishServer"] = "https://k8s-app.foneshare.cn"
	}

	queueId, err := jc.BuildWithParameters(params)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	buildId, err := jc.GetBuildIdByQueueItemId(queueId, 120)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	jobInfo, err := jc.GetBuild(buildId)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.CreateBySys(fmt.Sprintf("jacoco-report"), p.Pod, p)

	web.SuccessJson(c, jobInfo.Raw.URL)
}

// Deprecated: 已废弃，等所有测试都迁移到了新的覆盖率平台后可下线
func DumpDownload(c *gin.Context) {
	var p DumpDownloadParam
	if err := c.ShouldBindQuery(&p); err != nil {
		c.AbortWithStatusJSON(web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if err := kubectl.JacocoDump(p.Cluster, p.Namespace, p.Pod, p.Jars); err != nil {
		c.AbortWithStatusJSON(web.CODE_SERVER_ERROR, err.Error())
		return
	}
	fileName := fmt.Sprintf("%s--exec-and-class.tar.gz", p.Pod)
	podFilePath := "/opt/tomcat/jacoco-agent/exec-and-class.tar.gz"
	destFile := file2.AbsPath(filepath.Join(config.Conf.App.DownloadDir, "jacoco", fileName))
	err := k8s_service.CopyFileFromPod(p.Cluster, p.Namespace, p.Pod, podFilePath, destFile)
	if err != nil {
		c.AbortWithStatusJSON(web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.CreateBySys(fmt.Sprintf("jacoco-dump-download"), p.Pod, p)

	c.FileAttachment(destFile, fileName)
}

// Deprecated: 已废弃，等所有测试都迁移到了新的覆盖率平台后可下线
func ListJars(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	// todo: 考虑支持springboot jar应用
	isSpringBootJar := false
	jars, err := kubectl.ListJarFile(p.Cluster, p.Namespace, p.Pod, isSpringBootJar)
	//data := make([]string, 0, len(jars))
	//for _, it := range jars {
	//	if strings.HasPrefix(it, "fs-") || strings.Contains(it, "-support-") {
	//		data = append(data, it)
	//	}
	//}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, jars)
}

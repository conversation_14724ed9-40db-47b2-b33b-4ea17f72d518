package openapi

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	k8s_cli "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/encrypt"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// AppModuleVersionResponseData
// @Description 服务版本信息查询返回结构体
type AppModuleVersionResponseData struct {
	// k8s集群名
	Cluster string `form:"cluster" json:"cluster" example:"k8s1"`
	// k8s命名空间 （运行环境）
	Namespace string `form:"namespace" json:"namespace" example:"fstest"`
	// 发布系统上的应用名
	App string `form:"app" json:"app" example:"fs-k8s-tomcat-test"`
	// 代码仓库地址
	GitUrl string `form:"gitUrl" json:"gitUrl" example:"https://git.firstshare.cn/devops/fs-k8s-tomcat-test.git"`
	// 代码仓库子目录
	GitSubDir string `form:"gitSubDir" json:"gitSubDir" example:"fs-k8s-tomcat-test-biz"`
	// 代码提交ID
	GitCommitId string `form:"gitCommitId" json:"gitCommitId" example:"51e1001334abc0257a340e5791fbf578b06bb627"`
	// 代码分支或标签
	GitRef string `form:"gitRef" json:"gitRef" example:"master"`
}

type AppRestartParam struct {
	Cluster   string `form:"cluster" json:"cluster" example:"k8s1" binding:"required"`
	Namespace string `form:"namespace" json:"namespace" example:"fstest" binding:"required"`
	App       string `form:"app" json:"app" example:"fs-k8s-tomcat-test" binding:"required"`
	Token     string `form:"token" json:"token" example:"1234567890" binding:"required"`
}

// AppModuleVersionResponse
// @Description 响应结构体
type AppModuleVersionResponse struct {
	Code    int                            `json:"code" example:"200"`        // 状态码
	Message string                         `json:"message" example:"success"` // 状态信息描述
	Data    []AppModuleVersionResponseData `json:"data"`                      // 数据
}

// AppModuleVersion godoc
// @Summary 查询服务部署模的版本信息
// @Tags app
// @Produce json
// @Param namespace	query string true "运行环境"
// @Success     200 {object}    AppModuleVersionResponse 请求成功"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /openapi/app/module/version [get]
func AppModuleVersion(c *gin.Context) {
	data := make([]AppModuleVersionResponseData, 0, 500)
	namespace := c.Query("namespace")
	for _, clu := range settings.GetSetting().Clusters {
		if !clu.Enable {
			continue
		}
		for _, ns := range clu.Namespaces {
			if ns == namespace {
				deps, err := k8s_cli.GetDeploymentList(clu.Name, ns)
				if err != nil {
					web.FailJson2(c, web.CODE_SERVER_ERROR, err.Error())
					return
				}
				for _, dep := range deps.Items {
					modules, err := k8s_service.GetDeployModules(dep)
					if err == nil {
						for _, mod := range modules {
							data = append(data, AppModuleVersionResponseData{
								Cluster:     clu.Name,
								Namespace:   ns,
								App:         dep.Name,
								GitUrl:      mod.GitUrl,
								GitSubDir:   mod.Module,
								GitCommitId: mod.CommitID,
								GitRef:      mod.Tag,
							})
						}
					} else {
						//todo 记录日志
					}
				}
			}
		}
	}
	web.SuccessJson(c, data)
}

// GitProjectVersionResponseData
// @Description Git项目版本信息查询返回结构体
type GitProjectVersionResponseData struct {
	GitUrl string `form:"gitUrl" json:"gitUrl" example:"https://git.firstshare.cn/devops/fs-k8s-tomcat-test.git"`
	// 代码提交标签
	GitRef string `form:"gitRef"  json:"gitRef" example:"master"`
	// 代码提交ID
	GitCommitId string `form:"gitCommitId" json:"gitCommitId" example:"51e1001334abc0257a340e5791fbf578b06bb627"`
	// 被应用引用的模块列表。空值表示该项目的根模块
	Modules []string `json:"modules" example:"fs-k8s-tomcat-test-biz,fs-k8s-tomcat-test-provider"`
	// 被引用的应用列表
	Apps        []string `json:"apps" example:"fs-k8s-tomcat-test-biz"`
	AppsWithEnv []string `json:"appsWithEnv" example:"k8s1/fstest/fs-k8s-tomcat-test-biz"`
	// 被引用的应用的所有负责人信息
	Owners []string `json:"owners" example:"吴志辉,刘全胜"`
}

// GitProjectVersionResponse
// @Description 响应结构体
type GitProjectVersionResponse struct {
	Code    int                             `json:"code" example:"200"`        // 状态码
	Message string                          `json:"message" example:"success"` // 状态信息描述
	Data    []GitProjectVersionResponseData `json:"data"`                      // 数据
}

// GitProjectVersion godoc
// @Summary 查询应用所使用Git项目的版本信息
// @Tags app
// @Produce json
// @Param namespace	query string true "运行环境"
// @Success     200 {object}    GitProjectVersionResponse 请求成功"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /openapi/app/git-project/version [get]
func GitProjectVersion(c *gin.Context) {
	items := map[string]GitProjectVersionResponseData{}
	namespace := c.Query("namespace")
	for _, clu := range settings.GetSetting().Clusters {
		if !clu.Enable {
			continue
		}
		for _, ns := range clu.Namespaces {
			if ns == namespace {
				deps, err := k8s_cli.GetDeploymentList(clu.Name, ns)
				if err != nil {
					web.FailJson2(c, web.CODE_SERVER_ERROR, err.Error())
					return
				}
				for _, dep := range deps.Items {
					if *dep.Spec.Replicas < 1 {
						log.Infof(`应用%s/%s的副本数为0，不查询版本信息`, dep.Namespace, dep.Name)
						continue
					}
					modules, err := k8s_service.GetDeployModules(dep)
					if err != nil {
						log.Warnf("获取应用%s/%s的模块信息失败：%s", dep.Namespace, dep.Name, err.Error())
						continue
					}
					for _, mod := range modules {
						key := fmt.Sprintf("%s@%s", mod.GitUrl, mod.Tag)
						v, found := items[key]
						if !found {
							items[key] = GitProjectVersionResponseData{
								GitUrl:      mod.GitUrl,
								GitRef:      mod.Tag,
								GitCommitId: mod.CommitID,
								Modules:     make([]string, 0, 10),
								Apps:        make([]string, 0, 10),
								AppsWithEnv: make([]string, 0, 10),
								Owners:      make([]string, 0, 30),
							}
						}
						v = items[key]
						v.Apps = append(v.Apps, dep.Name)
						v.AppsWithEnv = append(v.AppsWithEnv, fmt.Sprintf("%s/%s/%s", clu.Name, ns, dep.Name))
						v.Modules = append(v.Modules, mod.Module)
						if users := cmdb_service.GetMainOwners(dep.Name); len(users) > 0 {
							v.Owners = append(v.Owners, users...)
						}
						items[key] = v
					}
				}
			}
		}
	}
	data := make([]GitProjectVersionResponseData, 0, len(items))
	for _, v := range items {
		data = append(data, v)
	}
	web.SuccessJson(c, data)
}

// AppServerDetailResponse
// @Description 响应结构体
type AppServerDetailResponse struct {
	Code    int            `json:"code" example:"200"`        // 状态码
	Message string         `json:"message" example:"success"` // 状态信息描述
	Data    dto.Deployment `json:"data"`                      // 数据
}

// AppServerDetail 获取应用服务信息
// @Summary 获取应用服务信息
// @Tags app
// @Produce json
// @Param cluster query string true "k8s集群名"
// @Param namespace query string true "运行环境"
// @Param app query string true "应用名"
// @Success 200 {object} AppServerDetailResponse "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /openapi/app/server/detail [get]
func AppServerDetail(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	if cluster == "" || namespace == "" || app == "" {
		web.FailJson2(c, web.CODE_CLIENT_ERROR, "参数错误")
		return
	}
	dep, err := k8s_service.GetDeploymentDTO(cluster, namespace, app)
	if err != nil {
		web.FailJson2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, dep)
}

// AppRestart godoc
// @Summary 重启应用
// @Tags app
// @Produce json
// @Param RequestBody	body		AppRestartParam	true "json格式的请求体"
// @Success 200 {object} web.Response "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /openapi/app/restart [post]
func AppRestart(c *gin.Context) {
	var p AppRestartParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	if !strslice.Find(config.Conf.OpenApiTokens, p.Token) {
		web.FailJson2(c, web.CODE_CLIENT_ERROR, "token无效")
		return
	}

	if err := k8s_service.AppRedeploy(p.Cluster, p.Namespace, p.App, ""); err != nil {
		web.FailJson2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	p.Token = "md5:" + encrypt.MD5Hash(p.Token)
	log_service.Create(auth.GetRealName(c), "OenAPI-服务-重启", p.App, p)
	event_service.Create("openapi", event_service.BuildAppKey(p.Cluster, p.Namespace, p.App), "API重启服务")
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      "openapi",
		ResourceType: "app",
		ResourceId:   p.App,
		Title:        "应用重启",
		Message:      fmt.Sprintf("openapi 手动重启了 %s 服务", p.App),
		Level:        "warn",
		Extra:        "",
	})
	web.SuccessJson(c, "success")
}

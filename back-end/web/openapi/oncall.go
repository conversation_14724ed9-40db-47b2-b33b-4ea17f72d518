package openapi

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	log "github.com/sirupsen/logrus"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// scaleUpLock 添加应用扩容锁
func scaleUpLock(cluster, namespace, app string, expire time.Duration) {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("scale-up-locker:%s/%s/%s", cluster, namespace, app))
	_ = cache.SetStruct(cacheKey, "true", expire)
}

// scaleUpIsLocked 检查应用扩容锁
func scaleUpIsLocked(cluster, namespace, app string) bool {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("scale-up-locker:%s/%s/%s", cluster, namespace, app))
	if _, found := cache.GetStruct(cacheKey, interface{}(nil)); found {
		return true
	}
	return false
}

// nodeDrainLock 添加节点驱逐锁
func nodeDrainLock(cluster, node string, expire time.Duration) {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("node-drain-locker:%s/%s", cluster, node))
	_ = cache.SetStruct(cacheKey, "true", expire)
}

// nodeDrainIsLocked 检查节点驱逐锁
func nodeDrainIsLocked(cluster, node string) bool {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("node-drain-locker:%s/%s", cluster, node))
	if _, found := cache.GetStruct(cacheKey, interface{}(nil)); found {
		return true
	}
	return false
}

// OncallWebhookParam
// @Description Oncall服务Webhook参数
type OncallWebhookParam struct {
	// 告警环境
	AlertEnv string `json:"alert_env" binding:"required" example:"k8s0"`
	// 告警等级
	AlertLevel string `json:"alert_level" binding:"-" example:"WARN"`
	// 告警来源
	AlertSource string `json:"alert_source" binding:"-" example:"GRAFANA"`
	// 告警状态
	AlertStatus string `json:"alert_status" binding:"required" example:"FIRING"`
	// 告警名称
	AlertName string `json:"alertname" binding:"required" example:"pod-cpu-throttled"`
	// 运行环境
	Namespace string `json:"namespace" binding:"required" example:"foneshare"`
	// 资源ID
	ResourceID string `json:"resource_id" binding:"required" example:"fs-k8s-tomcat-test-5d8874c9dd-6bg6q"`
	// 资源名称
	ResourceName string `json:"resource_name" binding:"required" example:"fs-k8s-tomcat-test"`
	// 资源类型
	ResourceType string `json:"resource_type" binding:"required" example:"app"`
}

// OncallWebhook oncall webhook API
// @Summary 提供给Oncall系统的回调接口
// @Description 根据 operate 请求参数来指定操作类型
// @Tags oncall
// @Accept json
// @Produce octet-stream
// @Param RequestBody	body		OncallWebhookParam	true "json格式的请求体"
// @Param operate	query		string	false	"操作类型（可选值：ScaleUp | ThreadDump | podDeregister | NodeDrain ）" default()
// @Success 200 {object} web.Response "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /openapi/oncall/webhook [post]
func OncallWebhook(c *gin.Context) {
	var p OncallWebhookParam
	var response web.Response
	defer func(request *OncallWebhookParam, response *web.Response) {
		operate := fmt.Sprintf("OnCall-WebHook")
		operator := fmt.Sprintf("%s(%s)", request.AlertName, request.AlertStatus)
		target := fmt.Sprintf("%s/%s/%s", request.AlertEnv, request.Namespace, request.ResourceName)
		log_service.Create(operator, operate, target, map[string]interface{}{
			"request":  *request,
			"response": *response,
		})
	}(&p, &response)

	if err := c.ShouldBindBodyWith(&p, binding.JSON); err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if !strings.EqualFold(p.AlertStatus, "FIRING") {
		response = web.SuccessJson2(c, "skipped, because alert_status is not FIRING")
		return
	}
	if strings.EqualFold(p.Namespace, "jacoco") {
		response = web.SuccessJson2(c, "skipped, jacoco namespace ignore")
		return
	}
	conf := config.GetOncallConfig()
	if !conf.Enable {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, "oncall webhook is not enabled")
		return
	}

	operate := c.Query("operate")
	var data interface{}
	var err error
	if operate == "ScaleUp" {
		data, err = scaleUp(p)
	} else if operate == "ThreadDump" {
		data, err = ThreadDump(p)
	} else if operate == "podDeregister" {
		data, err = podDeregister(p)
	} else if operate == "NodeDrain" {
		data, err = nodeDrain(p)
	} else {
		response = web.FailJsonWithFailStatus2(c, web.CODE_CLIENT_ERROR, "operate ["+operate+"] is not supported")
		return
	}

	if err != nil {
		response = web.FailJsonWithFailStatus2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	response = web.SuccessJson2(c, data)
}

func scaleUp(p OncallWebhookParam) (ret interface{}, err error) {
	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName
	addReplica := int32(3)

	if !config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, namespace, app) {
		err = fmt.Errorf("skipped, app is not in alert whitelist")
		return
	}

	if scaleUpIsLocked(cluster, namespace, app) {
		err = fmt.Errorf("skipped, app scale up is locked because of has been scaled up recently")
		return
	}
	//todo: 如果服务处于发版或重启过程中（创建时间小于15分钟）则跳过扩容

	dep, err := k8s_service.GetDeploymentDTO(cluster, p.Namespace, app)
	if err != nil {
		return
	}
	if dep.Replicas == 0 {
		//todo: debug日志，等cluster问题解决后删除
		log_service.Create(p.AlertName, "OnCall-服务扩容-Debug", app, p)
		err = fmt.Errorf("skipped, current replicas is 0")
		return
	}

	allowedMaxReplicas := int32(20)
	if clu := settings.GetSetting().GetCluster(cluster); clu != nil {
		allowedMaxReplicas = clu.ScaleMaxReplicas
	}
	if dep.Replicas >= allowedMaxReplicas {
		err = fmt.Errorf("skipped, current replicas is greater than allowed max replicas %d", allowedMaxReplicas)
		return
	}
	newReplicas := dep.Replicas + addReplica

	err = kubectl.Scale(cluster, namespace, app, newReplicas)
	if err != nil {
		return
	}

	//该日志给 scaleDownOncallScaledApps 定时任务使用。参考：jobs.go 下的 scaleDownOncallScaledApps
	log_service.Create(p.AlertName, "oncall-scale-up", fmt.Sprintf("%s/%s/%s", cluster, namespace, app), "")
	scaleUpLock(cluster, namespace, app, 15*time.Minute)

	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ScaleUp",
		fmt.Sprintf("服务 %s 因 %s 告警触发了自动扩容，副本数：%d → %d", app, p.AlertName, dep.Replicas, newReplicas))

	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
		fmt.Sprintf("【告警自愈】服务扩容，副本数: %d → %d", dep.Replicas, newReplicas))

	return
}

func podDeregister(p OncallWebhookParam) (ret interface{}, err error) {
	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName

	if !config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, namespace, app) {
		err = fmt.Errorf("skipped, app is not in alert whitelist")
		return
	}

	isPodResource := false
	if pod, err2 := k8s_service.PodDetail(cluster, namespace, p.ResourceID); pod != nil && err2 == nil {
		isPodResource = true
		//对于启动不久的pod，关闭摘除。避免Pod启动阶段被摘除
		if time.Now().Unix()-pod.CreationTimestamp.Unix() < 900 {
			err = fmt.Errorf("skipped, pod is created less than 15 minutes")
			return
		}
	}
	if !isPodResource {
		err = fmt.Errorf("skipped, resource is not pod")
		return
	}
	_ = kubectl.DeregisterService(cluster, namespace, p.ResourceID)
	_ = k8s_service.PodDeregister(cluster, namespace, p.ResourceID)

	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "Deregister",
		fmt.Sprintf("Pod %s 因 %s 告警触发了自动摘除", p.ResourceID, p.AlertName))

	//待改进： 记录摘除状态
	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
		fmt.Sprintf("【告警自愈】Pod摘除，名称: %s", p.ResourceID))

	return
}

func nodeDrain(p OncallWebhookParam) (ret interface{}, err error) {
	cluster := p.AlertEnv
	node := p.ResourceID
	if strings.EqualFold(p.AlertEnv, "k8s1") {
		node = ipToVlnxName(node)
	}

	if !config.GetOncallConfig().GetAlert(p.AlertName).NodeAllow(cluster, node) {
		err = fmt.Errorf("skipped, node is not in alert whitelist")
		return
	}
	podList, err := k8s.ListPodByNode(p.AlertEnv, node)
	if err != nil {
		return
	}

	drainPodList := make([]corev1.Pod, 0, 5)
	//只对L3和L2级别的pod进行驱逐，优先驱逐L3级别的pod。
	for _, serverLevel := range []string{"L3", "L2"} {
		for _, pod := range podList.Items {
			if pod.Annotations["fxiaoke.com/server-level"] == serverLevel {
				drainPodList = append(drainPodList, pod)
			}
		}
	}
	//最多驱逐3个pod
	if len(drainPodList) > 3 {
		drainPodList = drainPodList[:3]
	}

	if len(drainPodList) > 0 {
		if nodeDrainIsLocked(p.AlertEnv, node) {
			err = fmt.Errorf("skipped, node drain is locked")
			return
		}
		nodeDrainLock(p.AlertEnv, node, 10*time.Minute)
	}

	data := make([]map[string]interface{}, 0, len(drainPodList))
	for _, pod := range drainPodList {
		status := "skipped"
		if e := k8s_service.PodDelete(p.AlertEnv, pod.Namespace, pod.Name); e != nil {
			status = "delete failed, err: " + e.Error()
		} else {
			status = "delete success"
			app := k8s_util.GetAppName(pod.Name)
			event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, pod.Namespace, app),
				fmt.Sprintf("【告警自愈】Pod驱逐，名称: %s", pod.Name))
			sendSelfHealingEvent(p.AlertEnv, pod.Namespace, "app", app, p.AlertName, "Eviction",
				fmt.Sprintf("Pod %s 因 %s 告警触发了自动驱逐", pod.Name, p.AlertName))
		}
		data = append(data, map[string]interface{}{
			"cluster":      p.AlertEnv,
			"namespace":    pod.Namespace,
			"node":         node,
			"pod":          pod.Name,
			"server_level": pod.Annotations["fxiaoke.com/server-level"],
			"status":       status,
		})
	}
	ret = data
	return
}

func ThreadDump(p OncallWebhookParam) (ret interface{}, err error) {
	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName
	pod := p.ResourceID

	_, err = k8s_service.PodDetail(cluster, namespace, pod)

	if err != nil {
		return
	}
	if !config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, namespace, app) {
		err = fmt.Errorf("skipped, app is not in alert whitelist")
		return
	}

	dumpFile, err := k8s_service.ThreadDump(cluster, namespace, pod, false)
	_, _ = k8s_service.ThreadDumpByGstack(cluster, namespace, pod)
	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, k8s_util.GetAppName(pod)),
		fmt.Sprintf("【告警自愈】线程Dump，文件路径: %s", dumpFile))

	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ThreadDump",
		fmt.Sprintf("Pod %s 因 %s 告警触发了线程Dump，文件地址：%s", pod, p.AlertName, dumpFile))

	return "filepath: " + dumpFile, err
}

func ipToVlnxName(ip string) string {
	if !strings.HasPrefix(ip, "172.17") {
		return ip
	}

	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return ip
	}

	third, err1 := strconv.Atoi(parts[2])
	fourth, err2 := strconv.Atoi(parts[3])
	if err1 != nil || err2 != nil {
		return ip
	}

	return fmt.Sprintf("vlnx%03d%03d", third, fourth)
}

func sendSelfHealingEvent(cluster, namespace, resourceType, resourceId, alertName, selfHealingType, message string) {
	if err := kafka.SendDevopsEvent(kafka.DevopsEvent{
		Creator:      "fs-k8s-app-manager",
		Level:        "warn",
		Title:        "告警自愈",
		Cluster:      cluster,
		Profile:      namespace,
		ResourceType: resourceType,
		ResourceId:   resourceId,
		Message:      fmt.Sprintf("%s/%s", alertName, selfHealingType),
		Extra:        message,
	}); err != nil {
		log.Warn("send self healing event fail, " + err.Error())
	}
}

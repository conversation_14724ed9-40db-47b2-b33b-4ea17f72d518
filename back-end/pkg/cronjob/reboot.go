package cronjob

import (
	"fmt"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/notify_service"
	"fs-k8s-app-manager/service/reboot_service"
	log "github.com/sirupsen/logrus"
	"strings"
	"time"
)

/*
*
重启任务
*/
func reboot() {
	log.Info("start reboot job")
	items, err := reboot_service.Search("")
	if err != nil {
		log.Error(err)
		return
	}
	for _, it := range items {
		if !it.InRebootTime() {
			continue
		}
		if err := k8s_service.AppRedeploy(it.Cluster, it.Namespace, it.App, "25%"); err != nil {
			log.Errorf("reboot fail, app: %v,err: %v", it, err)
			continue
		}
		log_service.CreateBySys("服务重启", it.App, it)
		qixin := []string{
			fmt.Sprintf("--- 服务定时重启 ---"),
			fmt.Sprintf("【应用】：%s", it.App),
			fmt.Sprintf("【环境】：%s", it.Namespace),
			fmt.Sprintf("【所在集群】：%s", it.Cluster),
			fmt.Sprintf("【重启时间点】：%d", it.RebootHour),
			fmt.Sprintf("【备注】：%s", it.Remark),
			fmt.Sprintf("【重启原因】：通过定时任务自动重启，详情请查看 发布系统/服务定时重启"),
		}
		if err := notify_service.SendQiXinToImportantAlertSession(cmdb_service.GetMainOwnerIds(it.App), strings.Join(qixin, "\n")); err != nil {
			log.Warn("企信发送失败，" + err.Error())
		}
		time.Sleep(1 * time.Minute)
	}
	return
}

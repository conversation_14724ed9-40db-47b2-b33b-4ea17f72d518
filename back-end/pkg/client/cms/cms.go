package cms

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"

	"github.com/go-resty/resty/v2"
)

func GetConfig(profile, name string) (ret string, err error) {
	cms := config.Conf.CMS
	if cms.ApiHost == "" {
		return "", errors.New("cms cmdb api is empty")
	}
	url := fmt.Sprintf("%s/get/?token=%s&profile=%s&name=%s", cms.ApiHost, cms.Token, profile, name)
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})

	resp, err := client.R().
		Get(url)
	if resp.StatusCode() != 200 {
		return "", fmt.Errorf("cms fail, code: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}
	return string(resp.Body()), err
}

func UpdateConfig(profile, name, content string) (ret string, err error) {
	cms := config.Conf.CMS
	if cms.ApiHost == "" {
		return "", errors.New("cms cmdb api is empty")
	}
	url := fmt.Sprintf("%s/save/", cms.ApiHost)
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})

	resp, err := client.R().
		SetFormData(map[string]string{
			"token":   cms.Token,
			"profile": profile,
			"name":    name,
			"content": content,
			"editor":  "k8s-app-manager",
		}).
		Post(url)
	if resp.StatusCode() != 200 {
		return "", fmt.Errorf("cms fail, code: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}
	return string(resp.Body()), err
}

type CmsConfig struct {
	Content        string            `json:"content"`
	CreateTime     int64             `json:"createTime"`
	Editor         string            `json:"editor"`
	GroupId        int               `json:"groupId"`
	Id             int               `json:"id"`
	ModifyTime     int64             `json:"modifyTime"`
	Mtime          string            `json:"mtime"`
	Name           string            `json:"name"`
	NameDisplay    string            `json:"nameDisplay"`
	NeedSync       bool              `json:"needSync"`
	OperationsType string            `json:"operationsType"`
	Profile        string            `json:"profile"`
	Remarks        string            `json:"remarks"`
	Summary        string            `json:"summary"`
	Version        int               `json:"version"`
	View           string            `json:"view"`
	RepairAddrs    map[string]string `json:"repairAddrs"`
}

func GetProfileConfigs(profile string) (ret []CmsConfig, err error) {
	cms := config.Conf.CMS
	if cms.ApiHost == "" {
		return nil, errors.New("cms api is empty")
	}
	url := fmt.Sprintf("%s/get/byProfile/?caller=%s&profile=%s", cms.ApiHost, "fs-k8s-app-manager", profile)
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})

	resp, err := client.R().Get(url)
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("cms fail, code: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}
	err = json.Unmarshal(resp.Body(), &ret)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}
	return ret, nil
}

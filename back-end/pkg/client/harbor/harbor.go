package harbor

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"github.com/go-resty/resty/v2"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"strconv"
	"strings"
	"time"
)

type Pagination struct {
	Page     int
	PageSize int
	MaxPage  int
}

func (p Pagination) QueryStr() string {
	return fmt.Sprintf("page=%d&page_size=%d", p.Page, p.PageSize)
}
func (p Pagination) maxPageExceed() bool {
	return p.Page > p.MaxPage
}

func Create() *client {
	conf := config.Conf.Harbor
	ret := &client{
		Host:     conf.Host,
		Username: conf.Username,
		Password: conf.Password,
		cli:      resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}),
	}
	if conf.Https {
		ret.cli.SetHostURL("https://" + conf.Host)
	} else {
		ret.cli.SetHostURL("http://" + conf.Host)
	}
	ret.cli.SetBasicAuth(ret.Username, ret.Password)
	return ret
}

type client struct {
	Host     string
	Username string
	Password string
	cli      *resty.Client
}

type Project struct {
	ProjectId    int    `json:"project_id"`
	Name         string `json:"name"`
	CreationTime string `json:"creation_time"`
	UpdateTime   string `json:"update_time"`
	OwnerId      int    `json:"owner_id"`
	OwnerName    string `json:"owner_name"`
	RepoCount    int    `json:"repo_count"`
}

type Repository struct {
	Id            int    `json:"id"`
	Name          string `json:"name"`
	ProjectId     int    `json:"project_id"`
	Description   string `json:"description"`
	PullCount     int64  `json:"pull_count"`
	ArtifactCount int64  `json:"artifact_count"`
	CreationTime  string `json:"creation_time"`
	UpdateTime    string `json:"update_time"`
}

type ExtraAttrs struct {
	Architecture string         `json:"architecture"`
	Os           string         `json:"os"`
	Author       string         `json:"author"`
	Created      string         `json:"created"`
	Config       ArtifactConfig `json:"config"`
}
type ArtifactConfig struct {
	Entrypoint []string          `json:"entrypoint"`
	Env        []string          `json:"env"`
	Labels     map[string]string `json:"labels"`
}

func (c ArtifactConfig) GitTag() string {
	ret := c.Labels["gitTag"]
	//兼容历史数据，等到了 2025-01-01， 如果历史数据问题已经不存在，则可去掉以下代码
	if ret == "" {
		ret = c.Labels["tag"]
	}
	return ret
}

func (c ArtifactConfig) BuildRemark() string {
	ret := c.Labels["buildRemark"]
	//兼容历史数据，等到了 2025-01-01， 如果历史数据问题已经不存在，则可去掉以下代码
	if ret == "none" || ret == "--" {
		ret = ""
	}
	return ret
}

type Artifact struct {
	Id           int64      `json:"id"`
	Digest       string     `json:"digest"`
	Size         int64      `json:"size"`
	ProjectId    int64      `json:"project_id"`
	RepositoryId int64      `json:"repository_id"`
	ExtraAttrs   ExtraAttrs `json:"extra_attrs"`
	Tags         []Tag      `json:"tags"`
}

type Tag struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	PullTime     string `json:"pull_time"`
	PushTime     string `json:"push_time"`
	Immutable    bool   `json:"immutable"`
	ArtifactId   int64  `json:"artifact_id"`
	RepositoryId int64  `json:"repository_id"`
}

func (c client) GetAllProjects() (ret []Project, err error) {
	page := Pagination{
		Page:     0,
		PageSize: 100,
		MaxPage:  500,
	}
	for {
		page.Page += 1
		if page.maxPageExceed() {
			log.Warnf("GetAllProjects page exceed max page: %d", page.MaxPage)
			break
		}
		items, e := c.GetProjects(page)
		if e != nil {
			err = e
			return
		}
		ret = append(ret, items...)
		if len(items) == 0 || len(items) < page.PageSize {
			break
		}
		time.Sleep(20 * time.Millisecond)
	}
	return
}
func (c client) GetProjects(page Pagination) (ret []Project, err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetQueryString(page.QueryStr()).
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Get("/api/v2.0/projects")
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	err = json.Unmarshal(repo.Body(), &ret)
	return
}
func (c client) GetAllRepositories(project string) (ret []Repository, err error) {
	page := Pagination{
		Page:     0,
		PageSize: 100,
		MaxPage:  500,
	}
	for {
		page.Page += 1
		if page.maxPageExceed() {
			log.Warnf("GetAllRepositories page exceed max page: %d", page.MaxPage)
			break
		}
		items, e := c.GetRepositories(project, page)
		if e != nil {
			err = e
			return
		}
		ret = append(ret, items...)
		if len(items) == 0 || len(items) < page.PageSize {
			break
		}
		time.Sleep(20 * time.Millisecond)
	}
	return
}
func (c client) GetRepositories(project string, page Pagination) (ret []Repository, err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetQueryString(page.QueryStr()).
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Get(fmt.Sprintf("/api/v2.0/projects/%s/repositories", project))
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	err = json.Unmarshal(repo.Body(), &ret)
	if err == nil {
		//harbor bug: Repository名称里包含了project名称. todo: 升级后看问题是否解决
		projPre := project + "/"
		startIdx := len(project) + 1
		for idx, _ := range ret {
			it := &ret[idx]
			if strings.HasPrefix(it.Name, projPre) {
				it.Name = it.Name[startIdx:]
			}
		}
	}
	return
}

func (c client) GetAllArtifacts(project, repository string) (ret []Artifact, err error) {
	page := Pagination{
		Page:     0,
		PageSize: 100,
		MaxPage:  500,
	}
	for {
		page.Page += 1
		if page.maxPageExceed() {
			log.Warnf("GetAllArtifacts page exceed max page: %d", page.MaxPage)
			break
		}
		items, _, e := c.GetArtifacts(project, repository, page.Page, page.PageSize)
		if e != nil {
			err = e
			return
		}
		ret = append(ret, items...)
		if len(items) == 0 || len(items) < page.PageSize {
			break
		}
		time.Sleep(20 * time.Millisecond)
	}
	return
}

func (c client) GetArtifacts(project, repository string, page, pageSize int) (ret []Artifact, count int64, err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetQueryString(fmt.Sprintf("page=%d&page_size=%d", page, pageSize)+"&with_tag=true&with_label=false&with_scan_overview=false&with_signature=false&with_immutable_status=false").
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Get(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts", project, encodeRepository(repository)))
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	count, err = strconv.ParseInt(repo.Header().Get("x-total-count"), 10, 64)
	err = json.Unmarshal(repo.Body(), &ret)
	return
}

func (c client) FirstArtifactByTag(project, repository, tag string) (ret Artifact, err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetQueryString(fmt.Sprintf("page=%d&page_size=%d", 1, 10)+"&with_tag=true&with_label=false&with_scan_overview=false&with_signature=false&with_immutable_status=false").
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Get(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s", project, encodeRepository(repository), tag))
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	err = json.Unmarshal(repo.Body(), &ret)
	return
}

func (c client) ArtifactExist(project, repository, tag string) (bool, error) {
	var repo *resty.Response
	repo, err := c.cli.R().
		SetQueryString(fmt.Sprintf("page=%d&page_size=%d", 1, 10)+"&with_tag=true&with_label=false&with_scan_overview=false&with_signature=false&with_immutable_status=false").
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Get(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s", project, encodeRepository(repository), tag))
	if err != nil {
		return false, err
	}
	return repo.StatusCode() != 404, nil
}

func (c client) GetAllTags(project, repository, artifact string) (ret []Tag, err error) {
	page := Pagination{
		Page:     0,
		PageSize: 100,
		MaxPage:  500,
	}
	for {
		page.Page += 1
		if page.maxPageExceed() {
			log.Warnf("GetAllTags page exceed max page: %d", page.MaxPage)
			break
		}
		items, e := c.GetTags(project, repository, artifact, page)
		if e != nil {
			err = e
			return
		}
		ret = append(ret, items...)
		if len(items) == 0 || len(items) < page.PageSize {
			break
		}
		time.Sleep(20 * time.Millisecond)
	}
	return
}

func (c client) GetTags(project, repository, artifact string, page Pagination) (ret []Tag, err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetQueryString(page.QueryStr()).
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Get(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s/tags", project, encodeRepository(repository), artifact))
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	err = json.Unmarshal(repo.Body(), &ret)
	return
}

func (c client) CreateTag(project, repository, srcTag, targetTag string) (err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-request-id", createRId()).
		SetBody(fmt.Sprintf(`{"name": "%s"}`, targetTag)).
		Post(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s/tags", project, encodeRepository(repository), srcTag))

	if err == nil && repo.StatusCode() != 201 {
		err = errors.New(string(repo.Body()))
	}
	return
}

func (c client) CopyArtifact(project, repository, from string) (err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-request-id", createRId()).
		SetQueryParam("from", from).
		Post(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts", project, encodeRepository(repository)))
	if err == nil && repo.StatusCode() != 201 {
		err = errors.New(string(repo.Body()))
	}
	return
}
func (c client) DeleteArtifacts(project, repository, artifact string) (err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Delete(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s",
			project,
			encodeRepository(repository),
			artifact,
		))
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	return
}
func (c client) DeleteTag(project, repository, artifact, tag string) (err error) {
	var repo *resty.Response
	repo, err = c.cli.R().
		SetHeader("Accept", "application/json").
		SetHeader("x-request-id", createRId()).
		Delete(fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s/tags/%s",
			project,
			encodeRepository(repository),
			artifact,
			tag,
		))
	if err != nil {
		return
	}
	if repo.StatusCode() != 200 {
		err = errors.New(string(repo.Body()))
		return
	}
	return
}

func encodeRepository(name string) string {
	return strings.ReplaceAll(name, "/", "%252F")
}
func createRId() string {
	return uuid.NewV4().String()
}

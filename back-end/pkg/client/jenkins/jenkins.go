package jenkins

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"github.com/bndr/gojenkins"
	"net/http"
	"time"
)

type BuildResult string

const (
	BUILD_RESULT_SUCCESS = "SUCCESS"
	BUILD_RESULT_ABORTED = "ABORTED"
	BUILD_RESULT_FAILURE = "FAILURE"
)

func CreateJenkinsClientDefault(job string) (*jenkinsClient, error) {
	if job == "" {
		return nil, fmt.Errorf("jenkins job name is empty")
	}
	jc := config.Conf.Jenkins
	return CreateJenkinsClient(jc.Host, jc.Username, jc.Password, job)
}
func CreateJenkinsClient(host, user, pwd, jobName string) (*jenkinsClient, error) {
	//关闭证书校验
	client := http.Client{Transport: &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	}}
	cli := gojenkins.CreateJenkins(&client, host, user, pwd)

	cli, err := cli.Init(context.Background())
	if err != nil {
		return nil, err
	}
	return &jenkinsClient{
		Cli:     cli,
		JobName: jobName,
	}, nil
}

type jenkinsClient struct {
	Cli     *gojenkins.Jenkins
	JobName string
}

func (j *jenkinsClient) BuildWithParameters(params map[string]string) (queueId int64, err error) {
	queueId = 0
	ctx := context.Background()
	job, err := j.Cli.GetJob(ctx, j.JobName)
	if err != nil {
		return
	}
	//jenkins 默认有 5 秒的静默期。在静默期间，Job 会放入到 Queue 队列中，而在 InvokeSimple 方法里，如果检测到 Job 的 isQueued 为 true，则不会执行创建任务实例。
	//这个问题会导致 Job 实例无法正常创建。因此，这里通过等待一段时间后重试来确保执行 Jenkins 任务时不会因为静默期导致任务实例创建失败。
	for i := 0; i < 3; i++ {
		queueId, err = job.InvokeSimple(ctx, params)
		if err != nil || queueId != 0 {
			return
		}
		time.Sleep(5 * time.Second)
	}
	return
}

func (j *jenkinsClient) GetQueueItem(queueId int64) (*gojenkins.Task, error) {
	return j.Cli.GetQueueItem(context.Background(), queueId)
}
func (j *jenkinsClient) GetBuildIdByQueueItemId(queueItemId int64, timeoutSeconds int) (int64, error) {
	start := time.Now().Unix()
	timeout := int64(timeoutSeconds)

	for {
		item, err := j.Cli.GetQueueItem(context.Background(), queueItemId)
		if err != nil {
			return 0, err
		}
		if item.Raw.Executable.Number > 0 {
			return item.Raw.Executable.Number, nil
		}
		if time.Now().Unix()-start > timeout {
			return 0, errors.New(fmt.Sprintf("GetBuildIdByQueueItemId timeout [%d seconds], queueItemId: %d", timeout, queueItemId))
		}
		time.Sleep(3 * time.Second)
	}
}

func (j *jenkinsClient) GetBuild(buildId int64) (*gojenkins.Build, error) {
	return j.Cli.GetBuild(context.Background(), j.JobName, buildId)
}
func (j *jenkinsClient) StopBuild(buildId int64) (bool, error) {
	if build, err := j.Cli.GetBuild(context.Background(), j.JobName, buildId); err == nil {
		return build.Stop(context.Background())
	} else {
		return false, err
	}
}

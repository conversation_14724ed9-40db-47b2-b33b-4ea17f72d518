package clickhouse

import (
	"context"
	"fs-k8s-app-manager/config"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	log "github.com/sirupsen/logrus"
	"time"
)

var conn driver.Conn

func init() {
	var err error
	user := config.Conf.Clickhouse.User
	if user == "" {
		user = "default"
	}
	pwd := config.Conf.Clickhouse.Password
	conn, err = clickhouse.Open(&clickhouse.Options{
		Addr: config.Conf.Clickhouse.Addr,
		Auth: clickhouse.Auth{
			Database: "logger",
			Username: user,
			Password: pwd,
		},
		//TLS: &tls.Config{
		//	InsecureSkipVerify: false,
		//},
		DialTimeout:      5 * time.Second,
		Debug:            false,
		MaxOpenConns:     5,
		MaxIdleConns:     5,
		ConnMaxLifetime:  time.Duration(10) * time.Minute,
		ConnOpenStrategy: clickhouse.ConnOpenInOrder,
	})
	if err != nil {
		log.Errorf("clickhouse init fail, %v", err)
	}
}

// TomcatTrafficCount 查询Tomcat流量
// cluster 集群名称
// namespace 命名空间
// app 应用名称
// startTimes 开始时间
func TomcatTrafficCount(cluster, namespace, app string, startTimes time.Time) (uint64, error) {
	var count uint64
	err := conn.QueryRow(context.Background(), "select count() from logger.tomcat_access where _time_second_ >= ? and cluster = ? and profile = ? and app = ?", startTimes, cluster, namespace, app).Scan(&count)
	return count, err
}

package kubectl

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/util/exec"
	"fs-k8s-app-manager/pkg/util/file"
	"github.com/dustin/go-humanize"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type BaseMetric struct {
	Cluster    string    `json:"cluster"`
	Namespace  string    `json:"namespace"`
	Name       string    `json:"name"`
	CreateTime time.Time `json:"createTime"`
	CPU        int64     `json:"cpu"`
	Memory     int64     `json:"memory"`
}
type PodMetric struct {
	BaseMetric
}

type AppMetric struct {
	BaseMetric
	PodMetrics []PodMetric `json:"podMetrics"`
}

func Apply(cluster, namespace, fileContent string) error {
	f, err := file.CreateTempFile(fileContent)
	if err != nil {
		return err
	}
	command := fmt.Sprintf("apply -n %s -f %s", namespace, f)
	_, err = Cmd(cluster, command)
	return err
}

func Create(cluster, namespace, fileContent string, useSSA bool) error {
	f, err := file.CreateTempFile(fileContent)
	if err != nil {
		return err
	}
	command := fmt.Sprintf("create -n %s -f %s", namespace, f)
	if useSSA {
		command += " --server-side=true"
	}
	_, err = Cmd(cluster, command)
	return err
}

func Replace(cluster, namespace, fileContent string) error {
	f, err := file.CreateTempFile(fileContent)
	if err != nil {
		return err
	}
	command := fmt.Sprintf("replace -n %s -f %s", namespace, f)
	_, err = Cmd(cluster, command)
	return err
}

func Scale(cluster, namespace, app string, replicas int32) error {
	command := fmt.Sprintf("scale deployment -n %s %s --replicas=%d", namespace, app, replicas)
	_, err := Cmd(cluster, command)
	return err
}

func ApplyOrReplace(cluster, namespace, fileContent string, op string) error {
	if op == "replace" {
		return Replace(cluster, namespace, fileContent)
	} else if op == "apply" {
		return Apply(cluster, namespace, fileContent)
	}
	return errors.New("only support operate is: apply, replace")
}

func WaitDeployRollout(cluster, namespace, app string, timeoutSecond int) (string, error) {
	command := fmt.Sprintf("rollout status -n %s deployment %s --timeout=%ds", namespace, app, timeoutSecond)
	return Cmd(cluster, command)
}
func RolloutUndo(cluster, namespace, app string) (string, error) {
	command := fmt.Sprintf("rollout undo -n %s deployment %s ", namespace, app)
	return Cmd(cluster, command)
}

func ListPodFile(cluster, namespace, pod, path string) (*[]dto.PodFile, error) {
	//command_only_file := fmt.Sprintf("exec -n %s %s -- ls -lAt --full-time %s | grep -v ^d ",
	//	namespace,
	//	pod,
	//	path)e
	command := fmt.Sprintf("exec -n %s %s -- ls -lAt --full-time %s ",
		namespace,
		pod,
		path)

	lines, err := Cmd(cluster, command)
	if err != nil {
		return nil, err
	}
	lineList := strings.Split(lines, "\n")
	ret := make([]dto.PodFile, 0, len(lineList))
	for _, line := range lineList {
		rows := strings.Fields(line)
		if len(rows) < 9 {
			continue
		}
		isDir := false
		if strings.HasPrefix(rows[0], "d") {
			isDir = true
		}
		size, err := strconv.ParseInt(rows[4], 10, 64)
		var humanizeSize string
		if err == nil {
			humanizeSize = humanize.IBytes(uint64(size))
		} else {
			size = -1
			humanizeSize = "-"
		}
		day := rows[5]
		hour := rows[6]
		// some time format is : 11:27:21.569306249
		if len(hour) > 0 && strings.Contains(hour, ".") {
			hour = strings.Split(hour, ".")[0]
		}
		ret = append(ret, dto.PodFile{
			Name:         rows[8],
			IsDir:        isDir,
			Size:         size,
			HumanizeSize: humanizeSize,
			ModifyTime:   fmt.Sprintf("%s %s", day, hour),
		})
	}
	return &ret, nil
}

func ListJavaCmsFile(cluster, namespace, pod string) ([]string, error) {
	command := fmt.Sprintf("exec -n %s %s "+
		"-- find /opt/tomcat/webapps -regex /opt/tomcat/webapps/.*/WEB-INF/classes/autoconf/.*",
		namespace,
		pod)

	lines, err := Cmd(cluster, command)
	if err != nil {
		return nil, err
	}
	lineList := strings.Split(lines, "\n")
	ret := make([]string, 0, len(lineList))
	for _, line := range lineList {
		name := filepath.Base(line)
		if len(name) < 1 || strings.HasPrefix(name, ".") {
			continue
		}
		ret = append(ret, name)
	}
	return ret, nil
}
func ListJarFile(cluster, namespace, pod string, isSpringBootJar bool) ([]string, error) {
	var items []string
	var err error
	if isSpringBootJar {
		items, err = ListJarFilePathFromSpringBootJar(cluster, namespace, pod)
	} else {
		items, err = ListJarFilePath(cluster, namespace, pod)
	}
	if err != nil {
		return nil, err
	}
	ret := make([]string, 0, len(items))
	for _, it := range items {
		ret = append(ret, filepath.Base(it))
	}
	return ret, nil
}

func GetTomcatVersion(cluster, namespace, pod string, isSpringBootJar bool) string {
	if isSpringBootJar {
		return "Error: 暂不支持SpringBoot应用扫描"
	}
	command := fmt.Sprintf("exec -n %s %s "+
		"-- java -Xmx16m -cp /opt/tomcat/lib/catalina.jar org.apache.catalina.util.ServerInfo | grep 'Server version'",
		namespace,
		pod)

	output, err := Cmd(cluster, command)
	if err != nil {
		return fmt.Sprintf("Error: %s", err.Error())
	}
	output = strings.ReplaceAll(output, "\r\n", " ")
	output = strings.ReplaceAll(output, "\n", " ")
	output = strings.ReplaceAll(output, "Server version: Apache Tomcat", "")
	output = strings.ReplaceAll(output, "/", "")
	output = strings.TrimSpace(output)
	return output
}

//func ListModuleJarFile(cluster, namespace, pod string) (map[string]string, error) {
//	items, err := ListJarFilePath(cluster, namespace, pod)
//	if err != nil {
//		return nil, err
//	}
//	ret := make(map[string]string)
//	for _, it := range items {
//
//		ret = append(ret, filepath.Base(it))
//	}
//	return ret, nil
//}

func ListJarFilePath(cluster, namespace, pod string) ([]string, error) {
	command := fmt.Sprintf("exec -n %s %s "+
		"-- find /opt/tomcat/webapps -regex /opt/tomcat/webapps/.*/WEB-INF/lib/.*",
		namespace,
		pod)

	lines, err := Cmd(cluster, command)
	if err != nil {
		return nil, err
	}
	lineList := strings.Split(lines, "\n")
	ret := make([]string, 0, len(lineList))
	for _, line := range lineList {
		if !strings.HasSuffix(line, ".jar") {
			continue
		}
		ret = append(ret, line)
	}
	return ret, nil
}

// ListJarFilePathFromSpringBootJar 从SpringBoot jar应用中获取依赖jar文件列表
func ListJarFilePathFromSpringBootJar(cluster, namespace, pod string) ([]string, error) {
	command := fmt.Sprintf("exec -n %s %s "+
		"-- sh -c 'jar -tf /opt/tomcat/webapps/*.jar' ",
		namespace,
		pod)

	lines, err := Cmd(cluster, command)
	if err != nil {
		return nil, err
	}
	lineList := strings.Split(lines, "\n")
	ret := make([]string, 0, len(lineList))
	for _, line := range lineList {
		if !strings.HasSuffix(line, ".jar") {
			continue
		}
		if !strings.Contains(line, "BOOT-INF/lib/") {
			continue
		}
		ret = append(ret, line)
	}
	return ret, nil
}

func CopyFileFromPod(cluster, namespace, pod, path, destPath string) error {
	command := fmt.Sprintf("cp %s/%s:%s %s",
		namespace,
		pod,
		path,
		destPath)
	_, err := CmdWithTimeout(cluster, command, 5*time.Minute)
	return err
}

func ArchiveFiles(cluster, namespace, pod, dir string, files []string, destFile string) error {
	command := fmt.Sprintf("-n %s exec %s -- bash -c 'cd %s && zip -r %s %s'",
		namespace, pod, dir, destFile, strings.Join(files, " "))
	_, err := CmdWithTimeout(cluster, command, 10*time.Minute)
	return err
}

func CopyFileToPod(cluster, namespace, pod, path, srcPath string) error {
	command := fmt.Sprintf("cp %s %s/%s:%s",
		srcPath,
		namespace,
		pod,
		path)
	_, err := CmdWithTimeout(cluster, command, 5*time.Minute)
	return err
}
func RolloutDeployment(cluster, namespace, app, revision string) error {
	command := fmt.Sprintf("rollout undo -n %s deployment.v1.apps/%s --to-revision=%s",
		namespace,
		app,
		revision)
	_, err := Cmd(cluster, command)
	return err
}

func DeleteJob(cluster, namespace, job string) error {
	command := fmt.Sprintf("delete job -n %s %s ",
		namespace, job)
	_, err := Cmd(cluster, command)
	return err
}

func DeregisterService(cluster, namespace, pod string) error {
	command := fmt.Sprintf("-n %s exec %s -- fs-service.sh --operate=deregister",
		namespace, pod)
	_, err := Cmd(cluster, command)
	return err
}

func JacocoReset(cluster, namespace, pod string) error {
	//	command := fmt.Sprintf("-n %s exec %s -- java -jar /opt/tomcat/jacoco-agent/jacococli.jar dump --reset --address 127.0.0.1 --port 6300 --destfile /tmp/reset.exec",
	//		namespace, pod)
	//	_, err := Cmd(cluster, command)
	//	return err

	command := fmt.Sprintf("-n %s exec %s -- fs-jacoco.sh reset",
		namespace, pod)
	_, err := Cmd(cluster, command)
	return err
}
func JacocoDump(cluster, namespace, pod string, jars string) error {
	command := fmt.Sprintf("-n %s exec %s -- fs-jacoco.sh buildExecAndClassFile %s",
		namespace, pod, jars)
	_, err := CmdWithTimeout(cluster, command, 300*time.Second)
	return err
}

func JacocoCoverageReset(cluster, namespace, pod string) (string, error) {
	command := fmt.Sprintf("-n %s exec %s -- fs-jacoco-coverage-reset.sh ",
		namespace, pod)
	return CmdWithTimeout(cluster, command, 300*time.Second)
}

func JacocoCoverageBuild(cluster, namespace, pod string, webapp, jar, commitId, gitRef string, outputFilepath string) (string, error) {
	command := fmt.Sprintf("-n %s exec %s -- fs-jacoco-coverage-build.sh  "+
		"--webapp %s "+
		"--jars %s "+
		"--commit_id %s "+
		"--git_ref %s "+
		"--output_filepath %s",
		namespace, pod, webapp, jar, commitId, gitRef, outputFilepath)
	return CmdWithTimeout(cluster, command, 300*time.Second)
}

func GetPodsMetrics(cluster, namespace, app string) ([]PodMetric, error) {
	command := fmt.Sprintf("top pod -n %s", namespace)
	if app != "" {
		command = fmt.Sprintf("top pod -n %s -l app=%s", namespace, app)
	}
	out, err := Cmd(cluster, command)
	if err != nil {
		return nil, err
	}
	ret := make([]PodMetric, 0, 10)
	now := time.Now()
	for _, line := range splitNewLineAndRemoveEmpty(out) {
		items := strings.Fields(line)
		if len(items) != 3 {
			continue
		}
		name, cpuStr, memStr := items[0], items[1], items[2]
		if !strings.HasSuffix(cpuStr, "m") || !strings.HasSuffix(memStr, "Mi") {
			continue
		}
		cpu, _ := strconv.ParseInt(cpuStr[:len(cpuStr)-1], 10, 64)
		mem, _ := strconv.ParseInt(memStr[:len(memStr)-2], 10, 64)
		ret = append(ret, PodMetric{
			BaseMetric{
				Cluster:    cluster,
				Namespace:  namespace,
				Name:       name,
				CPU:        cpu,
				Memory:     mem,
				CreateTime: now,
			},
		})
	}
	return ret, nil
}

func GetPodMetric(cluster, namespace, pod string) (ret PodMetric, err error) {
	command := fmt.Sprintf("top pod -n %s %s", namespace, pod)
	out, err := Cmd(cluster, command)
	if err != nil {
		return ret, err
	}
	now := time.Now()
	for _, line := range splitNewLineAndRemoveEmpty(out) {
		items := strings.Fields(line)
		if len(items) != 3 {
			continue
		}
		name, cpuStr, memStr := items[0], items[1], items[2]
		if !strings.HasSuffix(cpuStr, "m") || !strings.HasSuffix(memStr, "Mi") {
			continue
		}
		cpu, _ := strconv.ParseInt(cpuStr[:len(cpuStr)-1], 10, 64)
		mem, _ := strconv.ParseInt(memStr[:len(memStr)-2], 10, 64)
		ret = PodMetric{
			BaseMetric{
				Cluster:    cluster,
				Namespace:  namespace,
				Name:       name,
				CPU:        cpu,
				Memory:     mem,
				CreateTime: now,
			},
		}
		break
	}
	return ret, nil
}

func PodCmd(cluster, namespace, pod, exeCmd string, timeout time.Duration) (string, error) {
	if strings.Contains(exeCmd, "'") {
		return "", errors.New("command don't allow contains \"'\"")
	}
	command := fmt.Sprintf("exec -n %s %s "+
		"-- bash -c '%s'",
		namespace,
		pod,
		exeCmd)
	return CmdWithTimeout(cluster, command, timeout)
}

func Cmd(cluster, param string) (output string, err error) {
	return CmdWithTimeout(cluster, param, 30*time.Second)
}
func CmdWithTimeout(cluster, param string, timeout time.Duration) (output string, err error) {
	if !onlyLetterAndUnderline(cluster) {
		return "", errors.New("cluster name only allow [a-zA-Z0-9_-]")
	}
	command := fmt.Sprintf("%s --kubeconfig %s %s",
		getKubectl(cluster),
		getKubeConfig(cluster),
		param)
	output, err = exec.CommandExecWithTimeout(command, timeout)
	return
}

func getKubeConfig(cluster string) string {
	path := filepath.Join(config.Conf.App.KubeConfDir, cluster)
	return file.AbsPath(path)
}

func getKubectl(cluster string) string {
	version := settings.GetSetting().ClusterVersion(cluster)
	return "kubectl" + version
}

func splitNewLineAndRemoveEmpty(text string) []string {
	items := strings.Split(strings.ReplaceAll(text, "\r\n", "\n"), "\n")
	ret := make([]string, 0, len(items))
	for _, it := range items {
		item := strings.TrimSpace(it)
		if item != "" {
			ret = append(ret, item)
		}
	}
	return ret
}

func onlyLetterAndUnderline(input string) bool {
	regex := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	return regex.MatchString(input)
}

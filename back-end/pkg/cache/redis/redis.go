package redis

import (
	"context"
	"encoding/json"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache/key"
	"github.com/go-redis/redis/v8"
	log "github.com/sirupsen/logrus"
	"time"
)

var ctx = context.Background()
var _client *redis.Client

func cli() *redis.Client {
	if _client == nil {
		_client = redis.NewClient(&redis.Options{
			Addr:     config.Conf.Redis.Addr,
			Password: config.Conf.Redis.Password,
			DB:       config.Conf.Redis.DB,
		})
	}
	return _client
}

func Delete(key string) bool {
	return cli().Del(ctx, key).Err() == nil
}

func SetStr(key string, value string, expire time.Duration) error {
	return cli().Set(ctx, key, value, expire).Err()
}
func GetStr(key string) (string, bool) {
	if ret, err := cli().Get(ctx, key).Result(); err == nil {
		return ret, true
	}
	return "", false
}

func SetStruct[T any](key string, val T, expire time.Duration) error {
	data, err := json.Marshal(val)
	if err != nil {
		return err
	}
	return cli().Set(ctx, key, data, expire).Err()
}

func GetStruct[T any](key string, t T) (T, bool) {
	data, err := cli().Get(ctx, key).Bytes()
	if err != nil {
		return t, false
	}
	if err = json.Unmarshal(data, &t); err != nil {
		log.Warnf("redis data json unmarshal fail, %v", err)
		return t, false
	} else {
		return t, true
	}
}

func ClearAll() {
	iter := cli().Scan(ctx, 0, "", 500).Iterator()
	for iter.Next(ctx) {
		//不要清理Session数据
		if key.Pre().SESSION.Belong(iter.Val()) {
			continue
		}
		err := cli().Del(ctx, iter.Val()).Err()
		if err != nil {
			log.Warnf("Redis Del fail, %v", err)
		}
	}
}

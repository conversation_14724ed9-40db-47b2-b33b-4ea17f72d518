package cache

import (
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache/gocache"
	"fs-k8s-app-manager/pkg/cache/redis"
	log "github.com/sirupsen/logrus"
	"time"
)

//type cache interface {
//	Delete(key string)
//	ClearAll()
//	SetStruct(key string, val interface{}, expire time.Duration)
//	GetStruct[T any](key string, t T) (ret T, found bool)
//}

type Category string

const (
	GoCache Category = "goCache"
	Redis   Category = "redis"
)

func cacheType() Category {
	return Category(config.Conf.App.CacheCategory)
}

func Delete(key string) {
	t := cacheType()
	switch t {
	case GoCache:
		gocache.Delete(key)
	case Redis:
		redis.Delete(key)
	default:
		panic("not support cache type: " + t)
	}
	log.Infof("cache [%s] Delete, key: %s", t, key)
}
func ClearAll() {
	t := cacheType()
	switch t {
	case GoCache:
		gocache.ClearAll()
	case Redis:
		redis.ClearAll()
	default:
		panic("not support cache type: " + t)
	}
	log.Infof("cache [%s] ClearAll", t)
}

func SetStruct(key string, val interface{}, expire time.Duration) (err error) {
	t := cacheType()
	switch t {
	case GoCache:
		err = gocache.SetStruct(key, val, expire)
	case Redis:
		//todo: 是否会存在并发问题,从而导致会话错串的问题？
		err = redis.SetStruct(key, val, expire)
	default:
		panic("not support cache type: " + t)
	}
	log.Infof("cache [%s] SetStruct, key: %s, expire: %s", t, key, expire)
	return
}

func GetStruct[T any](key string, v T) (ret T, found bool) {
	t := cacheType()
	switch t {
	case GoCache:
		ret, found = gocache.GetStruct(key, v)
	case Redis:
		ret, found = redis.GetStruct(key, v)
	default:
		panic("not support cache type: " + t)
	}
	if found {
		log.Infof("cache [%s] GetStruct hit, key: %s", t, key)
	} else {
		log.Infof("cache [%s] GetStruct miss, key: %s", t, key)
	}
	return
}

func SetStr(key string, val string, expire time.Duration) (err error) {
	t := cacheType()
	switch t {
	case GoCache:
		err = gocache.SetStr(key, val, expire)
	case Redis:
		err = redis.SetStr(key, val, expire)
	default:
		panic("not support cache type: " + t)
	}
	log.Infof("cache [%s] SetStr, key: %s, expire: %s", t, key, expire)
	return
}
func GetStr(key string) (ret string, found bool) {
	t := cacheType()
	switch t {
	case GoCache:
		ret, found = gocache.GetStr(key)
	case Redis:
		ret, found = redis.GetStr(key)
	default:
		panic("not support cache type: " + t)
	}
	if found {
		log.Infof("cache [%s] GetStr hit, key: %s", t, key)
	} else {
		log.Infof("cache [%s] GetStr miss, key: %s", t, key)
	}
	return
}

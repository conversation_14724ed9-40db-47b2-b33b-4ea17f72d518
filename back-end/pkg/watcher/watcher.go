package watcher

import (
	"fmt"
	"fs-k8s-app-manager/config/settings"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	log "github.com/sirupsen/logrus"
	core_v1 "k8s.io/api/core/v1"
	meta_v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/watch"
	"os"
	"strings"
	"time"
)

func Setup() {
	if !tryLock() {
		//没有拿到锁，则退出
		return
	}
	clusters := settings.GetSetting().Clusters
	for _, cluster := range clusters {
		//暂且不对专属云做watcher
		if !cluster.IsFxiaokeCloud() {
			continue
		}
		go podWatcher(cluster.Name)
		go eventWatcher(cluster.Name)
	}
}

/*
关于Kubernetes Watch： 当出现某些情况时（比如超时，错误等），watch对应的chan会不能再正常接受到消息，需要做ReConnection，参见网络讨论：
https://github.com/kubernetes/client-go/issues/152
https://github.com/SUSE/eirinix/pull/21
https://github.com/kubernetes/kubernetes/issues/6513
*/

func podWatcher(cluster string) {
	for {
		log.Info(fmt.Sprintf("cluster[%s] pod watcher starting...", cluster))
		podWatcherOnce(cluster)
		time.Sleep(time.Duration(5) * time.Second)
	}
}

func podWatcherOnce(cluster string) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		log.Warn(fmt.Sprintf("cluster[%s] pod watcher fail, %s", cluster, err.Error()))
		return
	}
	timeoutSeconds := int64(1200)
	w, err := c.CoreV1().Pods(core_v1.NamespaceAll).Watch(meta_v1.ListOptions{
		TimeoutSeconds: &timeoutSeconds,
	})
	if err != nil {
		log.Warn(fmt.Sprintf("cluster[%s] pod watcher fail, %s", cluster, err.Error()))
		return
	}
	log.Info(fmt.Sprintf("cluster[%s] pod watcher started", cluster))
	ch := w.ResultChan()
labelReadChannel:
	for {
		select {
		case e, ok := <-ch:
			if !ok {
				break labelReadChannel
			}
			if e.Type == watch.Modified {
				if pod, ok := interface{}(e.Object).(*core_v1.Pod); ok {
					//某些情况下Pod是没有 ContainerStatuses的，测试结果， 比如：Pod创建阶段的部分MODIFIED操作
					if len(pod.Status.ContainerStatuses) > 0 {
						terminalStatus := pod.Status.ContainerStatuses[0].State.Terminated
						//虽然Exit Code为137并不一定就是CGroup OOM Kill，但我们当前的137情况基本都是因为OOM
						if terminalStatus != nil && terminalStatus.ExitCode == 137 {
							//观察发现当容器OOM退出时， 会在很短的时间里接收到两条或两条以上的 modify 消息
							//这里通过缓存一段时间，确保一次OOM只创建一个Event
							//if _, found := watchCache.Get(pod.Name); found {
							//	continue
							//}
							log.Debug("Pod exit: ", pod.Name, terminalStatus.ExitCode, terminalStatus.Reason)
							var reason, message string
							if strings.Contains(strings.ToLower(terminalStatus.Reason), "oom") {
								reason = "ContainerOOM"
								message = "container oom and restart"
							} else {
								reason = "ContainerKilled"
								message = fmt.Sprintf("container was killed, kill signal: [SIGKILL]. exit code: %d, reason: %s", terminalStatus.ExitCode, terminalStatus.Reason)
							}
							//todo: 当pod已经被删除了的话，会出现 pods xxx not found
							if err := podExitEvent(cluster, pod.Namespace, pod.Name, reason, message); err != nil {
								log.Warn("podExitEvent create err: ", err.Error())
							} else {
								log.Info(fmt.Sprintf("podExitEvent create success! [ns|pod|reason|message]: %s|%s|%s|%s", pod.Namespace, pod.Name, reason, message))
							}
						}
					}
				}
			}
		}
	}
	log.Info(fmt.Sprintf("cluster[%s] pod watcher exit", cluster))
	w.Stop()
}
func podExitEvent(cluster, namespace, pod, reason, message string) (err error) {

	event := dto.Event{
		Namespace:          namespace,
		InvolvedObjectType: "Pod",
		InvolvedObjectName: pod,
		Type:               dto.EventTypeWarning,
		Reason:             reason,
		Message:            message,
		Action:             "Restart",
	}
	err = k8s_service.CreateEvent(cluster, &event)
	return
}

func eventWatcher(cluster string) {
	for {
		log.Info(fmt.Sprintf("cluster[%s] event watcher starting...", cluster))
		eventWatcherOnce(cluster)
		time.Sleep(time.Duration(5) * time.Second)
	}
}

func eventWatcherOnce(cluster string) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		log.Warn(fmt.Sprintf("cluster[%s] event watcher fail, %s", cluster, err.Error()))
		return
	}
	timeoutSeconds := int64(1200)
	w, err := c.CoreV1().Events(core_v1.NamespaceAll).Watch(meta_v1.ListOptions{
		TimeoutSeconds: &timeoutSeconds,
	})
	if err != nil {
		log.Warn(fmt.Sprintf("cluster[%s] event watcher fail, %s", cluster, err.Error()))
		return
	}
	log.Info(fmt.Sprintf("cluster[%s] event watcher started", cluster))
	ch := w.ResultChan()
labelReadChannel:
	for {
		select {
		case e, ok := <-ch:
			if !ok {
				break labelReadChannel
			}
			if e.Type == watch.Added {
				if event, ok := interface{}(e.Object).(*core_v1.Event); ok {
					//忽略2分钟之前的事件，避免重复消费
					if event.FirstTimestamp.Time.Before(time.Now().Add(-time.Minute * 2)) {
						continue
					}
					ns := event.InvolvedObject.Namespace
					app := event.InvolvedObject.Name
					//fs-k8s-scaler的扩缩容事件
					if strings.EqualFold(event.Reason, "AutoScaleUp") {
						_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
							Cluster:      cluster,
							Profile:      ns,
							Creator:      "自动扩缩容",
							ResourceType: "app",
							ResourceId:   app,
							Title:        "服务自动缩容V2",
							Message:      event.Message,
							Level:        "info",
							Extra:        "",
						})
						event_service.CreateBySys(event_service.BuildAppKey(cluster, ns, app), "【自动扩缩容V2】自动扩容，"+event.Message)
					} else if strings.EqualFold(event.Reason, "AutoScaleDown") {
						_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
							Cluster:      cluster,
							Profile:      ns,
							Creator:      "自动扩缩容",
							ResourceType: "app",
							ResourceId:   app,
							Title:        "服务自动扩容V2",
							Message:      event.Message,
							Level:        "info",
							Extra:        "",
						})
						event_service.CreateBySys(event_service.BuildAppKey(cluster, ns, app), "【自动扩缩容V2】自动缩容，"+event.Message)
					}
				}
			}
		}
	}
	log.Info(fmt.Sprintf("cluster[%s] pod watcher exit", cluster))
	w.Stop()
}

func tryLock() bool {
	lockKey := key.Pre().WATCHER.Key("lockKey")
	//获取锁
	if _, found := cache.GetStr(lockKey); found {
		return false
	}
	val, _ := os.Hostname()
	if val == "" {
		val = "unknown"
	}
	//加锁
	cache.SetStr(lockKey, val, 2*time.Minute)
	return true
}

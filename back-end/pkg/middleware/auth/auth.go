package auth

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"github.com/gin-gonic/gin"
	"github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"net/http"
	"strings"
	"time"
)

const sessionCookie = "fs_k8s_app_manager_token"
const sessionExpire = 5 * time.Hour

var ignorePathsMap = make(map[string]bool)

var authRequiredFunc = func(c *gin.Context) {
	if _, ok := ignorePathsMap[c.FullPath()]; ok {
		c.Next()
		return
	}
	//Authentication 身份认证
	if _, found := GetUser(c); !found {
		data := make(map[string]interface{})
		data["code"] = 401
		data["message"] = "没有登录"
		data["data"] = GetLoginUrl(c.Request.Host)
		c.<PERSON>(http.StatusOK, data)
		c.Abort()
		return
	}
	c.Next()
}

func BuildAuthRequired(ignorePaths ...string) func(*gin.Context) {
	for _, it := range ignorePaths {
		ignorePathsMap[it] = true
	}
	return authRequiredFunc
}

func GetLoginUrl(serverHost string) string {
	if strings.Contains(serverHost, ":") {
		serverHost = serverHost[0:strings.Index(serverHost, ":")]
	}
	//port := config.Conf.App.HttpPort
	schema := "https"
	if serverHost == "localhost" || serverHost == "127.0.0.1" {
		//本地开发环境基本都是使用http
		schema = "http"
	}
	service := fmt.Sprintf("%s://%s/api/shiro-cas", schema, serverHost)
	return fmt.Sprintf("%s?service=%s", config.Conf.CAS.LoginPath, service)
}

func AddUserSession(c *gin.Context, user models.User) {
	token := uuid.NewV4().String() + "--" + time.Now().Format("20060102150405")
	if err := cache.SetStruct(key.Pre().SESSION.Key(token), user, sessionExpire); err != nil {
		log.Info("add user session fail, %v", err)
		fmt.Printf("add user session fail, %v", err)
	}
	//httpOnly一定要设置为false, 否则当前端需要cookie时，无法用js获取到。
	c.SetCookie(sessionCookie, token, 4*3600, "/", "", false, false)
}

func GetUser(c *gin.Context) (ret models.User, found bool) {
	return cache.GetStruct(key.Pre().SESSION.Key(getTokenFromCookie(c)), models.User{})
}
func UpdateUserInfo(c *gin.Context, user models.User) error {
	if token := getTokenFromCookie(c); token != "" {
		cache.SetStruct(key.Pre().SESSION.Key(token), user, sessionExpire)
		return nil
	} else {
		return errors.New("can't found token from cookie")
	}
}

//func GetUserFullName(c *gin.Context) string {
//	if u, f := GetUser(c); f {
//		return u.FullName()
//	}
//	return ""
//}

func GetUsername(c *gin.Context) string {
	if u, f := GetUser(c); f {
		return u.Username
	}
	return ""
}

func GetRealName(c *gin.Context) string {
	if u, f := GetUser(c); f {
		return u.RealName
	}
	return ""
}

func DeleteUserInfo(c *gin.Context) {
	if token := getTokenFromCookie(c); token != "" {
		cache.Delete(key.Pre().SESSION.Key(token))
	}
}

func getTokenFromCookie(c *gin.Context) string {
	if t, err := c.Cookie(sessionCookie); err != nil {
		log.Warn("get token cookie fail ", err)
		return ""
	} else {
		return t
	}
}

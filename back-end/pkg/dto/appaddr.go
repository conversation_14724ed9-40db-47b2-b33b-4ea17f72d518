package dto

import "k8s.io/apimachinery/pkg/util/intstr"

type AppAddress struct {
	Name                string             `json:"name"`
	Protocol            string             `json:"protocol"`
	Port                int32              `json:"port"`
	TargetPort          intstr.IntOrString `json:"targetPort"`
	NodePort            int32              `json:"nodePort"`
	ClusterInnerAddress []string           `json:"clusterInnerAddress"`
	ClusterOuterAddress []string           `json:"clusterOuterAddress"`
	Remark              string             `json:"remark"`
}

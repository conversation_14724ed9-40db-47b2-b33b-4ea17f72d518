package dto

import (
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/service/cmdb_service"
	"gorm.io/gorm"
)

type App struct {
	ID             uint                 `json:"id" form:"id"`
	Name           string               `json:"name" form:"name" binding:"required"`
	Level          string               `json:"level" form:"level"`
	Orgs           []string             `json:"orgs" form:"org" `
	Owner          string               `json:"owner" form:"owner"`
	Remark         string               `json:"remark" form:"remark"`
	Admins         []string             `json:"admins" form:"admins"`
	CreatedTime    *Time                `json:"createdTime"`
	UpdatedTime    *Time                `json:"updatedTime"`
	TimeWindow     datatype.TimePeriods `json:"timeWindow" form:"timeWindow"`
	TimeWindowDesc string               `json:"timeWindowDesc"`
}

func (my *App) ToModel() models.App {
	return models.App{
		Model: gorm.Model{
			ID: my.ID,
		},
		Name:       my.Name,
		Level:      my.Level,
		Orgs:       my.Orgs,
		Admins:     my.Admins,
		Remark:     my.Remark,
		TimeWindow: my.TimeWindow,
	}
}
func (my *App) ParseByModel(entity models.App) {
	my.ID = entity.ID
	my.Name = entity.Name
	my.Level = entity.Level
	my.Orgs = entity.Orgs
	my.Admins = entity.Admins
	my.Owner = cmdb_service.GetMainOwnerNames(entity.Name, ",")
	my.Remark = entity.Remark
	my.CreatedTime = NewTimePointer(entity.CreatedAt)
	my.UpdatedTime = NewTimePointer(entity.UpdatedAt)
	my.TimeWindow = entity.TimeWindow
	my.TimeWindowDesc = entity.TimeWindow.Desc()
}

func ToAppList(entities []models.App) []App {
	ret := make([]App, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item := App{}
			item.ParseByModel(it)
			ret = append(ret, item)
		}
	}
	return ret
}
func ToApp(entity models.App) App {
	ret := App{}
	ret.ParseByModel(entity)
	return ret
}

package dto

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

type TempAuth struct {
	ID          uint   `json:"id"`
	App         string `json:"app"`
	User        string `json:"user"`
	Operate     string `json:"operate"`
	OperateDesc string `json:"operateDesc"`
	Approver    string `json:"approver"`
	Approved    bool   `json:"approved"`
	Date        string `json:"date"`
	StartHour   string `json:"startHour"`
	EndHour     string `json:"endHour"`
	Authorizer  string `json:"authorizer"`
	Remark      string `json:"remark"`
	CreatedTime Time   `json:"createdTime"`
}

func (my *TempAuth) ToModel() models.TempAuth {
	return models.TempAuth{
		Model: gorm.Model{
			ID: my.ID,
		},
		App:        my.App,
		User:       my.User,
		Operate:    my.Operate,
		Approver:   my.Approver,
		Approved:   my.Approved,
		StartHour:  my.StartHour,
		EndHour:    my.EndHour,
		Authorizer: my.Authorizer,
		Remark:     my.Remark,
	}
}

func (my *TempAuth) ParseByModel(entity models.TempAuth) {
	my.ID = entity.ID
	my.App = entity.App
	my.User = entity.User
	my.Operate = entity.Operate
	my.StartHour = entity.StartHour
	my.Approver = entity.Approver
	my.Approved = entity.Approved
	my.EndHour = entity.EndHour
	my.Authorizer = entity.Authorizer
	my.Remark = entity.Remark
	my.OperateDesc = entity.OperateDesc()
	my.Date = entity.CreatedAt.Format("2006-01-02")
	my.CreatedTime = NewTime(entity.CreatedAt)
}

func ToTempAuthList(entities []models.TempAuth) []TempAuth {
	ret := make([]TempAuth, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item := TempAuth{}
			item.ParseByModel(it)
			ret = append(ret, item)
		}
	}
	return ret
}

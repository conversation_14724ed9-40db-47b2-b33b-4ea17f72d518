<template>
  <div class="pod-draft-container">
    <el-card class="box-card" :body-style="{padding: '0px 5px'}">
      <div slot="header" class="clearfix">
        <span class="card-panel-text">实例漂移</span>
        <el-tooltip>
          <template slot="content">
            <div style="line-height: 1.5em;">
              实例漂移定义： 实例配置了专用资源池， 但却没有运行在专用资源池上<br/>
              <b>配置资源池：</b>发布流程下配置的资源池<br/>
              <b>运行资源池：</b>实例当前所在的资源池
            </div>
          </template>
          <i class="el-icon-info help-text-icon"></i>
        </el-tooltip>

        <el-switch
          @change="onChange"
          style="float: right"
          v-model="showAllPods"
          active-color="#13ce66"
          active-text="所有"
          inactive-text="访问过的应用">
        </el-switch>
        <el-select v-model="cluster" placeholder="请选择集群" size="mini" style="float: right;margin-right: 20px;">
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </div>
      <div class="component-item">
        <div :class="className" :style="{height:height,width:width}">
          <el-table
            :data="tableData"
            v-loading="tableLoading"
            :max-height="300"
            style="width: 100%">
            <el-table-column
              prop="pod"
              min-width="260"
              label="实例">
            </el-table-column>
            <el-table-column
              prop="namespace"
              label="环境">
            </el-table-column>
            <el-table-column
              prop="shouldPool"
              label="配置资源池">
            </el-table-column>
            <el-table-column
              prop="runPool"
              label="运行资源池">
            </el-table-column>
            <el-table-column
              label=""
              fixed="right" width="180px">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="pipelinePage(scope.row)">发布流程
                </el-button>
                <el-button
                  type="text"
                  @click="podPage(scope.row)">实例管理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>

</template>

<script>
import {getPodDraft} from "@/api/dashboard";
import {getUserInfo} from "@/api/user";

export default {
  props: {
    className: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: true,
      showAllPods: false,
      cluster:"k8s0",
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    }
  },
  methods: {
    loadData() {
      this.tableLoading = true
      getPodDraft(this.cluster).then(rep1 => {
        if (this.showAllPods) {
          this.tableData = rep1.data
        }
        if (!this.showAllPods) {
          getUserInfo().then(rep2 => {
            let pods = []
            for (let it of rep1.data) {
              if (rep2.data.recentApps.includes(it.app)) {
                pods.push(it)
              }
            }
            this.tableData = pods
          }).catch((e) => {
            this.$message.error(e.message())
          })
        }
      }).catch((e) => {
        this.$message.error(e.message())
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
    podPage(row) {
      let p = {
        "cluster": row.cluster,
        "namespace": row.namespace,
        "app": row.app,
      };
      this.$router.push({name: 'pod-index', query: p});
    },
    onChange(val) {
      this.loadData()
    }
  }
}
</script>

<style>

.pod-draft-container .el-table td  {
  padding: 5px 0;
}
</style>

<template>
  <div class="dashboard-container">
    <el-row :gutter="20" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="todo">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="alert" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              告警 （当天）
            </div>
            <div style="font-size: 20px;">todo</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="deployHistoryPage">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="guide" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              我的发布历史
            </div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="todo">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="list" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              发布执行计划
            </div>
            <div style="font-size: 20px;">todo</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="todo">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="unlink" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              摘除的实例
            </div>
            <div style="font-size: 20px;">todo</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="panel-group">
      <el-col :xs="24" :sm="24" :lg="24">
        <div style="position: relative;">
          <el-button type="text" icon="el-icon-full-screen"
                     @click="fullScreen('appDeployStatus')"
                     style="position: absolute;top:10px;right:20px;z-index: 999">全屏
          </el-button>
          <app-deploy-status height="300px" table-height="300"/>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="panel-group">
      <el-col :xs="24" :sm="8" :lg="8">
        <history-app height="300px"/>
      </el-col>
      <el-col :xs="24" :sm="16" :lg="16">
        <pod-draft height="300px"/>
      </el-col>
    </el-row>

    <el-row class="panel-group">
      <el-col :xs="24" :sm="24" :lg="24">
        <resource-pool-usage height="500px"/>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="panel-group">
      <el-col :xs="24" :sm="8" :lg="8">
        <div class="chart-wrapper">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span class="card-panel-text">负责应用资源使用情况 （todo)</span>
            </div>
            <div class="component-item">
              <pie-chart height="300px"/>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <el-dialog
      :visible.sync="appDeployStatusFullScreen"
      width="30%"
      :fullscreen="true"
      center>
      <app-deploy-status height="100%" table-height="100%"/>
    </el-dialog>
  </div>
</template>

<script>
import PieChart from "./components/PieChart";
import ResourcePoolUsage from "./components/ResourcePoolUsage";
import HistoryApp from "./components/HistoryApp";
import {getDashboardData} from "@/api/dashboard";
import PodDraft from "@/views/dashboard/components/PodDraft";
import AppDeployStatus from "@/views/dashboard/components/AppDeployStatus";

export default {
  data() {
    return {
      appDeployStatusFullScreen: false
    }
  },
  components: {
    AppDeployStatus,
    PodDraft,
    PieChart,
    ResourcePoolUsage,
    HistoryApp
  },
  mounted() {
  },
  computed: {
    user: function () {
      return this.$store.state.user
    },
  },
  methods: {
    loadData() {
      getDashboardData().then(response => {
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
      })
    },
    deployHistoryPage() {
      this.$router.push({
        name: 'app-pipeline-execution-history', query: {
          "keyword": this.user.realName,
        }
      });
    },
    fullScreen(view) {
      if (view === "appDeployStatus") {
        this.appDeployStatusFullScreen = true;
      } else {
        this.$message.warning("unknown view name")
      }
    },
    todo() {
      this.$message.info("todo")
    },
    alertPage() {
      this.$message.info("todo")
    },
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 10px 20px;
  background-color: rgb(240, 242, 245);
  position: relative;
}

.panel-group {
  margin-top: 10px;
}

.panel-group .card-panel-col {
  margin-bottom: 5px;
}

.panel-group .card-panel {
  height: 108px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.05);
}

.panel-group .card-panel:hover .card-panel-icon-wrapper {
  color: #fff;
}

.panel-group .card-panel:hover .icon-people {
  background: #40c9c6;
}

.panel-group .card-panel:hover .icon-message {
  background: #36a3f7;
}

.panel-group .card-panel:hover .icon-money {
  background: #fc8452;
}

.panel-group .card-panel:hover .icon-shopping {
  background: #34bfa3;
}

.panel-group .card-panel .icon-people {
  color: #40c9c6;
}

.panel-group .card-panel .icon-message {
  color: #36a3f7;
}

.panel-group .card-panel .icon-money {
  color: #fc8452;
}

.panel-group .card-panel .icon-shopping {
  color: #34bfa3;
}

.panel-group .card-panel .card-panel-icon-wrapper {
  float: left;
  margin: 14px 0 0 14px;
  padding: 16px;
  transition: all 0.38s ease-out;
  border-radius: 6px;
}

.panel-group .card-panel .card-panel-icon {
  float: left;
  font-size: 48px;
}

.panel-group .card-panel .card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px;
  margin-left: 0px;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
  font-weight: bold;
}

.panel-group .card-panel .card-panel-description .card-panel-num {
  font-size: 20px;
}

@media (max-width: 550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;
  }

  .card-panel-icon-wrapper .svg-icon {
    display: block;
    margin: 14px auto !important;
    float: none !important;
  }
}

</style>



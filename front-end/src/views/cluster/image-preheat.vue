<template>
  <div class="image-preheat" v-loading="loading">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">{{ this.cluster }}</span>
      </div>
      <div>
        <el-table
          :data="this.tableData"
          row-key="name"
          size="small"
        >
          <el-table-column label="名称" prop="metadata.name">
            <template slot-scope="scope">
              <div title="点击查看yaml配置">
                <el-link  type="text" @click="podDetailDialog(scope.row.metadata.name)"
                           style="font-size: 12px;margin: 0;padding: 0;color: #3a8ee6">
                  {{ scope.row.metadata.name }}
                </el-link>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status.phase" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status.phase === 'Succeeded'" type="success" size="mini" style="font-weight: bold;">
                {{ scope.row.status.phase }}
              </el-tag>
              <el-tag v-else type="warning" size="small" style="font-weight: bold;">
                {{ scope.row.status.phase }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="metadata.creationTimestamp" width="90">
          </el-table-column>
          <el-table-column label="" width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="podStdoutLog(scope.row.metadata.name)">日志</el-button>
              <el-button type="text" @click="jobRemove(scope.row.metadata.labels['job-name'])" style="color:#E6A23C">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <el-dialog
      title="容器启动日志（标准输出)"
      :visible.sync="podStdoutVisible"
      top="5vh"
      :close-on-click-modal="false"
      @close="podStdout.pod = null"
      width="70%"
      center>
      <div style="margin-top: -30px">
        <pod-stdout :cluster="this.podStdout.cluster" :namespace="this.podStdout.namespace" :pod="this.podStdout.pod" :containers="this.podStdout.containers"></pod-stdout>
      </div>
    </el-dialog>

    <el-dialog
      :title="podDetail.name"
      :visible.sync="podDetailVisible"
      top="5vh">
      <div style="margin: 5px auto;text-align: center;">
        <clipboard-icon :text="podDetail.json">一键复制</clipboard-icon>
      </div>
      <vue-json-pretty
        v-loading="podDetailLoading"
        :data="podDetail.data"
        style="max-height: 600px; overflow-y: auto"
      >
      </vue-json-pretty>
    </el-dialog>
  </div>
</template>
<script>

import {deleteImagePreheatJob, getImagePreheatJob} from "@/api/k8s/cluster";
import PodStdout from "@/views/components/pod-stdout.vue";
import {getPodDetail, podDelete} from "@/api/k8s/pod";
import VueJsonPretty from 'vue-json-pretty'
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  name: "imagePreheat",
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      default: "kube-public"
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      podStdoutVisible: false,
      podStdout: {
        cluster: "",
        namespace: "",
        pod: "",
        containers: []
      },
      podDetailVisible: false,
      podDetailLoading: false,
      podDetail: {
        name: "",
        data: {},
        json: "",
      },
    }
  },
  components: {ClipboardIcon, PodStdout, VueJsonPretty},
  mounted() {
    this.loadTable();
  },
  methods: {
    loadTable() {
      this.loading = true;
      getImagePreheatJob(this.cluster, this.namespace).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false;
      })
    },
    podDetailDialog(pod) {
      this.podDetailVisible = true;
      if (this.podDetail.name === pod && this.podDetail.data) {
        return;
      }
      this.podDetailLoading = true;
      getPodDetail(this.cluster, this.namespace, pod).then(response => {
        this.podDetailLoading = false;
        this.podDetail.name = pod;
        this.podDetail.data = response.data;
        this.podDetail.json = JSON.stringify(this.podDetail.data, null, 2);
      }).catch((e) => {
        this.$message.error(e.message);
        this.podDetailLoading = false;
      })
    },
    podStdoutLog(pod) {
      this.podStdoutVisible = true
      this.podStdout.cluster = this.cluster
      this.podStdout.namespace = this.namespace
      this.podStdout.pod = pod
      //todo: 补充容器信息
      // let podObj = this.tableData.find(item => item.metadata.name === pod)
      this.podStdout.containers = []
    },
    jobRemove(job) {
      this.$confirm(`是否确认删除job：${job} ?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteImagePreheatJob(this.cluster, this.namespace, job).then(response => {
          this.$message.success('操作成功,2秒后将刷新数据');
          let vthis = this;
          setTimeout(() => {
            vthis.loadTable();
          }, 3000);
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading = false;
        })
      }).catch((e) => {

      });
    },
  }
}
</script>

<style>
.image-preheat .el-card__header {
  background-color: #ccc;
}
</style>


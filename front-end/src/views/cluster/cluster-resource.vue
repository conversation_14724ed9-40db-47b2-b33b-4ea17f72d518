<template>
  <div v-loading="loading">
    <div>
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
        <el-form-item label="K8S集群">
          <el-select v-model="searchForm.cluster" placeholder="请选择一个集群" filterable>
            <el-option
              v-for="item in this.clusterOptions"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="节点类型">
          <el-select v-model="searchForm.nodeType" placeholder="请选择节点类型" >
            <el-option
              v-for="item in this.nodeOptions"
              :key="item.name"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
          <export-button :table-ref="this.$refs.table001"></export-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      ref="table001"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      :row-class-name="tableRowClassName"
      :default-sort="{prop: 'cpu.requests', order: 'descending'}"
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="宿主机" sortable prop="name">
      </el-table-column>
      <el-table-column label="专属类型" align="center" prop="dedicatedName">
        <template slot-scope="scope">
          <el-tag type="info">{{ scope.row.dedicatedName || "--" }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="CPU" align="center">
        <el-table-column
          label="请求" align="center" prop="cpu.requests" sortable :sort-method="(a, b) => strValueSort(a.cpu.requests, b.cpu.requests)">
        </el-table-column>
        <el-table-column
          label="请求百分比" align="center" prop="cpu.requestsPercent" sortable :sort-method="(a, b) => strValueSort(a.cpu.requestsPercent, b.cpu.requestsPercent)">
        </el-table-column>
        <el-table-column
          label="最大" align="center" prop="cpu.limits" sortable :sort-method="(a, b) => strValueSort(a.cpu.limits, b.cpu.limits)">
        </el-table-column>
        <el-table-column
          label="最大百分比" align="center" prop="cpu.limitsPercent" sortable :sort-method="(a, b) => strValueSort(a.cpu.limitsPercent, b.cpu.limitsPercent)">
        </el-table-column>
        <el-table-column
          label="当前使用率" align="center" prop="cpu.utilizationPercent" sortable
          :sort-method="(a, b) => strValueSort(a.cpu.utilizationPercent, b.cpu.utilizationPercent)">
        </el-table-column>
      </el-table-column>
      <el-table-column label="内存" align="center">
        <el-table-column
          label="请求" align="center" prop="memory.requests" sortable :sort-method="(a, b) => strValueSort(a.memory.requests, b.memory.requests)">
        </el-table-column>
        <el-table-column
          label="请求百分比" align="center" prop="memory.requestsPercent" sortable
          :sort-method="(a, b) => strValueSort(a.memory.requestsPercent, b.memory.requestsPercent)">
        </el-table-column>
        <el-table-column
          label="最大" align="center" prop="memory.limits" sortable :sort-method="(a, b) => strValueSort(a.memory.limits, b.memory.limits)">
        </el-table-column>
        <el-table-column
          label="最大百分比" align="center" prop="memory.limitsPercent" sortable
          :sort-method="(a, b) => strValueSort(a.memory.limitsPercent, b.memory.limitsPercent)">
        </el-table-column>
        <el-table-column
          label="当前使用率" align="center" prop="memory.utilizationPercent" sortable
          :sort-method="(a, b) => strValueSort(a.memory.utilizationPercent, b.memory.utilizationPercent)">
        </el-table-column>
      </el-table-column>

      <el-table-column
        label=""
        fixed="right" width="100px">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.name !== '所有'"
            type="text"
            @click="showPodCapacity(scope.row.name)">查看Pod
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <el-drawer
      :withHeader="false"
      :visible.sync="podCapacity.show"
      direction="btt"
      size="500"
    >
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>宿主机：{{ podCapacity.node }}</span>
        </div>
        <div>
          <el-table
            v-loading="podCapacity.loading"
            :data="podCapacity.data"
            element-loading-text="Loading"
            border
            height="500"
            fit
            highlight-current-row
            :default-sort="{prop: 'cpu.requestsPercent', order: 'descending'}"
          >
            <el-table-column type="index">
            </el-table-column>
            <el-table-column label="Pod" sortable prop="name">
            </el-table-column>
            <el-table-column label="环境" sortable prop="namespace">
            </el-table-column>

            <el-table-column label="CPU" align="center">
              <el-table-column
                label="请求" align="center" prop="cpu.requests" sortable :sort-method="(a, b) => strValueSort(a.cpu.requests, b.cpu.requests)">
              </el-table-column>
              <el-table-column
                label="请求百分比" align="center" prop="cpu.requestsPercent" sortable
                :sort-method="(a, b) => strValueSort(a.cpu.requestsPercent, b.cpu.requestsPercent)">
              </el-table-column>
              <el-table-column
                label="最大" align="center" prop="cpu.limits" sortable :sort-method="(a, b) => strValueSort(a.cpu.limits, b.cpu.limits)">
              </el-table-column>
              <el-table-column
                label="最大百分比" align="center" prop="cpu.limitsPercent" sortable :sort-method="(a, b) => strValueSort(a.cpu.limitsPercent, b.cpu.limitsPercent)">
              </el-table-column>
              <el-table-column
                label="当前使用率" align="center" prop="cpu.utilizationPercent" sortable
                :sort-method="(a, b) => strValueSort(a.cpu.utilizationPercent, b.cpu.utilizationPercent)">
              </el-table-column>
            </el-table-column>
            <el-table-column label="内存" align="center">
              <el-table-column
                label="请求" align="center" prop="memory.requests" sortable :sort-method="(a, b) => strValueSort(a.memory.requests, b.memory.requests)">
              </el-table-column>
              <el-table-column
                label="请求百分比" align="center" prop="memory.requestsPercent" sortable
                :sort-method="(a, b) => strValueSort(a.memory.requestsPercent, b.memory.requestsPercent)">
              </el-table-column>
              <el-table-column
                label="最大" align="center" prop="memory.limits" sortable :sort-method="(a, b) => strValueSort(a.memory.limits, b.memory.limits)">
              </el-table-column>
              <el-table-column
                label="最大百分比" align="center" prop="memory.limitsPercent" sortable
                :sort-method="(a, b) => strValueSort(a.memory.limitsPercent, b.memory.limitsPercent)">
              </el-table-column>
              <el-table-column
                label="当前使用率" align="center" prop="cpu.utilizationPercent" sortable
                :sort-method="(a, b) => strValueSort(a.memory.utilizationPercent, b.memory.utilizationPercent)">
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-drawer>
  </div>
</template>


<style>
.el-table .bold-row {
  font-weight: bold;
}

</style>

<script>

import {nodeCapacity, podCapacity} from "@/api/k8s/cluster";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "clusterResource",
  data() {
    return {
      loading:false,
      tableData: [],
      searchForm: {
        cluster: "",
        nodeType: ""
      },
      podCapacity: {
        show: false,
        loading: false,
        node: "",
        data: [],
      }
    }
  },
  components: {
    ExportButton
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    nodeOptions: function () {
      if (this.searchForm.cluster) {
        for (let i in this.$settings.clusters) {
          let curr = this.$settings.clusters[i];
          if (this.searchForm.cluster === curr.name) {
            let all = {
              "name": "全部",
              "value": ''
            }
            let nodes = JSON.parse(JSON.stringify(curr.nodes));
            nodes.splice(0, 0, all)
            console.log(nodes)
            return nodes;
          }
        }
      }

      return []
    },
  },
  mounted() {
  },
  methods: {
    loadTableData() {
      if (!this.searchForm.cluster) {
        return
      }
      this.loading = true;
      nodeCapacity(this.searchForm).then(response => {
        this.tableData = response.data.nodes || [];
        let clu = response.data.clusterTotals;
        if (clu) {
          clu["name"] = "所有"
          this.tableData.unshift(clu)
        }
        for (let it of this.tableData) {
          this.rowValueHandler(it);
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false;
      })
    },
    tableRowClassName({row, rowIndex}) {
      if (row.name === '所有') {
        return 'bold-row';
      }
      return '';
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
    rowValueHandler(row) {
      function cpuHandler(v) {
        if (/^\d+$/.test(v)) {
          return (parseInt(v) * 1000) + "m"
        }
        return v
      }

      function memHandler(v) {
        if (/^\d+$/.test(v)) {
          return Math.floor((parseInt(v) / 1024 / 1024)) + "Mi"
        } else if (v.endsWith("Ki") || v.endsWith("ki")) {
          return Math.floor((parseInt(v) / 1024)) + "Mi"
        }
        return v
      }

      //统一单位
      row["cpu"]["requests"] = cpuHandler(row["cpu"]["requests"])
      row["cpu"]["limits"] = cpuHandler(row["cpu"]["limits"])
      row["memory"]["requests"] = memHandler(row["memory"]["requests"])
      row["memory"]["limits"] = memHandler(row["memory"]["limits"])
    },
    showPodCapacity(node) {
      if (!node) {
        this.$message.error("请选择一个宿主机")
        return
      }
      this.podCapacity.node = node;
      this.podCapacity.loading = true;
      this.podCapacity.show = true;
      let params = {
        cluster: this.searchForm.cluster,
        node: node,
      }
      podCapacity(params).then(response => {
        this.podCapacity.data = response.data.nodes[0].pods;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.podCapacity.loading = false;
      })
    },
    strValueSort(a, b) {
      return parseInt(a) - parseInt(b);
    },
    podPage(row) {
      let p = {
        "cluster": row.cluster,
        "namespace": row.namespace,
        "app": row.app,
      };
      this.$router.push({name: 'pod-index', query: p});
    }
  }
}
</script>


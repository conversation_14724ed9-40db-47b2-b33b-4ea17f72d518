<template>
  <div>
    <el-row>
      <el-col :span="colSpan" v-for="clu in clusterOptions" :key="clu.name">
        <image-preheat :cluster="clu.name"></image-preheat>
      </el-col>
    </el-row>
  </div>
</template>
<script>

import ImagePreheat from "@/views/cluster/image-preheat.vue";

export default {
  name: "imagePreheatList",
  data() {
    return {
      span: 8
    }
  },
  components: {ImagePreheat},
  mounted() {
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    colSpan: function () {
      if (this.clusterOptions.length > 6) {
        return 8
      }
      return 12
    }
  },
  methods: {}
}
</script>


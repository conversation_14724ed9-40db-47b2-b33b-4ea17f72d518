<template>
  <div>
    <div>
      <el-row>
        <el-col :span="4">
          <div>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="createPage">新建</el-button>
          </div>
        </el-col>
        <el-col :span="20">
          <div style="text-align: right;">
            <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
              <el-form-item label="服务等级" style="margin-bottom: 0;">
                <el-select v-model="searchForm.level" placeholder="请选择" style="width: 100%;">
                  <el-option label="所有" value=""></el-option>
                  <el-option label="L0-底层公共服务" value="L0"></el-option>
                  <el-option label="L1-核心业务服务" value="L1"></el-option>
                  <el-option label="L2-一般业务服务" value="L2"></el-option>
                  <el-option label="L3-非业务服务" value="L3"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" style="margin-bottom: 0;">
                <el-input v-model="searchForm.keyword" clearable style="width: 360px;"></el-input>
              </el-form-item>
              <el-form-item style="margin-bottom: 0;">
                <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.limit"
      layout="total,prev,pager,next"
      :total="tableData.count"
      @current-change="PageChange">
    </el-pagination>
    <el-table
      v-loading="tableLoading"
      :data="tableData.data"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名"
                       prop="name">
      </el-table-column>
      <el-table-column label="服务等级"
                       prop="level" width="100" align="center">
      </el-table-column>
      <!--      <el-table-column label="所属部门"-->
      <!--                       prop="org">-->
      <!--      </el-table-column>-->
      <el-table-column label="描述" prop="remark">
      </el-table-column>
      <el-table-column label="创建时间"
                       width="140px"
                       prop="createdTime">
      </el-table-column>
      <el-table-column label="发版权限" align="center" width="120px">
        <template slot-scope="scope">
          <template v-if="scope.row.orgs && scope.row.orgs.length > 0">
            <i class="el-icon-circle-check" style="color: #67c23a;font-weight: bold;font-size: 20px;"></i><br/>
            <el-tooltip effect="dark" :content="'部门：' + scope.row.orgs.join(',')" placement="top">
              <el-button type="text" style="font-size: 12px;padding: 0;">查看</el-button>
            </el-tooltip>
          </template>
          <template v-else>
            --<br/>
          </template>
          <el-button
            type="text"
            style="font-size: 12px;padding: 0;"
            @click="permPage(scope.row)">修改
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="发版窗口" align="center" show-overflow-tooltip width="120px">
        <template slot-scope="scope">
          <template v-if="scope.row.timeWindow && scope.row.timeWindow.length > 0">
            <i class="el-icon-circle-check" style="color: #67c23a;font-weight: bold;font-size: 20px;"></i><br/>
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <pre>{{ scope.row.timeWindowDesc }}</pre>
              </div>
              <el-button type="text" style="font-size: 12px;padding: 0;">查看</el-button>
            </el-tooltip>
          </template>
          <template v-else>
            --<br/>
          </template>
          <el-button
            type="text"
            style="font-size: 12px;padding: 0;"
            @click="editPage(scope.row.name)">修改
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="管理员" prop="owner" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.admins && scope.row.admins.length > 0" style="font-size: 12px;">
            {{ scope.row.admins.join(',') }}<br/>
          </div>
          <div v-else>
            --<br/>
          </div>
          <el-button
            type="text"
            style="font-size: 12px;padding: 0;"
            @click="editPage(scope.row.name)">修改
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="负责人" prop="owner">
      </el-table-column>
      <el-table-column label="操作"
                       width="220px"
                       fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="editPage(scope.row.name)">修改
          </el-button>
          <el-popconfirm :title="'确定要删除【 ' + scope.row.name + ' 】吗？'" @confirm="deleteApp(scope.$index, scope.row)">
            <el-button
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
          <el-button
            type="text"
            icon="el-icon-s-custom"
            @click="permPage(scope.row)">权限管理
          </el-button>
          <el-button
            type="text"
            icon="el-icon-position"
            style="display: none"
            @click="pipelinePage(scope.row)">发布流程
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="dialogEditTitle" :visible.sync="dialogEditVisible" width="900px" :close-on-click-modal="false">
      <el-form :model="dialogEditForm" ref="dialogEditForm" label-width="120px" :rules="dialogEditFormRules">
        <el-form-item label="ID" v-show="false">
          <el-input v-model="dialogEditForm.id" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="应用名" prop="name">
          <el-input v-model.trim="dialogEditForm.name" autocomplete="off" :disabled="dialogEditForm.id > 0"></el-input>
        </el-form-item>
        <el-form-item label="服务等级" prop="level">
         <el-select v-model="dialogEditForm.level" placeholder="请选择" style="width: 100%;">
           <el-option label="L0-底层公共服务" value="L0"></el-option>
           <el-option label="L1-核心业务服务" value="L1"></el-option>
           <el-option label="L2-一般业务服务" value="L2"></el-option>
           <el-option label="L3-非业务服务" value="L3"></el-option>
         </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-autocomplete
            v-model="dialogEditForm.org"
            placeholder="请输入部门名称"
            :fetch-suggestions="getOrgOptions"
            style="width: 100%"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="管理员">
          <el-select v-model="dialogEditForm.admins" multiple
                     filterable
                     :filter-method="(query)=>{userPinYinMatch(query)}"
                     placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in userOptions"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <div style="line-height: normal;color: #888;margin-top: 3px">
            说明：管理员拥有应用所有权限
          </div>
        </el-form-item>
        <el-form-item label="负责人">
          <el-input v-model.trim="dialogEditForm.owner" disabled></el-input>
          <div style="line-height: normal;color: #888;">
            说明：负责人可以接受应用的告警信息。数据来源于配置中心系统
            <el-button
              type="text"
              @click="ownerPage(dialogEditForm.name)">去修改
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogEditForm.remark" type="textarea" :rows="3"></el-input>
        </el-form-item>
        <el-form-item label="发版时间窗口" prop="timeWindow">
          <el-row v-for="(item, index) in dialogEditForm.timeWindow" style="margin: 5px;">
            <el-col :span="12">
              <el-select v-model="item.daysOfWeek" multiple placeholder="请选择" style="width: 90%">
                <el-option
                  v-for="i in daysOfWeekOptions"
                  :key="i.index"
                  :label="i.label"
                  :value="i.value">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-time-picker is-range
                              format="HH:mm"
                              value-format="HH:mm"
                              v-model="item.timeRange"
                              range-separator="至"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              placeholder="选择时间范围"
                              style="width: 80%">
              </el-time-picker>
              <el-button
                type="text"
                @click="delTimePeriod(index)">删除
              </el-button>
            </el-col>
          </el-row>
          <el-button
            icon="el-icon-plus"
            size="medium"
            @click="addTimePeriod()">添加
          </el-button>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="createOrUpdate()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {createApp, deleteAppByName, editApp, findApp, searchApp} from "@/api/app";
import {userNames} from "@/api/user";
import pinyin from "pinyin-match";

export default {
  name: "appList",
  data() {
    let timeWindowValidate = (rule, value, callback) => {
      for (let item of value) {
        if (!item.daysOfWeek || item.daysOfWeek.length < 1) {
          callback(new Error('内容不能为空'));
          return
        }
        if (!item.timeRange || item.timeRange.length < 1 || !item.timeRange[0] || !item.timeRange[1]) {
          callback(new Error('内容不能为空'));
          return
        }
      }
      callback();
    };
    return {
      daysOfWeekOptions: [{
        value: 1,
        label: "周一"
      }, {
        value: 2,
        label: "周二"
      }, {
        value: 3,
        label: "周三"
      }, {
        value: 4,
        label: "周四"
      }, {
        value: 5,
        label: "周五"
      }, {
        value: 6,
        label: "周六"
      }, {
        value: 7,
        label: "周日"
      }
      ],
      searchForm: {
        keyword: "",
        level: "",
        page: 1,
        limit: 10,
      },
      tableData: [],
      tableLoading: false,
      dialogEditTitle: '',
      dialogEditVisible: false,
      dialogEditForm: {
        id: 0,
        name: "",
        level: "",
        org: "",
        owner: "",
        remark: "",
        timeWindow: []
      },
      userAllOptions: [],
      userOptions: [],
      dialogEditFormRules: {
        name: [
          {required: true, message: '请输入应用名', trigger: 'blur'},
        ],
        timeWindow: [
          {validator: timeWindowValidate, trigger: 'blur'},
        ],
      },

      orgOptions: [
        {"value": "CRM"},
        {"value": "PAAS"},
        {"value": "企信"},
        {"value": "运行平台"},
      ],
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.searchForm.keyword = this.$route.query.app
    this.loadTableData();
    this.loadUserNames();

    if (this.$route.query.showEditDialog === "true" && this.$route.query.app) {
      this.editPage(this.$route.query.app)
    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchApp(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    getOrgOptions(queryString, cb) {
      let ret = this.orgOptions;
      if (queryString) {
        ret = ret.filter(item => {
          return (item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        })
      }
      cb(ret);
    },
    loadUserNames() {
      userNames().then(response => {
        this.userOptions = response.data;
        this.userAllOptions = response.data;
      }).catch((e) => {
        this.$message.error("加载用户数据出错：", e.message);
      })
    },
    resetEditForm() {
      this.dialogEditForm.id = 0;
      this.dialogEditForm.name = "";
      this.dialogEditForm.org = "";
      this.dialogEditForm.owner = "";
      this.dialogEditForm.remark = "";
      this.dialogEditForm.timeWindow = [];
    },
    PageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    createPage() {
      this.dialogEditTitle = "新建"
      this.dialogEditVisible = true;
      this.resetEditForm();
    },
    deleteApp(index, row) {
      deleteAppByName(row.name).then(response => {
        this.tableData.data.splice(index, 1);
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    editPage(app) {
      findApp(app).then(response => {
        this.dialogEditForm = response.data
        this.dialogEditTitle = "修改"
        this.dialogEditVisible = true;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    createOrUpdate() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }

        let method = this.dialogEditForm.id > 0 ? editApp : createApp;
        method(this.dialogEditForm).then(response => {
          this.dialogEditVisible = false;
          this.$message.success("操作成功");
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        });
      });
    },
    permPage(row) {
      this.$router.push({
        name: 'app-permission', query: {
          "app": row.name,
        }
      });
    },
    adminPage(row) {
      this.$router.push({
        name: 'app-permission', query: {
          "app": row.name,
        }
      });
    },
    pipelinePage(row) {
      let p = {
        "app": row.name,
      };
      this.$router.push({name: 'cicd-app-deploy', query: p});
    },
    ownerPage(app) {
      let url = `/api/page/redirect?type=owner&app=${app}`
      window.open(url)
    },
    addTimePeriod() {
      if (this.dialogEditForm.timeWindow.length < 1) {
        this.dialogEditForm.timeWindow.push({
          daysOfWeek: [1,2,3,4,5,6,7],
          timeRange: ["23:00", "23:59"]
        })
        this.dialogEditForm.timeWindow.push({
          daysOfWeek: [1,2,3,4,5,6,7],
          timeRange: ["00:00", "06:00"]
        })
        // this.dialogEditForm.timeWindow.push({
        //   daysOfWeek: [6, 7],
        //   timeRange: ["00:00", "22:00"]
        // })
      } else {
        this.dialogEditForm.timeWindow.push({
          daysOfWeek: [],
          timeRange: ["22:00", "23:59"]
        })
      }
    },
    delTimePeriod(index) {
      this.dialogEditForm.timeWindow.splice(index, 1)
    },
    userPinYinMatch(key) {
      if (key) {
        this.userOptions = this.userAllOptions.reduce((X, item) => {
          if (pinyin.match(item, key) || item.includes(key)) {
            X.push(item);
          }
          return X;
        }, []);
      }
    }
  },

}
</script>


<template>
  <div class="app-container app-permission" v-loading="loading">
    <div class="app_info_panel" style="padding-bottom: 20px;">
      <b>应用</b><span>{{ app.name }}</span>
      <b>管理员</b><span>{{ app.admins.join(",") }}</span>
      <b>描述</b><span>{{ app.remark }}</span>
    </div>
    <el-transfer v-model="app.orgs" :data="orgOptions"
                 filterable
                 :titles="['可选角色','应用的角色']"
                 :filter-method="filterOrg"
                 filter-placeholder="请输入搜索关键字">
      <!--      <el-button class="transfer-footer" slot="left-footer" type="primary"  size="small"-->
      <!--                 @click="addRole">添加-->
      <!--      </el-button>-->
      <!--      <el-button class="transfer-footer" slot="right-footer" size="small" @click="rmRole">删除</el-button>-->
    </el-transfer>
    <div style="text-align: center;margin: 20px 80px 0 0">
      <el-button type="primary" @click="submitPerm" style="padding:12px 30px"
                 v-loading="submitBtnLoading" :disabled="this.submitBtnDisable">提 交
      </el-button>
    </div>
  </div>
</template>

<script>

import {findApp, updatePerm} from "@/api/app";
import {allOrg} from "@/api/org";

export default {
  data() {
    return {
      loading: true,
      submitBtnLoading: false,
      submitBtnDisable: false,
      app: {},
      orgOptions: [],
      tableData: [],
      tableLoading: false,
      leftCheckedOrg: [],
      rightCheckedOrg: [],
      filterOrg(query, item) {
        if (!query) return true
        return item.label.indexOf(query) > -1;
      }
    }
  },
  computed: {},
  mounted() {
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      if (!this.$route.query.app) {
        this.$message.error("缺少应用参数")
        return
      }
      this.loading = true;
      findApp(this.$route.query.app).then(response => {
        this.app = response.data
      }).catch((e) => {
        this.submitBtnDisable = true;
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false;
      })

      allOrg().then(response => {
        let newData = [];
        for (let it of response.data) {
          newData.push({
            key: it.name,
            label: it.name + ' (成员:' + it.users.join(",") + ')',
            name: it.name,
            remark: it.remark,
            disabled: false,
          })
        }
        this.orgOptions = newData
      }).catch((e) => {
        this.submitBtnDisable = true;
        this.$message.error("加载角色数据出错" + e.message);
      })
    },
    submitPerm() {
      this.submitBtnLoading = true;
      updatePerm(this.app.name, this.app.orgs).then(response => {
        this.$message.success("操作成功")
        this.$router.push({
          name: 'app-list', query: {
            "app": this.app.name,
          }
        });
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.submitBtnLoading = false;
      })
    }
    // leftCheckChange(items, item) {
    //   this.leftCheckedRole = items;
    // },
    // rightCheckChange(items, item) {
    //   this.rightCheckedRole = items;
    // },
    // addRole() {
    //   console.log(this.leftCheckedRole)
    //   if (!this.leftCheckedRole || this.leftCheckedRole.length < 1) {
    //     this.$message.warning("请先选择需要操作的数据")
    //   }
    //   this.appRoles = this.appRoles.concat(this.leftCheckedRole)
    //   this.leftCheckedRole = []
    // },
    // rmRole() {
    //   if (!this.rightCheckedRole || this.rightCheckedRole.length < 1) {
    //     this.$message.warning("请先选择需要操作的数据")
    //   }
    //   this.appRoles = this.appRoles.filter(item => this.rightCheckedRole.indexOf(item) < 0)
    //   this.rightCheckedRole = []
    // }
  }
}
</script>

<style>
.app-permission .el-transfer-panel {
  width: 45%;
}

.app-permission .el-transfer__buttons {
  width: 10%;
  max-width: 100px;
}

.app-permission .el-transfer-panel__body {
  height: 546px;
}

.app-permission .el-transfer-panel__list.is-filterable {
  height: 494px;
}

.app-permission .el-button + .el-button {
  margin-left: 0;
}

.app-permission .app_info_panel {
  margin: 10px 0;
  font-size: 14px;
}

.app-permission .app_info_panel > b {
  color: #909399;
  margin: 0 10px;
  padding: 5px 10px;
  background-color: #eee;
}

.app-permission .app_info_panel > span {
}

</style>

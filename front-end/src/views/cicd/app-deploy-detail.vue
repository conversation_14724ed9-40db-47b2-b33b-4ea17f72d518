<template>
  <div class="app-container app-deploy-detail" style="min-height: 1200px;">
    <div style="width: 960px;margin: 0 auto;">
      <el-steps :active="2" align-center>
        <el-step v-for="(item,index) in tasks" :title="item.title" description="" :status="item.status"
                 :icon="item.icon"
                 style="cursor: pointer;"
                 @click.native="stepClick(index)">
                <template slot="description">
                  {{ item.statusDesc }}
                  <div v-if="job.status.toUpperCase() === 'WAIT'" style="display: inline-block;">
                    <el-tooltip class="item" effect="dark" content="前置任务完成后，才会启动当前任务" placement="top">
                      <el-button type="text" @click="jobDetailPage(job.beforeJobId)" style="font-size: 12px;padding: 0;">
                        (查看前置任务)
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
                </el-step>
      </el-steps>
    </div>
    <div style="position: relative;height: 20px;">
      <div style="float: right; padding-bottom: 5px;">
        <el-button
          v-if="['RUNNING','WAIT'].includes(this.job.status)"
          type="text"
          style="padding: 0"
          icon="el-icon-switch-button"
          @click="cancel()">取消发布
        </el-button>
        <el-button
          v-else
          type="text"
          style="padding: 0"
          icon="el-icon-refresh-left"
          @click="redo()">重发
        </el-button>
        <el-button
          type="text"
          style="padding: 0"
          icon="el-icon-position"
          @click="pipelinePage()">发布流程页
        </el-button>
        <el-button
          type="text"
          style="padding: 0"
          icon="el-icon-menu"
          @click="podPage()">实例管理页
        </el-button>
      </div>
      <div style="font-size: 14px;font-weight: bold;color: rgb(64, 158, 255);float: left">信息
        <div style="margin-left: 60px;display: inline-block;">
          <el-button
            type="text"
            style="padding: 0;color:#ccc;font-size: 10px;"
            @click="setJobFailed()">设置为失败
          </el-button>
          <el-button
            type="text"
            style="padding: 0;color:#ccc;font-size: 10px;"
            @click="setJobSuccess()">设置为成功
          </el-button>
        </div>
      </div>
    </div>
    <el-card class="box-card" style="clear: both;" v-loading="jobLoading">
      <el-descriptions :column="4" border>
        <el-descriptions-item label="应用">{{ this.job.params.app }}</el-descriptions-item>
        <el-descriptions-item label="集群">{{ this.job.params.cluster }}</el-descriptions-item>
        <el-descriptions-item label="环境">{{ this.job.params.namespace }}</el-descriptions-item>
        <el-descriptions-item label="发布批次">{{ this.job.params.maxSurge }}</el-descriptions-item>
        <el-descriptions-item label="发布版本">{{ this.job.params.tag }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ this.job.remark }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ this.job.author }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ this.job.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="" span="4">
          <template slot="label">
            部署模块
          </template>
          <div class="deploy-module-table" style="margin-top: -10px;margin-left: -10px;">
            <el-table size="mini"
                      :data="job.params.deployModules"
                      style="width: 100%;">
              <el-table-column
                type="index"
                width="50">
              </el-table-column>
              <el-table-column
                prop="gitUrl"
                label="Git地址">
              </el-table-column>
              <el-table-column
                prop="module"
                label="子模块">
              </el-table-column>
              <el-table-column
                prop="contextPath"
                label="ContextPath">
              </el-table-column>
              <el-table-column
                prop="tag"
                label="Git 分支|标签">
              </el-table-column>
              <el-table-column
                label="镜像版本">
                <template slot-scope="scope">
                  {{ scope.row.artifactImage.split(':').pop() }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <div style="margin-top: 20px;padding-bottom: 3px;">
      <span style="font-size: 14px;font-weight: bold;color: rgb(64, 158, 255);">
        实例（pod）列表
      </span>
      <span style="display: inline-block;margin-left: 40px;color: coral;font-size: 13px">
        如果Pod无法正常启动
        <el-popover
          placement="top-start"
          width="600"
          trigger="click">
          <div style="line-height: 15px;font-size: 13px">
            1. 如果Pod一直处于"调度"状态，则说明集群资源不足。先确认Pod的CPU和内存资源配置是否合理。若配置合理，请联系集群管理员处理。 <br/><br/>
            2. 如果Pod一直处于"准备"状态，请查看Pod事件信息，确认是否存在具体的报错信息。<br/><br/>
            3. 如果Pod一直处于"启动"状态，请查看Pod启动日志，确认Tomcat启动状态，查看是否有业务报错信息。<br/><br/>
            4. 如果Pod发生了重启的话，请查看Pod重启前的日志（点击启动日志按钮，再勾选"重启前日志"复选框），查找可能得报错信息。<br/>
          </div>
          <el-button slot="reference" type="text" style="font-size: 13px;">请查看排查手册</el-button>
        </el-popover>
      </span>
    </div>
    <el-card class="box-card">
      <deploy-pod-list v-if="job.id" :app="job.params.app" :cluster="job.params.cluster" :namespace="job.params.namespace" :timestamp="new Date(job.createdAt).getTime()"></deploy-pod-list>
    </el-card>

    <div style="margin-top: 20px;font-size: 14px;font-weight: bold;color: rgb(64, 158, 255);padding-bottom: 3px;">日志</div>
    <el-card class="box-card">
      <el-tabs tab-position="top">
        <el-tab-pane :label="item.title" v-for="(item,index) in tasks">
          <div>
            <pre style="white-space: pre-wrap;padding-left: 10px;" class="el-collapse-item__content">{{ item.output }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div style="padding-top: 10px;">
        <!--        <el-collapse>-->
        <!--          <el-collapse-item v-for="(item,index) in tasks" :name="index">-->
        <!--            <template slot="title">-->
        <!--              <div style="width: 100%">-->
        <!--              <span-->
        <!--                :style="{'fontSize':'1.4em','color':item.status === 'success' ? '#67C23A': item.status === 'error' ? '#F56C6C':'#333'}">-->
        <!--                  {{ item.title }}-->
        <!--                <span style="padding-left: 16px;" v-if="item.summary">{{ item.summary }}</span>-->
        <!--              </span>-->
        <!--              </div>-->
        <!--            </template>-->
        <!--            <div style="padding-right: 10px">-->
        <!--              <div v-if="item.type ==='EOLINKER'">-->
        <!--                todo-->
        <!--              </div>-->
        <!--              <div v-else>-->
        <!--                <pre>{{item.output}}</pre>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </el-collapse-item>-->
        <!--        </el-collapse>-->
      </div>
    </el-card>

    <div>
      <el-backtop></el-backtop>
    </div>
  </div>
</template>

<script>

import {cancelJob, findJobById, findTaskByJobId, redoJob, setJobStatus} from "@/api/job";
import DeployPodList from "@/views/cicd/deploy-pod-list.vue";

export default {
  data() {
    return {
      timerId: null,
      jobLoading: true,
      job: {
        params: {}
      },
      tasks: [],
      activeNames: [],
      runningExecutions: [],
      jenkinsTable: "jenkinsTab2"
    }
  },
  components: {DeployPodList},
  computed: {},
  beforeDestroy: function () {
    if (this.timerId) {
      clearTimeout(this.timerId);
      console.log("clear timer, id:" + this.timerId);
    }
  },
  mounted() {
    let id = this.$route.query.jobId
    if (id) {
      this.loadJob(id)
    }
  },
  methods: {
    loadJob(id) {
      this.jobLoading = true
      findJobById(id).then(response => {
        this.job = response.data;
        this.loadTasks(id)
        let status = this.job.status.toUpperCase();
        let vThis = this;
        if (status === "RUNNING") {
          this.timerId = setTimeout(function () {
            vThis.loadJob(id);
          }, 5000);
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.jobLoading = false
      });
    },
    loadTasks(jobId) {
      findTaskByJobId(jobId).then(response => {
        this.tasks = response.data
        this.modifyStages()
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    stepClick(stepIndex) {
      if (this.activeNames.includes(stepIndex)) {
        this.activeNames = [];
      } else {
        this.activeNames = [stepIndex];
      }
    },
    modifyStages() {
      for (let item of this.tasks) {
        item.status = this.toStepStatus(item.status)
        if (item.status === "process") {
          item.icon = "el-icon-loading"
        } else {
          item.icon = ""
        }
      }
    },
    cancel() {
      this.$confirm(`取消后后端新旧实例还会继续进行替换。是否继续取消当前发布？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelJob(this.job.id).then(response => {
          this.$message.success('操作成功！');
          this.loadJob(this.job.id)
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    setJobFailed() {
      setJobStatus(this.job.id, "fail").then(response => {
        this.$message.success('操作成功！');
        this.loadJob(this.job.id)
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    setJobSuccess() {
      setJobStatus(this.job.id, "success").then(response => {
        this.$message.success('操作成功！');
        this.loadJob(this.job.id)
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    redo() {
      this.$confirm(`确认要使用当前参数重发吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        redoJob(this.job.id).then(response => {
          let id = response.data.id
          let routeUrl = this.$router.resolve({query: {"jobId": id}})
          window.location.href = routeUrl.href
          window.location.reload();
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    podPage() {
      let routeUrl = this.$router.resolve({
        name: "pod-index",
        query: {
          "cluster": this.job.params.cluster,
          "namespace": this.job.params.namespace,
          "app": this.job.params.app,
        }
      });
      window.open(routeUrl.href, '_blank');
    },
    pipelinePage() {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": this.job.params.app}});
    },
    jobDetailPage(jobId) {
      findJobById(jobId).then(response => {
        const job = response.data;
        const name = job.type === 'CD' ? 'cicd-app-deploy-detail' : 'cicd-image-build-detail';
        let rou = this.$router.resolve({name: name, query: {"jobId": jobId}});
        window.open(rou.href, '_blank');
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    toStepStatus(stageStatus) {
      let ret = ""
      switch (stageStatus) {
        case "WAIT":
        case "SKIP":
        case "CANCEL":
          ret = "wait"
          break;
        case "RUNNING":
          ret = "process"
          break;
        case "FAIL":
          ret = "error"
          break;
        case "SUCCESS":
          ret = "success"
          break;
        default:
          ret = "finish"
      }
      return ret
    },
  }
}
</script>

<style>
.app-deploy-detail .desc-label {
  color: #99a9bf;
  font-weight: 700;
}

.app-deploy-detail .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 0
}

.app-deploy-detail .el-card__body {
  padding: 10px;
}

.app-deploy-detail .el-divider--horizontal {
  margin-bottom: 10px;
}

.deploy-module-table .el-table td.el-table__cell, .deploy-module-table .el-table th.el-table__cell.is-leaf {
  border-bottom-width: 0;
}
</style>

<template>
  <div class="app-container build-history-container" style="margin: 0;padding: 0">
    <menu-tabs tab-name="image-build-history"></menu-tabs>
    <div style="padding: 10px">
      <div style="float: left">
        <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
          <el-form-item label="任务状态">
            <el-select v-model="searchForm.status" multiple style="width: 190px;" filterable clearable>
              <el-option label="运行中" value="RUNNING"></el-option>
              <el-option label="已取消" value="CANCEL"></el-option>
              <el-option label="等待中" value="WAIT"></el-option>
              <el-option label="成功" value="SUCCESS"></el-option>
              <el-option label="失败" value="FAIL"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="来源应用">
            <el-input v-model.trim="searchForm.app" style="width: 260px" clearable></el-input>
          </el-form-item>
          <el-form-item label="操作人">
            <el-input v-model.trim="searchForm.author" style="width: 140px" clearable></el-input>
          </el-form-item>
          <br/>
          <el-form-item label="Git模块" style="margin-top: -10px">
            <el-select v-model="gitModuleFullName" filterable clearable style="margin-bottom: 10px;width: 750px;">
              <el-option
                v-for="item in artifacts"
                :key="item.id"
                :label="item.gitUrl + (item.module ? ' --- ' + item.module : '')"
                :value="item.gitUrl + '---' + item.module">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-top: -10px">
            <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
            <export-button :table-ref="this.$refs.table001"></export-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="float: right">
        <el-pagination
          :current-page="searchForm.page"
          :page-size="searchForm.limit"
          :page-sizes="[10, 20, 50, 100,500,1000,2000,5000]"
          :pager-count="5"
          layout="total,sizes,prev,pager,next"
          :total="tableData.count"
          @size-change="pageSizeChange"
          @current-change="pageChange">
        </el-pagination>
      </div>
      <el-table
        ref="table001"
        v-loading="tableLoading"
        :data="tableData.data"
        element-loading-text="数据加载中..."
        highlight-current-row
      >
        <el-table-column type="index">
        </el-table-column>
        <el-table-column label="来源应用" prop="app">
          <template slot-scope="scope">
            <div style="color: black;">{{ scope.row.app }}</div>
            <div style="font-weight: bold">
              <router-link :to="{name:'cicd-image-build-detail',query:{'jobId':scope.row.id}}" target="_blank" style="color:#409EFF;display: inline-block">
                <div style="font-size: 12px;">构建详情</div>
              </router-link>
              <router-link :to="{name:'cicd-app-deploy',query:{'app':scope.row.app}}" target="_blank" style="color:#409EFF;display: inline-block;margin-left: 5px;">
                <div style="font-size: 12px;">去发布</div>
              </router-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Git模块" min-width="200">
          <template slot-scope="scope">
            <div style="font-size: 12px;line-height: 18px;">
              <a :href="scope.row.params.gitUrl" style="color: #3a8ee6" target="_blank">{{ scope.row.params.gitUrl }}</a>
              <div>[子模块]: {{ scope.row.params.gitModule }}</div>
              <div>[分支|标签]: {{ scope.row.params.gitTag }}</div>
              <div>[commitId]: {{ scope.row.params.commitId || '-'}}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="容器镜像" prop="params.gitTag" min-width="160">
          <template slot-scope="scope">
            <div style="font-size: 12px;line-height: 18px;">
              <div style="color: black;">
                版本：{{ scope.row.params.artifactImage ? scope.row.params.artifactImage.split(":").pop() : '-' }}
              </div>
              <div style="color: #888;">
                <div> [父pom]: {{ scope.row.params.parentPom.replace("fxiaoke-parent-pom-", "") }}</div>
              </div>
              <div style="color: #888;">
                <div> [构建环境]: {{ scope.row.buildEnv }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="参数">
          <template slot-scope="scope">
            <el-checkbox label="单元测试" :checked="scope.row.params.unitTest" disabled class="checkbox001"></el-checkbox>
            <br/>
            <el-checkbox label="强制编译" :checked="scope.row.params.forceCodeCompile" disabled class="checkbox001"></el-checkbox>
            <br/>
            <el-checkbox label="依赖包校验" :checked="scope.row.params.dependencyCheck" disabled class="checkbox001"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="耗时（秒）" width="100" prop="timeCost" align="center">
        </el-table-column>
        <el-table-column label="备注" prop="remark">
        </el-table-column>
        <el-table-column label="状态" prop="status" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.status === 'SUCCESS'">
              <el-tag type="success">成功</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'FAIL'">
              <el-tag type="danger">失败</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'CANCEL'">
              <el-tag type="warning">已取消</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'WAIT'">
              <el-tag type="info" effect="plain">等待中...</el-tag>
              <div style="font-size: 12px;color:#409EFF">
                <el-tooltip class="item" effect="dark" content="前置任务完成后，才会启动当前任务" placement="top">
                  <el-button type="text" @click="jobDetailPage(scope.row.beforeJobId)" style="font-size: 12px;padding: 0;">
                    查看前置任务
                  </el-button>
                </el-tooltip>
              </div>
            </div>
            <div v-else-if="scope.row.status === 'RUNNING'">
              <el-tag type="info" effect="plain">运行中...</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作人" width="130" prop="author">
        </el-table-column>
        <el-table-column label="创建时间" width="100" prop="createdAt">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

import {findJobById, searchJob} from "@/api/job";
import {getAllArtifact} from "@/api/artifact";
import MenuTabs from "@/views/cicd/menu-tabs.vue";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "image-build-history",
  components: {ExportButton, MenuTabs},
  data() {
    return {
      tableData: {
        data: [],
        count: 0
      },
      artifacts: [],
      tableLoading: false,
      gitModuleFullName: "",
      searchForm: {
        type: "CI",
        author: null,
        app: "",
        params: {
          gitUrl: "",
          gitModule: "",
        },
        status: "",
        page: 1,
        limit: 20,
      },
    }
  },
  computed: {},
  mounted() {
    if(this.$route.query.author) {
      this.searchForm.author = this.$route.query.author
    }
    if (this.searchForm.author == null && this.$store.state.user && this.$store.state.user.realName) {
      this.searchForm.author = this.$store.state.user.realName
    }
    if (this.$route.query.gitUrl) {
      this.gitModuleFullName = this.$route.query.gitUrl + "---" + (this.$route.query.gitModule ? this.$route.query.gitModule:'')
    }

    this.loadAllArtifact();
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      if (this.gitModuleFullName) {
        let parts = this.gitModuleFullName.split("---")
        this.searchForm.params.gitUrl = parts[0]
        this.searchForm.params.gitModule = parts[1]
      } else {
        this.searchForm.params.gitUrl = ""
        this.searchForm.params.gitModule = ""
      }
      searchJob(this.searchForm).then(response => {
        let newData = [];
        for (let item of response.data.data) {
          let buildEnv = item.params.mavenImage
          if (buildEnv) {
            let parts = buildEnv.split("/")
            if (parts.length > 0) {
              buildEnv = parts[parts.length - 1]
            }
          }
          item.buildEnv = buildEnv
          newData.push(item)
        }
        this.tableData = {
          data: newData,
          count: response.data.count
        };

      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    loadAllArtifact() {
      getAllArtifact().then(response => {
        this.artifacts = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    pageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    pageSizeChange(size) {
      this.searchForm.limit = size;
      this.loadTableData();
    },
    jobDetailPage(jobId) {
      findJobById(jobId).then(response => {
        const job = response.data;
        const name = job.type === 'CD' ? 'cicd-app-deploy-detail' : 'cicd-image-build-detail';
        let rou = this.$router.resolve({name: name, query: {"jobId": jobId}});
        window.open(rou.href, '_blank');
      }).catch((e) => {
        this.$message.error(e.message);
      })
    }
  }
}
</script>
<style>
.build-history-container .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

.build-history-container .checkbox001 .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #555;
}
</style>



<template>
  <div class="app-container" style="margin: 0;padding: 0;">
    <menu-tabs tab-name="app-deploy-history"></menu-tabs>
    <div style="padding: 10px;">
      <div style="float: left">
        <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
          <el-form-item label="发布状态">
            <el-select v-model="searchForm.status" multiple style="width: 190px;" filterable>
              <el-option label="运行中" value="RUNNING"></el-option>
              <el-option label="已取消" value="CANCEL"></el-option>
              <el-option label="等待中" value="WAIT"></el-option>
              <el-option label="成功" value="SUCCESS"></el-option>
              <el-option label="失败" value="FAIL"></el-option>
            </el-select>
          </el-form-item>
          <namespace-selector ref="nsSelector" namespace-label="环境"></namespace-selector>
          <el-form-item label="应用">
            <el-input v-model.trim="searchForm.params.app" clearable style="width: 280px"></el-input>
          </el-form-item>
          <el-form-item label="操作人">
            <el-input v-model.trim="searchForm.author" style="width: 140px" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
            <export-button :table-ref="this.$refs.table001"></export-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="float: right">
        <el-pagination
          :current-page="searchForm.page"
          :page-size="searchForm.limit"
          :page-sizes="[10, 20, 30, 40, 50, 100,500,1000,2000]"
          :pager-count="5"
          layout="total,sizes,prev,pager,next"
          :total="tableData.count"
          @size-change="pageSizeChange"
          @current-change="pageChange">
        </el-pagination>
      </div>
      <el-table
        ref="table001"
        v-loading="tableLoading"
        :data="tableData.data"
        element-loading-text="数据加载中..."
        highlight-current-row
      >
        <el-table-column type="index">
        </el-table-column>
        <el-table-column label="应用" prop="params.app">
          <template slot-scope="scope">
            <router-link :to="{name:'cicd-app-deploy-detail',query:{'jobId':scope.row.id}}" target="_blank" style="color:#409EFF">
              {{ scope.row.params.app }}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="版本号" prop="params.tag">
        </el-table-column>
        <el-table-column label="环境" prop="params.namespace">
        </el-table-column>
        <el-table-column label="集群" prop="params.cluster">
        </el-table-column>
        <el-table-column label="耗时（秒）" width="100" prop="timeCost">
        </el-table-column>
        <el-table-column label="备注" prop="remark">
        </el-table-column>
        <el-table-column label="操作人" prop="author" width="130">
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.status === 'SUCCESS'">
              <el-tag type="success">成功</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'FAIL'">
              <el-tag type="danger">失败</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'CANCEL'">
              <el-tag type="warning">已取消</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'WAIT'">
              <el-tag type="info" effect="plain">等待中...</el-tag>
              <div style="font-size: 12px;color:#409EFF">
                <el-tooltip class="item" effect="dark" content="前置任务完成后，才会启动当前任务" placement="top">
                  <el-button type="text" @click="jobDetailPage(scope.row.beforeJobId)" style="font-size: 12px;padding: 0;">
                    查看前置任务
                  </el-button>
                </el-tooltip>
              </div>
            </div>
            <div v-else-if="scope.row.status === 'RUNNING'">
              <el-tag type="info" effect="plain">运行中...</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createdAt" width="100">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

import {findJobById, searchJob} from "@/api/job";
import MenuTabs from "@/views/cicd/menu-tabs.vue";
import NamespaceSelector from "@/views/components/namespace-selector.vue";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "deploy-history",
  components: {ExportButton, NamespaceSelector, MenuTabs},
  data() {
    return {
      tableData: {
        data: [],
        count: 0
      },
      artifacts: [],
      tableLoading: false,
      searchForm: {
        type: "CD",
        author: null,
        params: {
          app: "",
          namespace: ""
        },
        status: [],
        page: 1,
        limit: 20,
      },
    }
  },
  computed: {},
  mounted() {
    if (this.searchForm.author == null && this.$store.state.user && this.$store.state.user.realName) {
      this.searchForm.author = this.$store.state.user.realName
    }
    if (this.$route.query.app) {
      this.searchForm.params.app = this.$route.query.app;
    }
    if (this.$route.query.namespace) {
      this.searchForm.params.namespace = this.$route.query.namespace;
    }
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      this.searchForm.params.namespace = this.$refs.nsSelector.namespace;
      searchJob(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    pageSizeChange(size) {
      this.searchForm.limit = size;
      this.loadTableData();
    },
    jobDetailPage(jobId) {
      findJobById(jobId).then(response => {
        const job = response.data;
        const name = job.type === 'CD' ? 'cicd-app-deploy-detail' : 'cicd-image-build-detail';
        let rou = this.$router.resolve({name: name, query: {"jobId": jobId}});
        window.open(rou.href, '_blank');
      }).catch((e) => {
        this.$message.error(e.message);
      })
    }
  }
}
</script>



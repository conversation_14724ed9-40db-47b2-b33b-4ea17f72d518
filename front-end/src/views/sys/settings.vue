<template>
  <div class="app-container" v-loading="loading" style="position: relative">
    <div style="position: absolute;right: 10px;top: 20px;">
      <el-button
        type="text"
        style="padding: 0;float: right;"
        @click="clear_cache">清除系统缓存
      </el-button>
    </div>
    <div style="position: relative">
      <el-tabs tab-position="top">
        <el-tab-pane label="settings.json">
          <div style="font-size: 14px;position: absolute; right: 10px;top: 0;z-index: 999">
            <clipboard-icon :text="JSON.stringify(this.settings, null, 2)" button-text="一键复制"></clipboard-icon>
          </div>
          <vue-json-pretty :data="this.settings">
          </vue-json-pretty>
        </el-tab-pane>
        <el-tab-pane label="oncall.json">
          <div style="font-size: 14px;position: absolute; right: 10px;top: 0;z-index: 999">
            <clipboard-icon :text="JSON.stringify(this.oncallConfig, null, 2)" button-text="一键复制"></clipboard-icon>
          </div>
          <vue-json-pretty :data="this.oncallConfig">
          </vue-json-pretty>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import VueJsonPretty from 'vue-json-pretty'
import {clearCache, getOncallConfig} from "@/api/sys";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  data() {
    return {
      loading: false,
      oncallConfig: {
        "err": "数据未加载"
      },
      settings: {}
    }
  },
  mounted() {
    this.loadOncallConfig()
    this.settings = this.$settings
  },
  methods: {
    loadOncallConfig() {
      getOncallConfig().then(response => {
        this.oncallConfig = response.data;
      }).catch((e) => {
        this.$message.error("oncall config load fail, err:" + e.message);
      })
    },
    clear_cache() {
      this.loading = true
      clearCache().then(response => {
        this.$message.success("操作成功");
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
  },
  components: {
    ClipboardIcon,
    VueJsonPretty
  },
}
</script>


<template>
  <div class="pod-expand">
    <div class="expand-wrapper" v-if="pod">
      <el-row>
        <el-col :span="12">
          <el-form label-position="left" label-width="80px" size="mini">
            <el-form-item label="实例名" class="expand-wrapper-item">
              <span>{{ pod.name }}</span>
            </el-form-item>
            <el-form-item label="集群/环境" class="expand-wrapper-item">
              <span>{{ pod.cluster }}/{{ pod.namespace }}</span>
            </el-form-item>
            <el-form-item label="运行版本">
              <span>{{ pod.deployTag }}</span>
            </el-form-item>
            <el-form-item label="容器镜像">
              <span>{{ pod.container0Image }}</span>
            </el-form-item>
            <el-form-item label="状态">
              {{ pod.statusDesc }}
              <span style="padding-left: 10px"> |
                 <el-tag effect="plain" size="mini" type="info">PodPhase: {{ pod.phase }}</el-tag>
              <el-tag effect="plain" size="mini" type="info">podReady: {{ pod.ready }}</el-tag>
              <el-tag effect="plain" size="mini" type="info">Container0Status: {{ pod.container0Status }}</el-tag>
                </span>
            </el-form-item>
            <el-form-item label="所在机器">
              <div>
                <span>{{ pod.hostIP }}</span>
                <span>
                  （资源池: {{ pod.resourcePool ? pod.resourcePool : "Common" }} ）
                </span>
              </div>
            </el-form-item>
            <el-form-item label="资源配置">
              <span><b style="padding-right: 5px;">CPU: </b>{{ (pod.requestCpu / 1000).toFixed(2) }} - {{ (pod.limitCpu / 1000).toFixed(2) }} </span>
              <span>
                <b style="padding-right: 5px; padding-left: 30px;">内存 (MB): </b>
                {{ Math.floor(pod.requestMemory / 1024 / 1024) }} - {{ Math.floor(pod.limitMemory / 1024 / 1024) }}
              </span>
            </el-form-item>
            <el-form-item label="重启次数">
              <span>{{ pod.restartCount }}</span>
            </el-form-item>
            <el-form-item label="最近重启时间" v-if="pod.restartCount > 0">
              <span>{{ pod.restartTime }}</span>
            </el-form-item>
            <el-form-item label="最近重启说明" v-if="pod.restartCount > 0">
              <span>Reason: {{ pod.restartReason || "--" }}</span>
              <span style="padding-left: 20px;">ExitCode: {{ pod.restartCode }}</span>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div style="color: #606266;font-weight: bold;padding-left: 15px;line-height: 40px;">事件列表</div>
          <pod-event :cluster="pod.cluster" :namespace="pod.namespace" :pod="pod.name"></pod-event>
        </el-col>
      </el-row>

    </div>
  </div>
</template>
<script>
import PodEvent from "@/views/pod/pod-event";

export default {
  components: {PodEvent},
  props: {
    pod: {
      type: Object,
      default: () => ({}),
      required: true
    },
  },
  created() {
    //解决element table fixed下组件重复加载的问题
    //参见：https://github.com/ElemeFE/element/issues/12177
    // if(this.$parent.$el
    //   && this.$parent.$el.offsetParent
    //   && this.$parent.$el.offsetParent.className
    //   && this.$parent.$el.offsetParent.className.includes("fixed")) {
    //   return
    // }
    // this.$nextTick(() => {
    //   console.log("pod-expand-mounted")
    //   this.loadEvents()
    // })

  },
  computed: {},
  data() {
    return {}
  },
  methods: {}
}
</script>

<style>
.pod-expand .expand-wrapper {
  margin-left: 20px;
}

.pod-expand .expand-wrapper .el-form-item {
  margin-bottom: 0;
}

.pod-expand .expand-wrapper .el-form-item__label {
  text-align: right;
}

</style>

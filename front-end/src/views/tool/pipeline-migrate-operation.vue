<template>
  <div class="app-container pipeline-migrate-operation">
    <pipeline-migrate-tab active-name="migrate-operation"></pipeline-migrate-tab>
    <el-row style="max-width: 1080px;" :gutter="20">
      <el-col :span="18">
        <el-form>
          <el-form-item>
            <el-input type="textarea" v-model="searchForm" autosize :autosize="{ minRows: 5, maxRows: 10}"
                      placeholder="输入需要过滤的发布流程列表，内容格式：运行环境/应用名，多个之间用换行分割"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" @click="loadTableData" size="small">查询</el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      :row-class-name="tableRowClassName"
      element-loading-text="数据加载中..."
      border
      fit
      highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column
        type="selection"
        width="50">
      </el-table-column>
      <el-table-column label="集群"
                       prop="cluster">
        <template slot-scope="scope" >
          <div v-if="scope.row.cluster === 'k8s0'" style="color:#FB5151;font-weight:bold;font-size:1.2em">{{scope.row.cluster}} (新集群）</div>
          <div v-else>{{scope.row.cluster}}</div>
        </template>
      </el-table-column>
      <el-table-column label="环境"
                       prop="namespace">
      </el-table-column>
      <el-table-column label="应用名"
                       prop="app">
      </el-table-column>
      <el-table-column
        label="操作"
        width="140px">
        <template slot-scope="scope" v-if="scope.row.cluster === 'k8s0'">
          <div style="">
            <el-button
              type="text"
              size="mini"
              style="margin-top: 0;padding-top: 0;color: #FF9800"
              @click="deployDialogShow(scope.row)">发布版本
            </el-button>
            <router-link :to="{name: 'app-pipeline-execution-history', query: {'keyword': scope.row.app, 'namespace': scope.row.namespace}}" target="_blank">
              <el-button
                type="text"
                size="mini"
                style="margin-top: 0;padding-top: 0;">
                发布历史
              </el-button>
            </router-link>
            <br/>
            <router-link :to="{name: 'cicd-app-deploy', query: {'app': scope.row.app}}" target="_blank">
              <el-button
                type="text"
                size="mini"
                style="margin-top: 0;padding-top: 0;">
                发布流程
              </el-button>
            </router-link>
            <router-link :to="{name: 'app-pipeline-edit', query: {'pipelineId': scope.row.id}}" target="_blank">
              <el-button
                type="text"
                size="mini"
                style="margin-top: 0;padding-top: 0;">
                编辑流程
              </el-button>
            </router-link>

          </div>
        </template>
      </el-table-column>
      <el-table-column label="地址(port/name)">
        <template slot-scope="scope" v-if="scope.row.cluster === 'k8s0'">
          <div v-for="it in scope.row.ports" style="border: 1px #ccc solid;padding: 2px;margin-top: 3px">
            <div style="display: inline-block;">
              {{ it.value }}/{{ it.name }}
            </div>
            <el-button
              style="padding-top:0;padding-bottom:0"
              type="text"
              size="mini"
              @click="updateCMSConfig(scope.row,it.value)">迁移地址
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100px" align="center"
                       :filters="[{ text: '正常', value: 'enabled' }, { text: '已迁移', value: 'migrated' },{ text: '待审核', value: '待审核' }]"
                       :filter-method="filterStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'enabled'" type="success" size="mini">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
          <el-tag v-else type="warning" size="mini">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
          <div>
            <el-popover
              placement="top"
              width="240">
              <p>修改状态</p>
              <div>
                <el-button size="mini" @click="updatePipelineStatus(scope.row,'enabled')">正常</el-button>
                <el-button size="mini" @click="updatePipelineStatus(scope.row,'migrated')">已迁移</el-button>
                <el-button size="mini" @click="updatePipelineStatus(scope.row,'disabled')">禁用</el-button>
              </div>
              <el-button slot="reference" type="text" size="mini">编辑</el-button>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实例数(运行/配置)" align="center" prop="replicas">
        <template slot-scope="scope">
          <div v-if="!replicasIsSame(scope.row.app, scope.row.namespace) && scope.row.cluster === 'k8s0'" style="color: #FB5151;font-size: 1.2em;font-weight: bold;">
            {{ scope.row.extraAttr.runningPodNum }} / {{ scope.row.replicas }}
          </div>
          <div v-else>
            {{ scope.row.extraAttr.runningPodNum }} / {{ scope.row.replicas }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="版本" align="center">
        <template slot-scope="scope">
          <div style="font-size: 12px;font-weight: bold;">
            <div v-if="!versionIsSame(scope.row.app, scope.row.namespace) && scope.row.cluster === 'k8s0'" style="color: #FB5151;">
              {{ scope.row.extraAttr.deployTag }}
            </div>
            <div v-else>
              {{ scope.row.extraAttr.deployTag }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="100" property="author">
      </el-table-column>
    </el-table>

    <el-dialog title="使用k8s1版本发布当前环境" :visible.sync="deployOption.deployDialogVisible" width="600px">
      <div v-html="'提示：' + deployOption.prompt">
      </div>
      <div style="margin-top: 10px;margin-bottom: -20px;">
        <el-form>
          <el-form-item>
            <el-checkbox v-model="deployOption.skipBuild">跳过镜像构建</el-checkbox>
            <el-checkbox v-model="deployOption.forceCodeCompile">强制编译代码</el-checkbox>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deployOption.deployDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="this.deployUseK8s1Tag">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="地址修改确认" :visible.sync="cmsUpdateVisible" width="860px">
      <div>
        <div style="padding-bottom: 3px;color: #666;">格式： 集群/环境/应用名/端口</div>
        <el-descriptions :column="1" border>
          <el-descriptions-item>
            <template slot="label">
              <b>{{ cmsUpdateOld.cluster + "/" + cmsUpdateOld.namespace + "/"}}</b>
              <span style="color: orangered">{{cmsUpdateOld.app }}</span>
              <span>/{{cmsUpdateOld.name}}</span>:
            </template>
            {{ cmsUpdateOld.addr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <b>{{ cmsUpdateNew.cluster + "/" + cmsUpdateNew.namespace + "/"  }}</b>
              <span style="color: orangered">{{ cmsUpdateNew.app }} </span>
              <span>/{{cmsUpdateNew.name}}</span>:
            </template>
            {{ cmsUpdateNew.addr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <b style="font-size: 1.2em;">地址修改：</b>
            </template>
            {{ cmsUpdateOld.addr }} → {{ cmsUpdateNew.addr }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
          <el-button @click="$message.info('请本地修改git代码')">改JavaConsole</el-button>
          <el-button @click="$message.info('请本地修改git代码')">改Traefik</el-button>
        <a :href="'https://oss.foneshare.cn/cms/replace/preview/?profile=&&src=' + this.cmsUpdateOld.addr + '&dst=' + this.cmsUpdateNew.addr" target="_blank">
          <el-button type="primary">改配置中心</el-button>
        </a>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {deploymentDetail} from "@/api/k8s/app";
import {getAppAddress} from "@/api/app";
import PipelineMigrateTab from "@/views/tool/pipeline-migrate-tab.vue";
import {appDeployWithOtherK8sTag, migratePipelineSearch} from "@/api/tool";
import {updatePipelineStatus} from "@/api/pipeline";

export default {
  components: {PipelineMigrateTab},
  data() {
    return {
      tableData: [],
      searchForm: "",
      tableLoading: false,
      cmsUpdateVisible: false,
      cmsUpdateOld: {
        cluster: "",
        namespace: "",
        app: "",
        name:"",
        addr: "",

      },
      cmsUpdateNew: {
        cluster: "",
        namespace: "",
        app: "",
        name:"",
        addr: "",
      },
      deployOption: {
        deployDialogVisible:false,
        prompt:"",
        cluster:"",
        namespace:"",
        app:"",
        skipBuild: true,
        forceCodeCompile:false,
      }
    }
  },
  computed: {},
  mounted() {
    if (this.searchForm) {
      this.loadTableData();
    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      migratePipelineSearch(this.searchForm).then(response => {
        this.tableData = response.data.data
        for (let it of this.tableData) {
          it.extraAttr.deployTag = "?"
          it.extraAttr.runningPodNum = "?"
          this.findDeployment(it.cluster, it.namespace, it.app)
        }

      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    tableRowClassName({row, rowIndex}) {
      return row.cluster + "-row"
    },
    tableFilter() {
      if (!this.searchForm || !this.searchForm.trim()) {
        this.tableData = this.tableDataAll;
        return
      }
      this.tableData = []
      this.searchForm = this.searchForm.replace(/ /g, '')
      if (!this.searchForm.endsWith("\n")) {
        this.searchForm += "\n"
      }
      console.log(this.searchForm)
      for (let it of this.tableDataAll) {
        if (this.searchForm.indexOf(`${it.cluster}/${it.namespace}/${it.app}\n`) > -1) {
          this.tableData.push(it)
        }
      }
    },
    versionIsSame(app, namespace) {
      let version0 = ""
      let version1 = ""
      for (let it of this.tableData) {
        if (it.cluster === "k8s0" && it.namespace === namespace && it.app === app) {
          version0 = it.extraAttr.deployTag
        }
        if (it.cluster === "k8s1" && it.namespace === namespace && it.app === app) {
          version1 = it.extraAttr.deployTag
        }
      }
      if (version0 === "" || version1=== "") {
        return false
      }
      return version0 === version1
    },
    replicasIsSame(app, namespace) {
      let k8s0Replicas = ""
      let k8s1Replicas = ""
      let k8s0RunningPodNum = ""
      let k8s1RunningPodNum = ""
      for (let it of this.tableData) {
        if (it.cluster === "k8s0" && it.namespace === namespace && it.app === app) {
          k8s0Replicas = it.replicas
          k8s0RunningPodNum = it.runningPodNum
        }
        if (it.cluster === "k8s1" && it.namespace === namespace && it.app === app) {
          k8s1Replicas = it.replicas
          k8s1RunningPodNum = it.runningPodNum
        }
      }
      return k8s0Replicas === k8s1Replicas && k8s0RunningPodNum === k8s1RunningPodNum
    },
    async findDeployment(cluster, namespace, app) {
      let pipe = null
      for (let it of this.tableData) {
        if (it.cluster === cluster && it.namespace === namespace && it.app === app) {
          pipe = it
          break
        }
      }
      if (pipe === null) {
        this.$message.warning(`找不到发布流程，应用：${cluster} / ${namespace} / ${app}`)
        return
      }
      deploymentDetail(cluster, namespace, app).then(response => {
        pipe.extraAttr.deployTag = response.data.deployTag
        pipe.extraAttr.runningPodNum = response.data.replicas
      }).catch((e) => {
        console.log(e.message)
      });
    },
    filterStatus(value, row) {
      return row.status === value
    },
    updateCMSConfig(row, portNum) {
      if (row.cluster !== "k8s0") {
        this.$message.warning("目前只支持k8s0环境")
        return
      }
      getAppAddress("k8s0", row.namespace, row.app).then(response => {
        this.cmsUpdateOld = {}
        this.cmsUpdateNew = {}
        let port = null;
        for (let p of response.data) {
          if (p.port === portNum) {
            port = p
            break
          }
        }
        if (!port) {
          this.$message.error("找不到地址")
          return
        }
        this.cmsUpdateNew = {
          "cluster": "k8s0",
          "namespace": row.namespace,
          "app": row.app,
          "name": port.name,
          "addr": port.clusterOuterAddress[0]
        }

        getAppAddress("k8s1", row.namespace, row.app).then(response => {
          let port = null;
          for (let p of response.data) {
            if (p.port === portNum) {
              port = p
              break
            }
          }
          if (!port) {
            this.$message.error("找不到地址")
            return
          }
          this.cmsUpdateOld = {
            "cluster": "k8s1",
            "namespace": row.namespace,
            "app": row.app,
            "name": port.name,
            "addr": port.clusterOuterAddress[0]
          }
          this.cmsUpdateVisible = true;
        }).catch((e) => {
          this.$message.error(e.message);
        })
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    deployDialogShow(row) {
      this.deployOption.prompt = `确定要使用k8s1集群的版本发布应用吗？应用：${row.cluster}/${row.namespace}/${row.app}`
      this.deployOption.deployDialogVisible = true
      this.deployOption.cluster = row.cluster
      this.deployOption.namespace = row.namespace
      this.deployOption.app = row.app
    },
    deployUseK8s1Tag() {
      let params = {
        "cluster": this.deployOption.cluster,
        "namespace": this.deployOption.namespace,
        "app": this.deployOption.app,
        "oldCluster": "k8s1",
        "skipImageBuild": this.deployOption.skipBuild,
        "forceCodeCompile": this.deployOption.forceCodeCompile,
      }
      appDeployWithOtherK8sTag(params).then(response => {
        this.$message.success(response.data)
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    updatePipelineStatus(row,status) {
      let req = {}
      req.id = row.id
      req.status = status
      console.log(req)
      updatePipelineStatus(req).then(response => {
        this.$message.success("修改成功")
        row.status = status
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
      })
    },
    convertStatus(status) {
      switch (status) {
        case "enabled":
          return "正常"
        case "audit":
          return "待审核"
        case "migrated":
          return "已迁移"
        case "disabled":
          return "禁用"
        default:
          return "未知"
      }
    }
  }
}
</script>

<style>
.pipeline-migrate-operation .el-table .k8s0-row {
  //background: #ffe1ac;
}
.pipeline-migrate-operation .el-table .k8s1-row {
  background: #eee;
}
</style>

<template>
  <div class="app-container">
    <app-manage-tab active-name="app-restart"></app-manage-tab>
    <el-alert
      title="服务批量重启"
      type="info"
      description="批量重启：创建任务 → 开始；任务间隔时间1分钟"
      show-icon>
    </el-alert>
    <div style="margin: 10px">
      <el-button type="primary" @click="appRestartVisible=true" size="small">创建任务</el-button>
      <div v-if="job && job.items" style="display: inline-block;margin-left: 30px;">
        <el-button v-if="job.status ===0" type="text" @click="start" size="small">开始</el-button>
        <el-button v-if="job.status ===1" type="text" @click="stop" size="small">暂停</el-button>
        <el-button type="text" @click="remove" size="small">删除</el-button>
      </div>
    </div>
    <div v-if="job && job.items">
      <div style="margin: 20px">任务状态： {{job.status}}</div>
      <el-timeline :reverse="true">
        <el-timeline-item
          v-for="(item, index) in job.items"
          :color="item.output.endsWith('success')? '#67C23A': item.output.includes('fail,') ? '#F56C6C':'#909399'"
          :key="index">
          <b style="padding-right: 10px">{{ index + 1 }}</b>{{ item.output }}
        </el-timeline-item>
      </el-timeline>
    </div>
    <div v-else style="margin: 20px">
      -无任何数据-
    </div>

    <el-dialog title="服务批量重启" :visible.sync="appRestartVisible">
      <el-form>
        <el-form-item label="服务列表" label-width="100">
          <el-input type="textarea" v-model="appRestartForm" autosize :autosize="{ minRows: 8, maxRows: 20}"></el-input>
        </el-form-item>
        <el-form-item label="">
          <div style="width: 100%;line-height: normal">
            内容为json格式，每行格式为 cluster/namespace/app，示例：
            <div style="background-color: #eee;padding: 10px;margin-top: 10px">
              [<br/>
              &nbsp;&nbsp;"k8s1/fstest/fs-aaa-bbb",<br/>
              &nbsp;&nbsp;"k8s1/fstest/fs-k8s-tomcat-test",<br/>
              &nbsp;&nbsp;"k8s1/firstshare/fs-k8s-tomcat-test"<br/>
              ]
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="appRestartVisible = false">取 消</el-button>
        <el-button type="primary" @click="createAppRestartJob">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {appRestartOutput, createAppRestart, removeAppRestart, startAppRestart, stopAppRestart} from "@/api/tool";
import appManageTab from "@/views/tool/app-manage-tab";

export default {
  components: {
    appManageTab
  },
  mounted() {
    this.loadAppRestartOutput()
    let _loadAppRestartOutput = this.loadAppRestartOutput
    this.timer = setInterval(function () {
      _loadAppRestartOutput()
    }, 3000)
  },
  beforeDestroy() {
    if (this.timer) {
      console.log("close timer")
      clearInterval(this.timer)
    }
  },
  computed: {},
  data() {
    return {
      timer: null,
      job: {
        items: [],
        status: 0
      },
      appRestartVisible: false,
      appRestartForm: ""
    }
  },
  methods: {
    loadAppRestartOutput() {
      appRestartOutput().then(response => {
        this.job = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    createAppRestartJob() {
      this.$confirm('是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        createAppRestart(this.appRestartForm).then(response => {
          this.appRestartVisible = false;
          this.$message.success("Success")
          this.loadAppRestartOutput()
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch(() => {
        console.log("取消批量重启")
      });
    },
    start() {
      startAppRestart(this.appRestartForm).then(response => {
        this.$message.success("Success")
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    stop() {
      stopAppRestart(this.appRestartForm).then(response => {
        this.$message.success("Success")
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    remove() {
      this.$confirm('确定要取消?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeAppRestart(this.appRestartForm).then(response => {
          this.$message.success("Success")
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch(() => {
        console.log("取消")
      });

    }
  }
}
</script>

<template>
  <div class="app-container">
    <pipeline-migrate-tab active-name="batch-clone"></pipeline-migrate-tab>
    <div>
      <div style="float: left;width: 600px;">
        <el-form>
          <el-form-item label="请输入发布流程列表">
            <el-input type="textarea" v-model="searchForm" autosize :autosize="{ minRows: 8, maxRows: 12}"></el-input>
          </el-form-item>
          <el-form-item>
            <el-popconfirm
              title="确定要克隆发布流程吗？"
              @confirm="query">
              <el-button slot="reference" type="primary" v-loading="btnLoading" size="mini">打开克隆页面</el-button>
            </el-popconfirm>
            <el-button type="default" @click="changeStatus('enabled')" v-loading="btnLoading" size="mini">批量改成【可用】</el-button>
            <el-button type="default" @click="changeStatus('disabled')" v-loading="btnLoading" size="mini">批量改成【禁用】</el-button>
            <el-button type="default" @click="changeStatus('migrated')" v-loading="btnLoading" size="mini">批量改成【已迁移】</el-button>
            <br/>
            <el-button type="default" @click="queryReplicas()" v-loading="btnLoading" size="mini">查询副本数</el-button>
            <el-popconfirm
              title="确定要修改副本数吗？"
              @confirm="updateReplicas">
              <el-button slot="reference" type="default" v-loading="btnLoading" size="mini">修改副本数</el-button>
            </el-popconfirm>
            <el-button type="default" @click="queryAddr()" v-loading="btnLoading" size="mini">查询地址</el-button>
            <el-button type="default" @click="queryMigrateAddr()" v-loading="btnLoading" size="mini">获取迁移地址</el-button>
            <br/>
            <el-button type="default" @click="createClickHouseSql()" v-loading="btnLoading" size="mini">Tomcat流量查询SQL生成</el-button>
            <el-button type="default" @click="createClickHouseSql2()" v-loading="btnLoading" size="mini">FCP流量查询SQL生成</el-button>
            <el-button type="default" @click="trafficAnalysis()" v-loading="btnLoading" size="mini">Tomcat流量查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="float: left;width: 500px;padding-left: 5px">
        <el-form>
          <el-form-item label="内容格式示例:" style="margin-bottom: 0;">
          </el-form-item>
          <el-form-item label="" style="margin-bottom: 0;">
            <div style="line-height: 1.2em;background-color: #eee;height: 180px;">
              # 克隆页面、修改状态、查询副本数：<br/>
              k8s0/fstest/fs-devops-console<br/>
              k8s0/fstest/fs-service-console<br/>
              k8s0/fstest/fs-stone-admin<br/><br/>
              # 修改副本数：<br/>
              k8s0/fstest/fs-devops-console/1<br/>
              k8s0/fstest/fs-service-console/1<br/>
              k8s0/fstest/fs-stone-admin/1
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div style="margin-top: 20px;max-width: 1080px;clear: both;" v-if="searchResult">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>操作结果</span>
        </div>
        <div>
          <pre style="white-space: pre-wrap;word-wrap: break-word;overflow-wrap: break-word;font-size:12px;">{{this.searchResult}}</pre>
        </div>
      </el-card>
    </div>

  </div>
</template>

<script>
import PipelineMigrateTab from "@/views/tool/pipeline-migrate-tab.vue";
import {migrateAddrQuery, migrateAddrQuery2, migrateTrafficAnalysis, pipelineBatchClone, pipelineBatchUpdateStatus, pipelineReplicaQuery, pipelineReplicaUpdate} from "@/api/tool";

export default {
  components: {
    PipelineMigrateTab
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {},
  data() {
    return {
      searchForm: "",
      searchResult: "",
      btnLoading: false

    }
  },
  methods: {
    clonePipeline(pipeline) {
      let routeUrl = this.$router.resolve({
        name: "app-pipeline-edit",
        query: {
          "operate": "clone",
          "pipelineId": pipeline.id,
          "targetCluster": "k8s0",
          "targetNamespace": pipeline.namespace,
        }
      });
      window.open(routeUrl.href, '_blank');
    },
    query() {
      this.btnLoading = true;
      pipelineBatchClone(this.searchForm).then(response => {
        this.searchResult = response.data.message;
        if (response.data.data.length > 0) {
          this.$message.info("即将在新窗口打开发布流程编辑页面")
          setTimeout(() => {
            for (let pipe of response.data.data) {
              this.clonePipeline(pipe);
            }
          }, 3000);
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.btnLoading = false;
      });
    },
    changeStatus(status) {
      pipelineBatchUpdateStatus(status, this.searchForm).then(response => {
        this.searchResult = response.data.message;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.btnLoading = false;
      });
    },
    queryReplicas() {
      this.btnLoading = true;
      pipelineReplicaQuery(this.searchForm).then(response => {
        this.searchResult = response.data.data.join("\n")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.btnLoading = false;
      });
    },
    updateReplicas() {
      this.btnLoading = true;
      pipelineReplicaUpdate(this.searchForm).then(response => {
        this.searchResult = response.data.data.join("\n")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.btnLoading = false;
      });
    },
    queryAddr() {
      this.btnLoading = true;
      migrateAddrQuery(this.searchForm).then(response => {
        this.searchResult = response.data.join("\n")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(()=>{
        this.btnLoading = false;
      });
    },
    queryMigrateAddr() {
      this.btnLoading = true;
      migrateAddrQuery2(this.searchForm).then(response => {
        this.searchResult = response.data.join("\n")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(()=>{
        this.btnLoading = false;
      });
    },
    createClickHouseSql() {
      this.btnLoading = true;
      let items = {}
      for(let line of this.searchForm.split("\n")) {
        if (line.trim() === "") {
          continue;
        }
        let [cluster, namespace, app,] = line.split("/");
        if (!items[cluster]) {
          items[cluster] = {}
        }
        if (!items[cluster][namespace]) {
          items[cluster][namespace] = []
        }
        items[cluster][namespace].push(app)
      }
      let sqlItems = []

      for(let cluster in items) {
        for(let namespace in items[cluster]) {
          let apps = items[cluster][namespace].join("','")
          let sql = `SELECT app, profile as namespace,COUNT(*) as count FROM logger.tomcat_access WHERE toDate(toDateTime(_time_second_)) > toDate(now() - 5*24*3600) AND cluster = 'foneshare-${cluster}' AND profile = '${namespace}' AND app IN ('${apps}') GROUP BY (app,profile);\n`
          sqlItems.push(sql)
        }
      }
      this.searchResult = sqlItems.join("\n")
      this.btnLoading = false;
    },
    createClickHouseSql2() {
      this.btnLoading = true;
      let items = []
      for(let line of this.searchForm.split("\n")) {
        if (line.trim()) {
          items.push(line.trim())
        }
      }
      let itemsStr = "'" + items.join("','") + "'"
      let sql = `SELECT server_ip, COUNT(*) as count FROM biz_log.log_cep_dist WHERE toDate(toDateTime(stamp)) > toDate(now() - 5*24*3600) AND  server_ip IN (${itemsStr}) GROUP BY server_ip;\n`
      this.searchResult = sql
      this.btnLoading = false;
    },
    trafficAnalysis() {
      this.btnLoading = true;
      migrateTrafficAnalysis(this.searchForm).then(response => {
        this.searchResult = JSON.stringify(response.data, null, 2)
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.btnLoading = false;
      });
    }
  }
}

</script>
<style>
</style>

<template>
  <div class="app-container">
    <app-manage-tab active-name="yaml-export"></app-manage-tab>
    <el-form ref="searchForm" :inline="true" :model="searchForm" style="height: 50px;">
      <el-form-item label="k8s集群" prop="cluster">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" @change="loadApp">
          <el-option
            v-for="item in this.clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运行环境" prop="namespace">
        <el-select v-model="searchForm.namespace" placeholder="选择Namespace" @change="loadApp">
          <el-option
            v-for="item in  this.namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用" prop="app">
        <el-select v-model="searchForm.app" filterable placeholder="选择应用" style="width: 340px;">
          <el-option
            v-for="item in this.appOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addApp">添加</el-button>
        <el-button type="text" @click="addAllApp">添加所有</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="this.tableData"
      row-key="name"
    >
      <el-table-column label="应用名" prop="app">
      </el-table-column>
      <el-table-column label="集群" prop="cluster">
      </el-table-column>
      <el-table-column label="环境" prop="namespace">
      </el-table-column>
    </el-table>
    <div style="padding-top: 10px;text-align: center">
      <el-button type="primary" size="small" @click="printYaml" icon="el-icon-view" v-loading="yamlExportBtnLoading">查看表格服务Yaml</el-button>
    </div>

    <el-dialog title="yaml内容" :visible.sync="yamlResultShow" width="70%" top="5vh" :close-on-click-modal="false">
      <div style="margin-top: -30px;">
        <el-button type="text" size="small" @click="copyToClipboard" icon="el-icon-document-copy" style="padding: 0">一键复制</el-button>
        <pre style="white-space: pre-wrap;border: 1px solid #eee;padding: 5px;margin-top: 5px">{{this.yamlResult}}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import appManageTab from "@/views/tool/app-manage-tab";
import {searchDeployment} from "@/api/k8s/app";
import {yamlExport} from "@/api/tool";
import fa from "element-ui/src/locale/lang/fa";

export default {
  components: {
    appManageTab
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    },
  },
  data() {
    return {
      searchForm: {
        cluster: '',
        namespace: '',
        app: '',
      },
      appOptions: [],
      tableData: [],
      yamlResultShow: false,
      yamlResult: "",
      yamlExportBtnLoading:false,
    }
  },
  methods: {
    loadApp() {
      let cluster = this.searchForm.cluster;
      let namespace = this.searchForm.namespace;
      if (!cluster || !namespace) {
        this.appOptions = [];
        return;
      }
      let vThis = this;
      searchDeployment(cluster, namespace).then(response => {
        vThis.appOptions = response.data;
      }).catch((e) => {
        console.error(e);
      });
    },
    addApp() {
      let cluster = this.searchForm.cluster
      let namespace = this.searchForm.namespace
      let app = this.searchForm.app
      if (!app) {
        this.$message.warning("请选择服务")
        return
      }
      let key = `${cluster}@@${namespace}@@${app}`

      for (let it of this.tableData) {
        if (it.key === key) {
          this.$message.warning("服务已存在")
          return
        }
      }

      let item = {
        "key": key,
        "cluster": cluster,
        "namespace": namespace,
        "app": app
      }
      this.tableData.push(item)
    },
    addAllApp() {
      this.$message.warning("todo")
    },
    printYaml() {
      if (this.tableData.size < 1) {
        this.$message.warning("请先添加服务")
        return
      }
      this.yamlExportBtnLoading = true
      this.yamlResult = ""
      let params = JSON.stringify(this.tableData)
      yamlExport(params).then(response => {
        this.yamlResult = response.data;
        this.yamlResultShow = true
      }).catch((e) => {
        console.error(e);
      }).finally(()=> {
        this.yamlExportBtnLoading = false
      })
    },
    copyToClipboard() {
      let text = this.yamlResult
      if(!text) {
        this.$message.warning("内容为空")
        return
      }
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message.success("复制成功")
        })
        .catch(() => {
          const input = document.createElement('input')
          document.body.appendChild(input)
          input.setAttribute('value', text)
          input.select()
          if (document.execCommand('copy')) {
            document.execCommand('copy')
          }
          document.body.removeChild(input)
          this.$message.success("复制成功")
        })
    }
  }
}
</script>

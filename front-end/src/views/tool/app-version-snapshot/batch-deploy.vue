<template>
  <div class="app-container">
    <div style="margin: 20px;" v-if="pageAlert">
      <el-alert
        :title="pageAlert"
        :closable="false"
        type="error"
        show-icon>
      </el-alert>
    </div>
    <el-card class="box-card">
      <div>
        <div style="font-weight: bold;color: #3a8ee6">快照信息：</div>
        <el-descriptions direction="vertical" :column="4" border>
          <el-descriptions-item label="快照名称">{{ appSnapshot.Column01 }}</el-descriptions-item>
          <el-descriptions-item label="快照ID">{{ appSnapshot.ID }}</el-descriptions-item>
          <el-descriptions-item label="快照集群">{{ appSnapshot.Column02 }}</el-descriptions-item>
          <el-descriptions-item label="快照环境">{{ appSnapshot.Column03 }}</el-descriptions-item>
          <el-descriptions-item label="创建人">{{ appSnapshot.Column05 }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ appSnapshot.Column04 }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ appSnapshot.CreatedAt }}</el-descriptions-item>
          <el-descriptions-item label="详细内容">
            <el-button type="text" @click="contentShow" size="mini">查看详情</el-button>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
    <el-card class="box-card" style="margin-top: 10px;">
      <div>
        <div style="font-weight: bold;color: #3a8ee6;">发布流程：</div>
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="tableRowClassName"
          element-loading-text="数据加载中..."
          border
          fit
          highlight-current-row>
          <el-table-column type="index">
          </el-table-column>
          <el-table-column
            type="selection"
            width="60" align="center">
          </el-table-column>
          <el-table-column
            label=""
            width="180">
            <template slot-scope="scope">
              <div style="">
                <router-link :to="{name: 'app-pipeline-execution-history', query: {'keyword': scope.row.app, 'namespace': scope.row.namespace}}" target="_blank">
                  <span style="color: #409EFF;font-size:12px;">发布历史</span>
                </router-link>
                <router-link :to="{name: 'cicd-app-deploy', query: {'app': scope.row.app}}" target="_blank">
                  <span style="color: #409EFF;font-size:12px;">发布流程</span>
                </router-link>
                <el-button
                  type="primary"
                  size="mini"
                  style="margin:0;padding: 5px 10px;"
                  @click="deploySubmit(scope.row)">发布
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="应用名"
                           prop="app">
          </el-table-column>
          <el-table-column label="集群"
                           prop="cluster">
          </el-table-column>
          <el-table-column label="环境"
                           prop="namespace">
          </el-table-column>
          <el-table-column label="状态" width="100px" align="center" prop="status">
          </el-table-column>
          <el-table-column label="实例数(运行/配置)" align="center" prop="replicas">
            <template slot-scope="scope">
              <div>
                {{ scope.row.extraAttr.runningPodNum }} / {{ scope.row.replicas }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="快照版本">
            <template slot-scope="scope">
            <span v-if="scope.row.extraAttr.snapshotVersion.indexOf('?') > -1" style="color: red;font-weight: bold;">
              {{ scope.row.extraAttr.snapshotVersion }}
            </span>
              <span v-else>
              {{ scope.row.extraAttr.snapshotVersion }}
            </span>
            </template>
          </el-table-column>
          <el-table-column label="当前运行版本">
            <template slot-scope="scope">
              <div v-if="scope.row.extraAttr.deployTag === '?'">
                <i class="el-icon-loading" style="font-size: 20px;color: red;font-weight: bold;"></i>
              </div>
              <div v-else-if="scope.row.extraAttr.deployTag !== scope.row.extraAttr.snapshotVersion" style="color: red;">
                {{ scope.row.extraAttr.deployTag }}

              </div>
              <div v-else>
                {{ scope.row.extraAttr.deployTag }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="应用负责人" property="extraAttr.owner">
          </el-table-column>
        </el-table>

      </div>
    </el-card>
    <el-dialog :title="contentDialog.title" :visible.sync="contentDialog.visible" width="70%" top="5vh" :close-on-click-modal="false">
      <div style="margin-top: -30px;overflow: auto;">
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ this.contentDialog.content }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {searchDeployment} from "@/api/k8s/app";
import {allAppOwners, findAppVersionSnapshot} from "@/api/tool";
import {searchPipeline} from "@/api/pipeline";
import {deployApp} from "@/api/job";

export default {
  name: "AppVersionSnapshotBatchDeploy",
  components: {},
  data() {
    return {
      cluster: "forceecrm-k8s1",
      namespace: "forceecrm-public-prod",
      tableData: [],
      pageAlert: "",
      tableLoading: false,
      appSnapshotId: "",
      appSnapshot: {
        ID: ""
      },
      contentDialog: {
        visible: false,
        title: '',
        content: ''
      }
    }
  },
  computed: {},
  mounted() {
    this.appSnapshotId = this.$route.query.snapshotId
    if (!this.appSnapshotId) {
      this.pageAlert = "请选择一个版本快照"
      return
    }
    if (this.$route.query.cluster) {
      this.cluster = this.$route.query.cluster
    }
    if (this.$route.query.namespace) {
      this.namespace = this.$route.query.namespace
    }
    this.loadAppSnapshot();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      let params = {
        "cluster": this.cluster,
        "namespace": this.namespace,
        "page": 1,
        "limit": 1000,
      }
      searchPipeline(params).then(response => {
        this.tableData = response.data.data
        for (let it of this.tableData) {
          it.extraAttr.deployTag = "?"
          it.extraAttr.runningPodNum = "?"
          it.extraAttr.owner = "?"
          it.extraAttr.deployParams = this.parseDeployParam(it)
          it.extraAttr.snapshotVersion = this.parseAppSnapshotVersion(it)
          // this.findDeployment(it.cluster, it.namespace, it.app)
        }
        this.loadDeployments()
        this.loadAppOwners()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    contentShow() {
      this.contentDialog.title = "快照名称：" + this.appSnapshot.Column01
      this.contentDialog.content = this.appSnapshot.Content
      this.contentDialog.visible = true
    },
    parseDeployParam(pipe) {
      let paramItem = {
        pipelineIds: pipe.id,
        maxSurge: "50%",
        remark: "",
        deployModuleImages: []
      }
      for (let mod of pipe.appModules) {
        let item = {
          gitUrl: mod.gitUrl,
          gitModule: mod.module,
          image: "",
        }
        for (let snapMod of this.appSnapshot.contentItems) {
          if (mod.gitUrl === snapMod.deployModule.gitUrl && mod.module === snapMod.deployModule.module) {
            if (snapMod.artifactImageSnapshot) {
              item.image = snapMod.artifactImageSnapshot.replaceAll("app-snapshot/", "")
              break
            }
          }
        }
        paramItem.deployModuleImages.push(item)
      }
      return {items:[].push(paramItem)}
    },
    parseAppSnapshotVersion(pipe) {
      let ret = []
      for (let it of pipe.extraAttr.deployParams.deployModuleImages) {
        if (it.image) {
          ret.push(it.image.split(":")[1].replaceAll("---", "/"))
        } else {
          ret.push("?")
        }
      }
      return ret.join("|")
    },
    loadAppSnapshot() {
      this.tableLoading = true;
      findAppVersionSnapshot(this.appSnapshotId).then(response => {
        this.appSnapshot = response.data
        this.appSnapshot.contentItems = JSON.parse(this.appSnapshot.Content)
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    tableRowClassName({row, rowIndex}) {
      return row.cluster + "-row"
    },
    loadAppOwners() {
      this.tableLoading = true;
      allAppOwners().then(response => {
        for (let pipe of this.tableData) {
          for (let it of response.data) {
            if (it.service === pipe.app) {
              if (it.owners) {
                pipe.extraAttr.owner = it.owners.join(",")
              }
              break
            }
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    loadDeployments() {
      this.tableLoading = true;
      searchDeployment(this.cluster, this.namespace).then(response => {
        for (let pipe of this.tableData) {
          for (let dep of response.data) {
            if (dep.name === pipe.app && dep.namespace === pipe.namespace) {
              pipe.extraAttr.deployTag = dep.deployTag
              pipe.extraAttr.runningPodNum = dep.replicas
              break
            }
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    deploySubmit(row) {
      let deployParams = row.extraAttr.deployParams
      if (!deployParams || !deployParams.deployModuleImages) {
        this.$message.error("没有在版本快照里找到镜像版本")
        return
      }
      for (let it of deployParams.deployModuleImages) {
        if (!it.image) {
          this.$message.error(`应用${row.app}的模块${it.gitModule}没有在版本快照里找到镜像版本`)
          return
        }
      }
      console.log(JSON.stringify(deployParams))
      this.tableLoading = true;
      deployApp(deployParams).then(response => {
        this.$message.success("操作成功，请到发布历史页查看详情")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    }
  },
}
</script>

<style>
</style>

<template>
  <div class="app-container">
    <app-manage-tab active-name="pipeline-clone"></app-manage-tab>
    <div style="width: 600px;margin: 0 auto;">
      <el-steps :active="active" finish-status="success">
        <el-step title="选择集群参数" process-status="error"></el-step>
        <!--        <el-step title="开始克隆" :status='cloneStatus' ></el-step>-->
        <el-step title="开始克隆"></el-step>
      </el-steps>
      <el-button style="margin-top: 12px;" :loading="loading" @click="next">{{ nextBtnText }}</el-button>
    </div>
    <el-divider></el-divider>
    <div class="app-container" v-if="active==='query-pipelines'">
      <el-form :inline="true" :rules="rules" ref="ruleForm" :model="form" class="demo-form-inline">
        <el-form-item label="从" prop="sourceCluster">
          <el-select v-model="form.sourceCluster" value-key="id" placeholder="源集群" filterable>
            <el-option
              v-for="item in clusterOptions"
              :key="item.id"
              :label="item.cluster + '/' + item.namespace"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="克隆到" prop="targetCluster">
          <el-select v-model="form.targetCluster" value-key="id" placeholder="目标集群" filterable>
            <el-option
              v-for="item in clusterOptions"
              :key="item.id"
              :label="item.cluster + '/' + item.namespace"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="active==='clone-pipelines'">
      <el-alert
        :title='form.sourceCluster.cluster + "/" + form.sourceCluster.namespace + "-->" + form.targetCluster.cluster + "/" +form.targetCluster.namespace'
        type="warning"
        center>
      </el-alert>
      <el-table ref="multipleTable"
                :data="clonePipelineTableData"
                size="mini"
                @selection-change="handleClonePipelineTableSelectionChange">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column type="index" label="序号" width="60"></el-table-column>
        <el-table-column property="app" label="所属应用"></el-table-column>
        <el-table-column property="status" label="发布流程状态"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {clonePipeline, getClonePipeline} from "@/api/pipeline";
import appManageTab from "@/views/tool/app-manage-tab";

export default {
  name: "clone",
  components: {
    appManageTab
  },
  data() {
    let validateClusterAndNamespace = (rule, value, callback) => {
      if (value.cluster === null && value.namespace === null) {
        callback(new Error('请选择集群'))
      } else {
        callback()
      }
    };
    return {
      active: "query-pipelines",
      form: {
        sourceCluster: {
          cluster: null,
          namespace: null
        },
        targetCluster: {
          cluster: null,
          namespace: null
        }
      },
      rules: {
        sourceCluster: [
          {validator: validateClusterAndNamespace, trigger: 'change'},
          {required: true, trigger: 'change'}
        ],
        targetCluster: [
          {validator: validateClusterAndNamespace, trigger: 'change'},
          {required: true, trigger: 'change'}
        ],
      },
      // dialogTableVisible: false,
      clonePipelineTableData: [],
      clonePipelineTableMultipleSelection: [],
      loading: false,
    }
  },
  computed: {
    nextBtnText: function () {
      if (this.active === "query-pipelines") {
        return "查询发布流程"
      } else if (this.active === "clone-pipelines") {
        return "克隆发布流程"
      } else {
        return "未知操作"
      }
    },
    clusterOptions: function () {
      let clusters = []
      let i = 0
      for (let clu of this.$settings.clusters) {
        for (let nm of clu.namespaces) {
          let c = {}
          c.cluster = clu.name
          c.namespace = nm
          c.id = i
          clusters.push(c)
          i++
        }
      }
      return clusters;
    },
  },
  methods: {
    next() {
      switch (this.active) {
        case "query-pipelines":
          this.$refs["ruleForm"].validate((valid) => {
            if (valid) {
              this.loading = true
              this.loadClonePipeline()
            } else {
              return false
            }
          })
          break;
        case "clone-pipelines":
          if (this.clonePipelineTableMultipleSelection.length <= 0) {
            this.$message({
              message: '未选择克隆的应用',
              type: 'warning'
            });
            return
          }
          this.loading = true
          this.clonePipeline()
      }
    },
    loadClonePipeline() {
      getClonePipeline(this.form.sourceCluster.cluster, this.form.sourceCluster.namespace, this.form.targetCluster.cluster, this.form.targetCluster.namespace).then(response => {
        this.active = "clone-pipelines"
        this.clonePipelineTableData = response.data
        // 不加这个
        this.$nextTick(() => {
          this.$refs.multipleTable.toggleAllSelection()
        })
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    handleClonePipelineTableSelectionChange(val) {
      this.clonePipelineTableMultipleSelection = val;
    },
    clonePipeline() {
      let pipelineIDs = []
      for (let v of this.clonePipelineTableMultipleSelection) {
        pipelineIDs.push(v.id)
      }

      let data = {}
      data.ids = pipelineIDs
      data.sourceCluster = this.form.sourceCluster.cluster
      data.sourceNamespace = this.form.sourceCluster.namespace
      data.targetCluster = this.form.targetCluster.cluster
      data.targetNamespace = this.form.targetCluster.namespace

      clonePipeline(data).then(response => {
        this.$message.success("操作成功")
        this.$alert('<pre style="overflow: auto;font-size: 10px;line-height:10px;">' + JSON.stringify(response.data, null, 2) + '</pre>', '操作成功，结果:', {
          dangerouslyUseHTMLString: true
        });
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>

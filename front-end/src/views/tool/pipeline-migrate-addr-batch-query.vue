<template>
  <div class="app-container">
    <pipeline-migrate-tab active-name="app-addr-batch-query"></pipeline-migrate-tab>
    <el-row style="max-width: 1080px;" :gutter="20">
      <el-col :span="12">
        <el-form >
          <el-form-item label="请输入要查询的服务列表">
            <el-input type="textarea" v-model="searchForm" autosize :autosize="{ minRows: 8, maxRows: 12}"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="query" v-loading="btnLoading">查 询</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <el-form>
          <el-form-item label="查询格式：集群/运行环境/应用名，应用之间用换行分割。示例:" style="margin-bottom: 0;">
          </el-form-item>
          <el-form-item label="" style="margin-bottom: 0;">
            <div style="line-height: 1.2em;background-color: #eee;height: 180px;padding-left: 5px">
              k8s1/festst/fs-devops-console<br/>
              k8s1/festst/i18n-console<br/>
              k8s1/festst/fs-service-console<br/>
              k8s1/festst/fs-stone-admin
            </div>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

  <div style="margin-top: 20px;max-width: 1080px" v-if="searchResult">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>查询结果 （格式：集群/运行环境/应用名/端口号 = 访问地址）</span>
      </div>
      <div>
        <pre>{{this.searchResult}}</pre>
      </div>
    </el-card>
  </div>
  </div>
</template>

<script>
import PipelineMigrateTab from "@/views/tool/pipeline-migrate-tab.vue";
import {migrateAddrQuery} from "@/api/tool";

export default {
  components: {
    PipelineMigrateTab
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {},
  data() {
    return {
      searchForm: "",
      searchResult: "",
      btnLoading:false

    }
  },
  methods: {
    query() {
      this.btnLoading = true;
      migrateAddrQuery(this.searchForm).then(response => {
        this.searchResult = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(()=>{
        this.btnLoading = false;
      });
    },
  }
}

</script>
<style>
</style>

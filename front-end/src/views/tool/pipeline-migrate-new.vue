<template>
  <div class="app-container">
    <pipeline-migrate-tab active-name="new-k8s"></pipeline-migrate-tab>
    <el-row style="max-width: 1080px;" :gutter="20">
      <el-col :span="18">
        <el-form>
          <el-form-item>
            <el-input type="textarea" v-model="searchForm" autosize :autosize="{ minRows: 5, maxRows: 5}" placeholder="输入需要过滤的发布流程列表，内容格式：集群/运行环境/应用名，多个之间用换行分割"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" @click="tableFilter" size="small">过滤</el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="数据加载中..."
      :default-sort="{prop: 'createdTime', order: 'ascending'}"
      border
      fit
      highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column
        type="selection"
        width="50">
      </el-table-column>
      <el-table-column
        label="操作"
        width="140px">
        <template slot-scope="scope">
          <el-button
            type="text"
            style="margin-top: 0;padding-top: 0;"
            @click="deployUseK8s1Tag(scope.row)">使用k8s1版本发布
          </el-button><br/>
          <router-link :to="{name: 'cicd-app-deploy', query: {'app': scope.row.app}}" target="_blank">
            <el-button
              type="text"
              style="margin-top: 0;padding-top: 0;">
              发布流程页
            </el-button>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="地址 (port / name)">
        <template slot-scope="scope">
          <div v-for="it in scope.row.ports" style="border: 1px #ccc solid;padding: 2px;margin-top: 3px">
            <div style="display: inline-block;">
              {{ it.value }} / {{ it.name }}
            </div>
            <el-button
              style="padding-top:0;padding-bottom:0"
              type="text"
              @click="updateCMSConfig(scope.row,it.value)">修改配置中心
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="集群"
                       prop="cluster">
      </el-table-column>
      <el-table-column label="环境"
                       :filters="namespaceOptions"
                       :filter-method="filterNamespace"
                       prop="namespace">
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="app">
      </el-table-column>

      <el-table-column label="状态" width="100px" align="center"
                       :filters="[{ text: '正常', value: 'enabled' }, { text: '已迁移', value: 'migrated' },{ text: '待审核', value: '待审核' }]"
                       :filter-method="filterStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'enabled'" type="success">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
          <el-tag v-else type="warning">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="配置实例数" align="center" prop="replicas">
      </el-table-column>
      <el-table-column label="创建人" property="author">
      </el-table-column>
      <el-table-column label="修改时间" prop="updatedTime" width="100px">
      </el-table-column>

    </el-table>

    <el-dialog title="地址修改确认" :visible.sync="cmsUpdateVisible" width="860px">
      <div>
        <el-descriptions :column="1" border>
          <el-descriptions-item>
            <template slot="label">
              <b>{{cmsUpdateOld.cluster}}</b> {{"/" + cmsUpdateOld.namespace + "/" + cmsUpdateOld.app }} 地址：
            </template>
            {{ cmsUpdateOld.text }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <b>{{cmsUpdateNew.cluster}}</b> {{"/" + cmsUpdateNew.namespace + "/" + cmsUpdateNew.app }} 地址：
            </template>
            {{ cmsUpdateNew.text }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <b style="font-size: 1.2em;">配置中心修改：</b>
            </template>
            {{ cmsUpdateOld.text }} → {{ cmsUpdateNew.text }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cmsUpdateVisible = false">取 消</el-button>
        <el-button type="primary" @click="cmsPage">去配置中心修改</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {searchPipeline} from "@/api/pipeline";
import {deploymentDetail} from "@/api/k8s/app";
import {getAppAddress} from "@/api/app";
import PipelineMigrateTab from "@/views/tool/pipeline-migrate-tab.vue";
import {appDeployWithOtherK8sTag} from "@/api/tool";

export default {
  components: {PipelineMigrateTab},
  data() {
    return {
      tableDataAll: [],
      tableData: [],
      searchForm:"",
      namespaceOptions: [],
      tableLoading: false,
      cmsUpdateVisible: false,
      cmsUpdateOld: {
        cluster: "",
        namespace: "",
        app: "",
        text:"",

      },
      cmsUpdateNew: {
        cluster: "",
        namespace: "",
        app: "",
        text:"",
      },
    }
  },
  computed: {},
  mounted() {
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchPipeline({"cluster": "k8s0", "page": 1, "limit": 10000}).then(response => {
        let nsSet = new Set();
        for(let pipe of response.data.data) {
          nsSet.add(pipe.namespace)
        }
        for(let ns of nsSet) {
          this.namespaceOptions.push({
            "text": ns,
            "value": ns
          })
        }

        this.tableDataAll = response.data.data
        // for (let it of this.tableDataAll) {
        //   it.extraAttr.deployTag = null
        //   it.extraAttr.runningPodNum = null
        //   this.findDeployment(it.cluster, it.namespace, it.app)
        // }
        this.tableFilter()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    tableFilter() {
      if(!this.searchForm || !this.searchForm.trim()) {
        this.tableData = this.tableDataAll;
        return
      }
      this.tableData = []
      this.searchForm = this.searchForm.replace(/ /g, '')
      if(!this.searchForm.endsWith("\n")) {
        this.searchForm += "\n"
      }
      console.log(this.searchForm)
      for(let it of this.tableDataAll) {
        if(this.searchForm.indexOf(`${it.cluster}/${it.namespace}/${it.app}\n`) > -1) {
          this.tableData.push(it)
        }
      }
    },
    async findDeployment(cluster, namespace, app) {
      let pipe = null
      for (let it of this.tableData) {
        if (it.cluster === cluster && it.namespace === namespace && it.app === app) {
          pipe = it
          break
        }
      }
      if (pipe === null) {
        this.$message.warning(`找不到发布流程，应用：${cluster} / ${namespace} / ${app}`)
        return
      }
      deploymentDetail(cluster, namespace, app).then(response => {
        pipe.extraAttr.deployTag = response.data.deployTag
        pipe.extraAttr.runningPodNum = response.data.replicas
      }).catch((e) => {
        pipe.extraAttr.deployTag = "--"
        pipe.extraAttr.runningPodNum = 0
        console.log(e.message)
      });
    },
    filterStatus(value,row) {
      return row.status === value
    },
    filterNamespace(value,row) {
      return row.namespace === value
    },
    updateCMSConfig(row, portNum) {
      if (row.cluster !== "k8s0") {
        this.$message.warning("目前只支持k8s0环境")
        return
      }
      getAppAddress("k8s0", row.namespace, row.app).then(response => {
        this.cmsUpdateOld= {}
        this.cmsUpdateNew= {}
        let port = null;
        for (let p of response.data) {
          if (p.port === portNum) {
            port = p
            break
          }
        }
        if (!port) {
          this.$message.error("找不到地址")
          return
        }
        this.cmsUpdateNew = {
          "cluster": "k8s0",
          "namespace": row.namespace,
          "app": row.app,
          "text": port.clusterOuterAddress[0]
        }
        getAppAddress("k8s1", row.namespace, row.app).then(response => {
          let port = null;
          for (let p of response.data) {
            if (p.port === portNum) {
              port = p
              break
            }
          }
          if (!port) {
            this.$message.error("找不到地址")
            return
          }
          this.cmsUpdateOld = {
            "cluster": "k8s1",
            "namespace": row.namespace,
            "app": row.app,
            "text": port.clusterOuterAddress[0]
          }
          this.cmsUpdateVisible = true;
        }).catch((e) => {
          this.$message.error(e.message);
        })
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    deployUseK8s1Tag(row) {
      this.$prompt(`确定要使用k8s1集群的版本发布应用 ${row.app} 吗？，如果要跳过镜像构建，输入 true，否则输入 false`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern:/^(true|false)$/,
        inputErrorMessage: '输入值只能是 true 或者 false',
        showInput:true,
        inputValue:'true',
        type: 'warning'
      }).then(({ value }) => {
        let params = {
          "cluster": row.cluster,
          "namespace": row.namespace,
          "app": row.app,
          "oldCluster": "k8s1",
          "skipImageBuild":  value,
          "forceCodeCompile":false,
        }
        appDeployWithOtherK8sTag(params).then(response => {
          this.$message.success(response.data)
        }).catch((e) => {
          this.$message.error(e.message);
        })

      }).catch(() => {
      });
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
    cmsPage() {
      let url = "https://oss.foneshare.cn/cms/replace/preview/?profile="
      url += "&src=" + this.cmsUpdateOld.text + "&dst=" + this.cmsUpdateNew.text
      window.open(url, '_blank');
    },
    convertStatus(status) {
      switch (status) {
        case "enabled":
          return "正常"
        case "audit":
          return "待审核"
        case "migrated":
          return "已迁移"
        default:
          return "未知"
      }
    }
  }
}
</script>


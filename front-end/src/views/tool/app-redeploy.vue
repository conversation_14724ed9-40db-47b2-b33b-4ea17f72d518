<template>
  <div class="app-container">
    <app-manage-tab active-name="app-redeploy"></app-manage-tab>
    <el-alert
      title="应用批量重发"
      type="info"
      description="使用当前运行的版本重新发布应用。 操作步骤：创建任务 → 开始 → 每间隔1分钟发布一个服务"
      show-icon>
    </el-alert>
    <div style="margin: 10px">
      <el-button type="primary" @click="appRestartVisible=true" size="small">创建任务</el-button>
      <div v-if="job && job.items" style="display: inline-block;margin-left: 30px;">
        <el-button v-if="job.status ===0" type="text" @click="start" size="small">开始</el-button>
        <el-button v-if="job.status ===1" type="text" @click="stop" size="small">暂停</el-button>
        <el-button type="text" @click="remove" size="small">删除</el-button>
      </div>
    </div>
    <div v-if="job && job.items">
      <div style="margin: 20px">任务状态： {{ job.status }}</div>
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in job.items"
          :color="item.output.endsWith('success')? '#67C23A': item.output.includes('fail,') ? '#F56C6C':'#909399'"
          :key="index">
          <b style="padding-right: 10px">{{ index + 1 }}</b>{{ item.output }}
        </el-timeline-item>
      </el-timeline>
    </div>
    <div v-else style="margin: 20px">
      -无任何数据-
    </div>

    <el-dialog title="服务批量重发" :visible.sync="appRestartVisible">
      <el-form label-width="100px">
        <el-form-item label="服务列表" >
          <slot name="label">
            内容为json格式，每行格式为 cluster/namespace/app，
            <el-popover
              placement="bottom-end"
              trigger="click">
              <div>
                [<br/>
                &nbsp;&nbsp;"k8s0/fstest/fs-aaa-bbb",<br/>
                &nbsp;&nbsp;"k8s0/fstest/fs-k8s-tomcat-test",<br/>
                &nbsp;&nbsp;"k8s0/firstshare/fs-k8s-tomcat-test"<br/>
                ]
              </div>
              <el-button type="text" slot="reference">点击查看示例</el-button>
            </el-popover>
          </slot>
          <el-input type="textarea" v-model="appRestartForm" autosize :autosize="{ minRows: 8, maxRows: 20}"></el-input>
        </el-form-item>
        <el-form-item label="发版参数" >
          <el-checkbox v-model="forceCodeCompile">【强制代码编译】</el-checkbox>
          <el-checkbox v-model="dependencyCheck">【依赖包版本校验】</el-checkbox>
        </el-form-item>
        <el-form-item label="父POM " >
          <el-input v-model="parentPom" style="width: 80%" placeholder="如果不填写，则使用上一次发版的父POM"></el-input>
        </el-form-item>
        <el-form-item label="tag后缀">
          <el-input v-model="suffixVersion" style="width: 80%" placeholder="tag后缀"></el-input>
        </el-form-item>
        <el-form-item label-width="100" style="margin-top: -20px;">
          <div style="color: #666;font-size: 12px;padding-left: 60px;">备注：如果填写了tag后缀，使用当前运行tag+后缀名创建新tag发布；否则，使用当前运行tag重发</div>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="message" style="width: 80%" placeholder="输入备注"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="appRestartVisible = false">取 消</el-button>
        <el-button type="primary" @click="create">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {appDeployOutput, createAppDeploy, removeAppDeploy, startAppDeploy, stopAppDeploy} from "@/api/tool";
import appManageTab from "@/views/tool/app-manage-tab";

export default {
  components: {
    appManageTab
  },
  mounted() {
    this.loadOutput()
    let _loadOutput = this.loadOutput
    this.timer = setInterval(function () {
      _loadOutput()
    }, 3000)
  },
  beforeDestroy() {
    if (this.timer) {
      console.log("close timer")
      clearInterval(this.timer)
    }
  },
  computed: {},
  data() {
    return {
      timer: null,
      job: {
        items: [],
        status: 0
      },
      appRestartVisible: false,
      forceCodeCompile: true,
      dependencyCheck: true,
      suffixVersion: "",
      message: "",
      appRestartForm: "",
      parentPom:""
    }
  },
  methods: {
    loadOutput() {
      appDeployOutput().then(response => {
        this.job = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    create() {
      this.$confirm('是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        createAppDeploy("redeploy","",this.suffixVersion, this.message,this.forceCodeCompile,this.dependencyCheck,this.parentPom, this.appRestartForm).then(response => {
          this.appRestartVisible = false;
          this.$message.success("Success")
          this.loadOutput()
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch(() => {
        console.log("取消")
      });
    },
    start() {
      startAppDeploy(this.appRestartForm).then(response => {
        this.$message.success("Success")
        this.loadOutput()
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    stop() {
      stopAppDeploy(this.appRestartForm).then(response => {
        this.$message.success("Success")
        this.loadOutput()
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    remove() {
      this.$confirm('确定要取消?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeAppDeploy(this.appRestartForm).then(response => {
          this.$message.success("Success")
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch(() => {
        console.log("取消")
      });

    }
  }
}
</script>

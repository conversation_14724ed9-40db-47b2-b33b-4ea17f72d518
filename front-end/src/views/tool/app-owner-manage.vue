<template>
  <div class="app-container">
    <el-alert
      style="font-size: 1.2em;background-color: unset;border: solid 1px #898282;margin-bottom: 10px;font-weight: bold;color:#333"
      title="应用负责人管理页面"
      type="info"
      :closable="false"
      description=""
      show-icon>
      <template>
        <div style="font-weight: bold;color:#333">
          发布系统、监控告警系统会使用这些负责人信息。
        </div>
      </template>
    </el-alert>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>应用负责人</span><span style="font-size: 14px;">(数据存储于配置中心的 cmdb-mark-v2.json 文件）</span>
        <el-button type="primary" size="mini" @click="toEditPage">去修改</el-button>
        <export-button :table-ref="this.$refs.table001" style="float: right"></export-button>
      </div>
      <el-table :data="tableData" style="width: 100%" v-loading="loading" ref="table001">
        <el-table-column prop="service" label="应用名称" />
        <el-table-column prop="mainOwner" label="主负责人">
          <template slot-scope="scope">
            {{ scope.row.mainOwner ? scope.row.mainOwner.join(', ') : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="owners" label="负责人">
          <template slot-scope="scope">
            {{ scope.row.owners ? scope.row.owners.join(', ') : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="desc" label="描述" />
      </el-table>
    </el-card>
  </div>
</template>

<script>

import {searchCmdbOwner} from "@/api/operation";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: 'AppOwnerManage',
  components: {ExportButton},
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  created() {
    this.loadTable()
  },
  methods: {
    loadTable() {
      this.loading = true
      searchCmdbOwner().then(response => {
        this.tableData = response.data
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false
      })
    },
    toEditPage() {
      window.open("https://oss.foneshare.cn/cms/edit/config/25292", '_blank');
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>

<template>
  <div style="margin-top: -10px">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="老集群（k8s1)" name="old-k8s">
      </el-tab-pane>
      <el-tab-pane label="新集群（k8s0)" name="new-k8s">
      </el-tab-pane>
      <el-tab-pane label="应用迁移处理" name="migrate-operation">
      </el-tab-pane>
      <el-tab-pane label="发布流程处理" name="pipeline-batch-operation">
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  props: {
    activeName: {
      type: String,
      default: ''
    }
  },
  mounted() {
  },
  computed: {},
  data() {
    return {
      activeTab: this.activeName
    }
  },
  methods: {
    handleClick(tab, event) {
      let tabName = tab.name
      if (tabName === "old-k8s") {
        this.$router.push({name: 'pipeline-migrate-old'});
      } else if (tabName === "new-k8s") {
        this.$router.push({name: 'pipeline-migrate-new'});
      } else if (tabName === "migrate-operation") {
        this.$router.push({name: 'pipeline-migrate-operation'});
      }else if (tabName === "pipeline-batch-operation") {
        this.$router.push({name: 'pipeline-batch-operation'});
      }else {
        this.$message.error("未知操作")
      }
    }
  }
}
</script>

<template>
  <div class="app-container" v-loading="pageLoading">
    <app-manage-tab active-name="pipeline-resource-update"></app-manage-tab>
    <el-row style="max-width: 1480px;" :gutter="20">
      <el-col :span="12">
        <el-form >
          <el-form-item label="请输入参数">
            <el-input type="textarea" v-model="form" autosize :autosize="{ minRows: 12, maxRows: 12}"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submit">提 交</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <el-form>
          <el-form-item label="参数格式模版（CPU单位：m，内存单位：Mi)" style="margin-bottom: 0;">
          </el-form-item>
          <el-form-item label="" style="margin-bottom: 0;">
            <div style="background-color: #eee;overflow: auto;height: 270px">
                          <pre style="line-height: normal">
[
  {
    "cluster": "k8s0",
    "namespace": "fstest",
    "app": "fs-k8s-tomcat-test",
    "requestCPU": 200,
    "limitCPU": 1000,
    "requestMemory": -1,
    "limitMemory": -1,
    "replicas": -1
  },
  {
    "cluster": "k8s0",
    "namespace": "fstest",
    "app": "fs-app-test",
    "requestCPU": 100,
    "limitCPU": 500,
    "requestMemory": 128,
    "limitMemory": 512,
    "replicas": -1
  },

  }
]
            </pre>
            </div>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

  <div style="margin-top: 20px;max-width: 1480px" v-if="result">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>操作结果</span>
      </div>
      <div>
        <pre style="font-size: 12px;">{{this.result}}</pre>
      </div>
    </el-card>
  </div>
  </div>
</template>

<script>
import PipelineMigrateTab from "@/views/tool/pipeline-migrate-tab.vue";
import appManageTab from "@/views/tool/app-manage-tab.vue";
import {pipelineBatchResourceUpdate} from "@/api/tool";

export default {
  components: {
    appManageTab,
    PipelineMigrateTab
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {},
  data() {
    return {
      form: "",
      result: "",
      pageLoading:false

    }
  },
  methods: {
    submit() {
      this.$confirm('确定要继续修改发布流程资源吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageLoading = true;
        pipelineBatchResourceUpdate(this.form).then(response => {
          this.result = response.data;
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(()=>{
          this.pageLoading = false;
        });
      }).catch(() => {

      });
    },
  }
}

</script>
<style>
</style>

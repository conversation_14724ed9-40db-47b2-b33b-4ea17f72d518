<template>
  <div class="app-container" v-loading="loading">
    <app-manage-tab active-name="pod-deregister-manage"></app-manage-tab>
    <div>
      <el-form :inline="true" :model="form">
        <el-form-item label="环境">
          <el-select v-model="form.cluster" filterable style="width: 360px;">
            <el-option
              v-for="item in clusterOptions"
              :key="item.id"
              :label="item.cluster"
              :value="item">
            </el-option>
          </el-select>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="data" style="width: 100%">
        <el-table-column prop="metadata.name" label="Pod"></el-table-column>
        <el-table-column prop="cluster" label="集群"></el-table-column>
        <el-table-column prop="namespace" label="命名空间"></el-table-column>
        <el-table-column prop="app" label="应用"></el-table-column>
        <el-table-column label="">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="podPage(scope.row.cluster, scope.row.namespace, scope.row.app)">实例管理页</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>


  </div>
</template>

<script>
import {podDeregisterListByCluster} from "@/api/k8s/pod";
import appManageTab from "@/views/tool/app-manage-tab";

export default {
  components: {
    appManageTab
  },
  data() {
    return {
      form: {
        cluster: null,
      },
      data: [],
      loading: false
    }
  },
  computed: {
    clusterOptions: function () {
      const clusters = []
      for (const clu of this.$settings.clusters) {
        clusters.push(clu.name)
      }
      return clusters
    }
  },
  methods: {
    loadData() {
      if (!this.form.cluster) {
        this.$message.error('请选择环境')
        return
      }
      this.loading = true
      podDeregisterListByCluster(this.form.cluster).then(response => {
        this.data = response.data
        for (const pod of this.data) {
          pod.cluster = this.form.cluster
          pod.namespace = pod.metadata.namespace
          pod.app = pod.metadata.labels.app.replace('-close', '')
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    podPage(cluster, namespace, app) {
      let routeUrl = this.$router.resolve({
        name: "pod-index",
        query: {
          "cluster": cluster,
          "namespace": namespace,
          "app": app,
        }
      });
      window.open(routeUrl.href, '_blank');
    },
  }
}
</script>

<style scoped>

</style>

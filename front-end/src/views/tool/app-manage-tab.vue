<template>
  <div style="margin-top: -10px" class="tool-app-manage-tab">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="应用清理" name="app-gc">
      </el-tab-pane>
      <el-tab-pane label="摘除Pod管理" name="pod-deregister-manage">
      </el-tab-pane>
      <el-tab-pane label="服务定时重启" name="app-cron-reboot">
      </el-tab-pane>
      <el-tab-pane label="应用批量重启" name="app-restart">
      </el-tab-pane>
      <el-tab-pane label="发布流程-资源批量修改" name="pipeline-resource-update">
      </el-tab-pane>
      <el-tab-pane label="应用批量重发" name="app-redeploy">
      </el-tab-pane>
      <el-tab-pane label="应用批量重发V2" name="app-redeploy-v2">
      </el-tab-pane>
      <el-tab-pane label="应用批量构建V2" name="app-build-v2">
      </el-tab-pane>
      <el-tab-pane label="应用批量发布" name="app-deploy">
      </el-tab-pane>
      <el-tab-pane label="发布流程克隆" name="pipeline-clone">
      </el-tab-pane>
      <el-tab-pane label="配置文件迁移" name="cms-config-migrate">
      </el-tab-pane>
      <el-tab-pane label="专属云应用发布助手" name="dedicated-cloud-publish-helper">
      </el-tab-pane>
      <!--      <el-tab-pane label="Yaml导出" name="yaml-export">-->
      <!--      </el-tab-pane>-->
      <el-tab-pane label="helm chart创建" name="helm-chart-build">
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  props: {
    activeName: {
      type: String,
      default: ''
    }
  },
  mounted() {
  },
  computed: {},
  data() {
    return {
      activeTab: this.activeName
    }
  },
  methods: {
    handleClick(tab, event) {
      let tabName = tab.name
      if (tabName === "app-restart") {
        this.$router.push({name: 'tool-app-restart'});
      } else if (tabName === "app-redeploy") {
        this.$router.push({name: 'tool-app-redeploy'});
      } else if (tabName === "app-redeploy-v2") {
        this.$router.push({name: 'tool-app-redeploy-v2'});
      } else if (tabName === "app-deploy") {
        this.$router.push({name: 'tool-app-deploy'});
      } else if (tabName === "app-build-v2") {
        this.$router.push({name: 'tool-app-build-v2'});
      } else if (tabName === "pipeline-clone") {
        this.$router.push({name: 'tool-pipeline-clone-by-namespace'});
      } else if (tabName === "dedicated-cloud-publish-helper") {
        this.$router.push({name: 'tool-dedicated-cloud-publish-helper'});
      } else if (tabName === "app-gc") {
        this.$router.push({name: 'tool-app-gc'});
      } else if (tabName === "app-cron-reboot") {
        this.$router.push({name: 'tool-app-reboot'});
      } else if (tabName === "yaml-export") {
        this.$router.push({name: 'tool-yaml-export'});
      } else if (tabName === "helm-chart-build") {
        this.$router.push({name: 'tool-helm-chart-build'});
      } else if (tabName === "pipeline-resource-update") {
        this.$router.push({name: 'tool-pipeline-resource-update'});
      } else if (tabName === "cms-config-migrate") {
        this.$router.push({name: 'tool-cms-config-migrate'});
      } else if (tabName === "pod-deregister-manage") {
        this.$router.push({name: 'tool-pod-deregister-manage'});
      } else {
        this.$message.error("未知操作")
      }
    }
  }
}
</script>

<style>
.tool-app-manage-tab .el-tabs__item {
  padding: 0 10px;
}
</style>


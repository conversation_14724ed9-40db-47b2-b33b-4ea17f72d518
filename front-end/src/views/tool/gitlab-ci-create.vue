<template>
  <div class="app-container" style="max-width: 840px;">
    <el-divider content-position="center" style="padding: 10px 0"><b style="font-size: 1.2em;">说明</b></el-divider>
    <div style="font-size: 14px;color: #444;">
      非Java系列的应用我们是通过Gitlab CI 来部署到k8s里面，大家可以通过当前页面配置和下载部署文件。<br/>
      Wiki文档：
      <a target="_blank" href="http://wiki.firstshare.cn/pages/viewpage.action?pageId=103195231">
        <el-button type="text">http://wiki.firstshare.cn/pages/viewpage.action?pageId=103195231</el-button>
      </a>
      有不明白的地方可以联系运维同学
    </div>
    <el-divider content-position="center" style="padding: 10px 0"><b style="font-size: 1.2em;">GitLab CI 部署配置文件下载</b></el-divider>
    <el-form ref="form" :rules="rules" :model="form" label-width="120px">
      <el-form-item label="应用名" prop="app">
        <el-tooltip class="item" effect="dark" placement="right-start">
          <div slot="content" style="max-width: 400px;">
            输入部署到k8s的应用名，一般跟Git工程名一致
          </div>
          <el-input v-model="form.app"/>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="开发语言" prop="language">
        <el-select v-model="form.language">
          <el-option
            v-for="item in languageOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实例数">
        <el-input-number v-model="form.replicas" :step="1" :min="1" :max="10"></el-input-number>
      </el-form-item>
      <el-form-item label="CPU">
        <el-input-number v-model="form.cpu" :step="0.1" :precision="2" :min="0.1" :max="8"></el-input-number>&nbsp;&nbsp;（单位：Core）
      </el-form-item>
      <el-form-item label="内存">
        <el-input-number v-model="form.memory" :step="256" :min="125" :max="10240"></el-input-number>&nbsp;&nbsp;（单位：MB）
      </el-form-item>
      <el-form-item label="运行环境" prop="namespace">
        <el-checkbox-group v-model="form.namespace">
          <el-col :span="6">
            <div>
              <el-checkbox label="fstest" border></el-checkbox>
            </div>
            <div>
              <el-checkbox label="firstshare" border></el-checkbox>
            </div>
            <div>
              <el-checkbox label="foneshare" border></el-checkbox>
            </div>
          </el-col>
          <el-col :span="6">
            <div>
              <el-checkbox label="fstest-gray" border></el-checkbox>
            </div>
            <div>
              <el-checkbox label="firstshare-gray" border></el-checkbox>
            </div>
            <div>
              <el-checkbox label="foneshare-gray" border></el-checkbox>
            </div>
          </el-col>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="端口" prop="ports">
        <div v-for="item in form.ports" style="margin: 5px 0;">
          <el-input placeholder="端口名"
                    clearable
                    style="display: inline-block;width: 200px;"
                    :disabled="item.disabled"
                    v-model="item.name"
          >
          </el-input>
          =
          <el-input-number placeholder="端口值"
                           controls-position="right"
                           style="display: inline-block;width: 150px;"
                           :min="0"
                           :max="65535"
                           :step="1000"
                           v-model="item.port"
                           :disabled="item.disabled"
          >
          </el-input-number>
          <el-button @click.prevent="removePort(item)" v-show="item.removable">删除</el-button>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="addPort">添加端口</el-button>
        <el-button type="primary" @click="onSubmit('form')" :loading="submitLoading">下载【k8s manifest】文件</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {createGitLabCIFile, downloadGitLabCIFile} from '../../api/gitlab'

export default {
  data() {
    let validatePorts = (rule, value, callback) => {
      console.log(value);
      for (let i in value) {
        let p = value[i]
        if (!p.name || !p.port) {
          callback(new Error('请输入所有端口的名称和值'));
          return
        }
      }
      callback();
    };
    return {
      submitLoading: false,
      languageOption: [
        {
          value: 'Javascript',
          label: 'Javascript'
        },
        {
          value: 'Python',
          label: 'Python'
        },
        {
          value: 'Golang',
          label: 'Golang'
        },
        {
          value: 'Java',
          label: 'Java'
        },
        {
          value: 'PHP',
          label: 'PHP'
        },
        {
          value: 'Unknown',
          label: 'Unknown'
        }
      ],
      form: {
        app: '',
        language: '',
        namespace: [],
        replicas: 1,
        cpu: 0.2,
        memory: 256,
        ports: [
          {
            name: "http",
            port: 80,
            disabled: true,
            removable: false
          }
        ],
      },
      rules: {
        app: [
          {required: true, message: '请输入应用名称'},
          {min: 4, message: '最少4个字符'}
        ],
        language: [
          {required: true, message: '请选择语言'}
        ],
        namespace: [
          {type: 'array', required: true, message: '请至少选择一个运行环境'}
        ],
        ports: [
          {validator: validatePorts, message: '请输入所有端口的名称和值'}
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return
        }
        this.submitLoading = true;
        createGitLabCIFile(this.form).then(response => {
          this.submitLoading = false;
          downloadGitLabCIFile(response.data);
        }).catch(() => {
          this.submitLoading = false;
        });
      })
    },
    addPort() {
      this.form.ports.push({
        name: '',
        value: '',
        disabled: false,
        removable: true
      });
    },
    removePort(item) {
      let index = this.form.ports.indexOf(item);
      if (index !== -1) {
        this.form.ports.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped>
.line {
  text-align: center;
}
</style>


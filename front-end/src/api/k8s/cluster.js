import request from "@/utils/request";


export function nodeCapacity(params) {
  return request({
    url: '/v1/k8s/cluster/capacity',
    method: 'get',
    params
  })
}

export function podCapacity(params) {
  return request({
    url: '/v1/k8s/cluster/pod/capacity',
    method: 'get',
    params
  })
}

export function imagePreheat(data) {
  return request({
    url: `/v1/k8s/image/preheat`,
    method: 'post',
    data
  })
}

export function getImagePreheatJob(cluster, namespace) {
  return request({
    url: `/v1/k8s/image/preheat/jobs?cluster=${cluster}&namespace=${namespace}`,
    method: 'get',
  })
}

export function deleteImagePreheatJob(cluster, namespace,job) {
  return request({
    url: `/v1/k8s/image/preheat/jobs?cluster=${cluster}&namespace=${namespace}&job=${job}`,
    method: 'delete',
  })
}



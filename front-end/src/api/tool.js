import request from '@/utils/request'


export function findAppByAddress(address) {
  return request({
    url: '/v1/tool/find-app-by-address',
    method: 'get',
    params: {
      address
    }
  })
}

export function findPodByIP(address) {
  return request({
    url: '/v1/tool/find-pod-by-ip',
    method: 'get',
    params: {
      address
    }
  })
}

export function scanJar(params) {
  return request({
    url: '/v1/tool/scan-jar',
    method: 'get',
    params
  })
}

export function scanTomcatVersion(params) {
  return request({
    url: '/v1/tool/scan-tomcat-version',
    method: 'get',
    params
  })
}

export function appRestartOutput() {
  return request({
    url: '/v1/tool/app-restart/output',
    method: 'get',
  })
}

export function createAppRestart(data) {
  return request({
    url: '/v1/tool/app-restart/create',
    method: 'post',
    data
  })
}

export function startAppRestart(data) {
  return request({
    url: '/v1/tool/app-restart/start',
    method: 'post',
    data
  })
}

export function stopAppRestart(data) {
  return request({
    url: '/v1/tool/app-restart/stop',
    method: 'post',
    data
  })
}

export function removeAppRestart(data) {
  return request({
    url: '/v1/tool/app-restart/remove',
    method: 'post',
    data
  })
}


export function appDeployOutput() {
  return request({
    url: '/v1/tool/app-deploy/output',
    method: 'get',
  })
}

export function createAppDeploy(type, fixVersion, suffixVersion, message, forceCodeCompile,dependencyCheck, parentPom, data) {
  return request({
    url: `/v1/tool/app-deploy/create?type=${type}&forceCodeCompile=${forceCodeCompile}&fixVersion=${fixVersion}&suffixVersion=${suffixVersion}&message=${message}&dependencyCheck=${dependencyCheck}&parentPom=${parentPom}`,
    method: 'post',
    data
  })
}

export function startAppDeploy(data) {
  return request({
    url: '/v1/tool/app-deploy/start',
    method: 'post',
    data
  })
}

export function stopAppDeploy(data) {
  return request({
    url: '/v1/tool/app-deploy/stop',
    method: 'post',
    data
  })
}

export function removeAppDeploy(data) {
  return request({
    url: '/v1/tool/app-deploy/remove',
    method: 'post',
    data
  })
}

export function yamlExport(data) {
  return request({
    url: '/v1/tool/yaml-export',
    method: 'post',
    data
  })
}

export function helmChartBuild(cluster, namespace, app, overrideNamespace) {
  return request({
    url: `/v1/tool/helm-chart-build?cluster=${cluster}&namespace=${namespace}&app=${app}&overrideNamespace=${overrideNamespace}`,
    method: 'post'
  })
}

export function appVersionSnapshot(cluster, namespace, version, remark,dryRun) {
  return request({
    url: `/v1/tool/app-version-snapshot?cluster=${cluster}&namespace=${namespace}&version=${version}&remark=${remark}&dryRun=${dryRun}`,
    method: 'post'
  })
}

export function allAppOwners() {
  return request({
    url: `/v1/tool/all-app-owners`,
    method: 'get'
  })
}

export function searchAppVersionSnapshot() {
  return request({
    url: `/v1/tool/app-version-snapshot/search`,
    method: 'get'
  })
}

export function findAppVersionSnapshot(id) {
  return request({
    url: `/v1/tool/app-version-snapshot/detail?id=${id}`,
    method: 'get'
  })
}

export function deleteAppVersionSnapshot(id) {
  return request({
    url: `/v1/tool/app-version-snapshot/delete?id=${id}`,
    method: 'delete'
  })
}

export function pipelineBatchClone(data) {
  return request({
    url: '/v1/tool/pipeline-batch-clone',
    method: 'post',
    data
  })
}

export function pipelineReplicaQuery(data) {
  return request({
    url: '/v1/tool/pipeline-replica-query',
    method: 'post',
    data
  })
}

export function pipelineReplicaUpdate(data) {
  return request({
    url: '/v1/tool/pipeline-replica-update',
    method: 'post',
    data
  })
}

export function migrateAddrQuery(data) {
  return request({
    url: '/v1/tool/migrate-addr-query',
    method: 'post',
    data
  })
}
export function migrateAddrQuery2(data) {
  return request({
    url: '/v1/tool/migrate-addr-query2',
    method: 'post',
    data
  })
}

export function pipelineBatchResourceUpdate(data) {
  return request({
    url: '/v1/tool/pipeline-batch-resource-update',
    method: 'post',
    data
  })
}

export function pipelineBatchUpdateStatus(status, data) {
  return request({
    url: '/v1/tool/pipeline-batch-update-status?status=' + status,
    method: 'post',
    data
  })
}

export function appDeployWithOtherK8sTag(params) {
  return request({
    url: '/v1/tool/app-deploy-with-old-k8s-tag',
    method: 'get',
    params
  })
}
export function migratePipelineSearch(data) {
  return request({
    url: '/v1/tool/migrate/pipeline-search',
    method: 'post',
    data
  })
}

export function migrateTrafficAnalysis(data) {
  return request({
    url: '/v1/tool/migrate/traffic-analysis?day=7',
    method: 'post',
    data
  })
}


export function loadCmsProfileConfigs(cluster, namespace, op) {
  return request({
    url: `/v1/tool/load-cms-profile-configs?cluster=${cluster}&namespace=${namespace}&op=${op}`,
    method: 'get',
  })
}

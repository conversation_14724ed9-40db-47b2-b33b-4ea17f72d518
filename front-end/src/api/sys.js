import request from '@/utils/request'

export function getSysLog(tailLines) {
  return request({
    url: '/v1/sys/log',
    method: 'get',
    params: {
      tailLines: tailLines
    }
  })
}

export function getSysSetting() {
  return request({
    url: '/v1/sys/setting',
    method: 'get',
  })
}

export function getOncallConfig() {
  return request({
    url: '/v1/sys/oncall-config',
    method: 'get',
  })
}

export function clearCache() {
  return request({
    url: '/v1/sys/setting/cache',
    method: 'delete',
  })
}

export function updateSettings(data) {
  return request({
    url: `/v1/sys/setting/edit`,
    method: 'post',
    data,
  })
}

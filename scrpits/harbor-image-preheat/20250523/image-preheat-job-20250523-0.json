{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250523-0", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-0-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--**********04", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.5-all-develop---9.5.5-20250523", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:v9.5.5-all-develop---9.5.5-20250523", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-mq-provider:v956-vip-20250524-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-develop-20250523-955", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-si/fs-fxiaoke-connector:v2.9.113-develop-20250523", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--**********44", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--**********37", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
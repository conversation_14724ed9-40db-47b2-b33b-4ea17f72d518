{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250523-2", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-2-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221739", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221733", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221710", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.5.5-all2-develop---9.5.5-20250523", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221822", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221709", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
{"metadata": {"name": "fs-image-preheat-************-ljm4b", "generateName": "fs-image-preheat-************-", "namespace": "kube-public", "uid": "22ef6f45-2a09-4734-a6ef-8be4d698feca", "resourceVersion": "6012991", "creationTimestamp": "2025-05-23T14:28:07Z", "labels": {"app": "fs-image-preheat", "batch.kubernetes.io/controller-uid": "1db867b4-5ced-425f-8b56-952f2769d7c3", "batch.kubernetes.io/job-name": "fs-image-preheat-************", "controller-uid": "1db867b4-5ced-425f-8b56-952f2769d7c3", "job-name": "fs-image-preheat-************", "recentHours": "30"}, "ownerReferences": [{"apiVersion": "batch/v1", "kind": "Job", "name": "fs-image-preheat-************", "uid": "1db867b4-5ced-425f-8b56-952f2769d7c3", "controller": true, "blockOwnerDeletion": true}], "finalizers": ["batch.kubernetes.io/job-tracking"], "managedFields": [{"manager": "kube-controller-manager", "operation": "Update", "apiVersion": "v1", "time": "2025-05-23T14:28:07Z"}, {"manager": "kubelet", "operation": "Update", "apiVersion": "v1", "time": "2025-05-23T14:28:11Z"}]}, "spec": {"volumes": [{"name": "kube-api-access-r2qbr", "projected": {"sources": [{"serviceAccountToken": {"expirationSeconds": 3607, "path": "token"}}, {"configMap": {"name": "kube-root-ca.crt", "items": [{"key": "ca.crt", "path": "ca.crt"}]}}, {"downwardAPI": {"items": [{"path": "namespace", "fieldRef": {"apiVersion": "v1", "fieldPath": "metadata.namespace"}}]}}], "defaultMode": 420}}], "containers": [{"name": "fs-image-preheat", "image": "reg.foneshare.cn/base/fs-image-preheat:v1.1", "command": [], "env": [{"name": "TZ", "value": "Asia/Shanghai"}, {"name": "NODE_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "IMAGE_PULL_PARALLEL", "value": "1"}, {"name": "IMAGE_PATH_0001"}, {"name": "IMAGE_PATH_0002", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--************"}, {"name": "IMAGE_PATH_0003", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.5-all-develop---9.5.5-20250523"}, {"name": "IMAGE_PATH_0004", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:v9.5.5-all-develop---9.5.5-20250523"}, {"name": "IMAGE_PATH_0005", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-mq-provider:v956-vip-20250524-1"}, {"name": "IMAGE_PATH_0006", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-develop-20250523-955"}, {"name": "IMAGE_PATH_0007", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-si/fs-fxiaoke-connector:v2.9.113-develop-20250523"}, {"name": "IMAGE_PATH_0008", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221744"}, {"name": "IMAGE_PATH_0009", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221737"}, {"name": "IMAGE_PATH_0010", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221653"}, {"name": "IMAGE_PATH_0011", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:feature---agent_sse_v2--202505231917"}, {"name": "IMAGE_PATH_0012", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v956-vip-20250524-1"}, {"name": "IMAGE_PATH_0013", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221734"}, {"name": "IMAGE_PATH_0014", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505231808"}, {"name": "IMAGE_PATH_0015", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505231802"}, {"name": "IMAGE_PATH_0016", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v955-vip-20250523-1"}, {"name": "IMAGE_PATH_0017", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-datax/datax-pusher:master--202505221907"}, {"name": "IMAGE_PATH_0018", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221739"}, {"name": "IMAGE_PATH_0019", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221733"}, {"name": "IMAGE_PATH_0020", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221710"}, {"name": "IMAGE_PATH_0021", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.5.5-all2-develop---9.5.5-20250523"}, {"name": "IMAGE_PATH_0022", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--************"}, {"name": "IMAGE_PATH_0023", "value": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--************"}], "resources": {"limits": {"cpu": "2", "memory": "400Mi"}, "requests": {"cpu": "500m", "memory": "100Mi"}}, "volumeMounts": [{"name": "kube-api-access-r2qbr", "readOnly": true, "mountPath": "/var/run/secrets/kubernetes.io/serviceaccount"}], "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File", "imagePullPolicy": "Always"}], "restartPolicy": "OnFailure", "terminationGracePeriodSeconds": 30, "dnsPolicy": "ClusterFirst", "serviceAccountName": "default", "serviceAccount": "default", "nodeName": "***********", "securityContext": {}, "schedulerName": "default-scheduler", "tolerations": [{"key": "node.kubernetes.io/not-ready", "operator": "Exists", "effect": "NoExecute", "tolerationSeconds": 180}, {"key": "node.kubernetes.io/unreachable", "operator": "Exists", "effect": "NoExecute", "tolerationSeconds": 180}], "priority": 0, "enableServiceLinks": true}, "status": {"phase": "Running", "conditions": [{"type": "PodReadyToStartContainers", "status": "True", "lastProbeTime": null, "lastTransitionTime": "2025-05-23T14:26:46Z"}, {"type": "Initialized", "status": "True", "lastProbeTime": null, "lastTransitionTime": "2025-05-23T14:26:42Z"}, {"type": "Ready", "status": "True", "lastProbeTime": null, "lastTransitionTime": "2025-05-23T14:26:46Z"}, {"type": "ContainersReady", "status": "True", "lastProbeTime": null, "lastTransitionTime": "2025-05-23T14:26:46Z"}, {"type": "PodScheduled", "status": "True", "lastProbeTime": null, "lastTransitionTime": "2025-05-23T14:28:07Z"}], "hostIP": "***********", "podIP": "************", "startTime": "2025-05-23T14:26:42Z", "containerStatuses": [{"name": "fs-image-preheat", "state": {"running": {"startedAt": "2025-05-23T14:26:46Z"}}, "lastState": {}, "ready": true, "restartCount": 0, "image": "reg.foneshare.cn/base/fs-image-preheat:v1.1", "imageID": "reg.foneshare.cn/base/fs-image-preheat@sha256:de79cb1bca64ba6dba2e2180f8296446bdc80bdf8c665404cfdda3cc2b7989fb", "containerID": "containerd://baee539183ee80f2390244887bfe9e7c7be2b632350bd6ae8973014f33690f9a"}], "qosClass": "Burs<PERSON>"}}
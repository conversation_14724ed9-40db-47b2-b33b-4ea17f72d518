{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-3", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-3-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:jacoco--202504072001", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.0--202504092049", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.5.0--202504101924", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:develop---9.5.0--202504101542", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202504100055", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202504100054", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--202504101603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-admin-provider:v9.5.0-stage-20250409-02", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-upload-consumer:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202504100051", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.5.0-all-mysql8-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:develop---9.5.0--202504072300", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-metaserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:main--202504071115", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/checkins/checkins-biz:release--202504092252", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:master--202504101601", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---async_trigger_approval_view_detail--202504092315", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2es:v3.7.1-master-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--202504101746", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.5.0--202504101842", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.5.0-stage-20250409-02", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/ibss/ibss-site-dc:master--202504082304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/store-es:master--202504102124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202504101602", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/store-es:v3.7.1-master-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-web:develop---9.5.0_calendar--202504102040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-online-manage-provider:master--202504101603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:feature950---rag_jacoco--202504081520", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/i18n-setting:develop---9.5.0--202504101930", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202504101430", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-pay-all/fs-crm-pay/fs-crm-pay-web:develop---9.5.0_support_callbackurl--202504101914", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202504101657", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-organization-sqlserver-proxy/fs-organization-sqlserver-proxy-provider:master--202504092331", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:stage--202504092249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-outer-web:tag-mengniu-20241120", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-erp-sync-data/erp-sync-data-all:v950.12-master-20250402", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.12-develop-20250406", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:release---9.5.0--202504092347", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-operator/paas-db-provider:feature---fix-clickhouse-timeout--202504101420", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-manage:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.5.0-stage-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:develop---9.5.0_ai_knowledge--202504082242", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504081729", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:develop---9.4.5-field_dependent_check--202504102124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--202504072031", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.156-feature---950_wait_notify-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-provider:tag-mengniu-20241120", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:release---9.5.0--202504101928", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.5.0-stage-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:develop---9.5.0_ai_knowledge--202504092234", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/client-log2es:master--202504102124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.5.0-stage-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-linkapp-provider:v9.5.0-stage-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-admin-provider:v9.5.0-stage-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:master--202504072312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-all/fs-crm-all-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-cloud:hotfix---async_trigger_approval_view_detail--202504092315", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:release---9.5.0--202504072342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:v9.5.0-gray-v3-release---9.5.0-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.157-feature---950_wait_notify-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.5.0--202504072300", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.5.2-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-task:release-950--202504092330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.8.3-main-20250409", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-cloud:hotfix---app_loop_fixed--202504081141", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---9.5.0--202504102007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.154-feature---950_wait_notify-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:feature---master---brush-paas-app--202504081056", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:1-release-950-20250409", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-paas2bi-transfer:v2.2.1120-release-20240407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-admin-provider:v9.4.5-master-20250410-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.12-develop-202504081811", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:develop---9.5.0--202504101424", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.5.0-stage-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:stage--202504092249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-81", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-admin-provider:stage--202504092249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-82", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-cloud:hotfix---async_trigger_approval_view_detail--202504091208", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-83", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.4.5-master-20250410-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-84", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-calculate-task/fs-paas-calculate-task-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-85", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:feature---paas2ChButDispatcher-7.0--202504101211", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-86", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:jacoco--202504100000", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-87", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-datax/datax-pusher:feature---master-datasource-mengniu--202504091926", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-88", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504081450", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-89", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--20**********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-90", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---sandbox_copy_add_dispatcher--202504101711", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-91", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-92", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-metadata-dataloader:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-93", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-linkapp-provider:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-94", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0_ai_knowledge--202504072239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-95", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-provider:v940.9-20250224", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-96", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-task:tag-mengniu-20241120", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-97", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.5.0-stage-20250409-02", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-98", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v3-jyq--202504101609", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-99", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:master--202504101600", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-100", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:bugfix---9.4.5-field_relation_validator--202504091959", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-101", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/feeds/fs-feeds/fs-feeds-next-provider-web:release-950--202504091921", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-102", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:develop---9.4.5--202504091812", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-103", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-cloud:hotfix---repeated_consumer--202504081736", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-104", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:develop---9.4.5-field_dependent_check--202504102106", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-105", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.12-main-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-106", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-admin-provider:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-4", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-4-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:v2024.11.60-release---gray-20250408", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504081945", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-task:develop---9.5.0_calendar--202504102040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-paas2bi-transfer:release--202504092305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:hotfix---async_trigger_approval_view_detail--202504091209", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504082028", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0--202504102006", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--202504101428", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-recycle:feature---dev-9.5.0--202504101419", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:bugfix---9.4.5-field_relation_validator--202504092004", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0_ai_knowledge--202504082237", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:master--202504101601", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-task-sfa-ai/fs-crm-task-sfa-ai-web:develop---9.5.0--202504092255", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.0_ai_knowledge--202504082306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--202504101657", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.0--202504101923", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-app-task/fs-paas-app-task-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.4.5-master-20250410-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.4.0--202504091813", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:develop---9.5.0--202504072248", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0_ai_knowledge--202504052256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-provider:develop---9.5.0_calendar--202504102040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.5.0-all-mysql8-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504101428", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:develop---9.5.0--202504091827", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-netdisk/fs-netdisk-biz:v940.3-20250118", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--202504101740", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v930-vip-20250409-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.5.0--202504082302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504101358", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0--202504102007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider:master--202504101657", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0410---ai_knowledge_sse--202504101117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202504100059", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:yan---bugfix---requisition_lock_0403--202504092248", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.5.0--202504092053", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.0--202504102007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--202504072035", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202504101603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:journal-export-optimize-wz-v2--202504092224", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:feature---9.4.5---SupportUpdateIsIndexField--202504091645", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider:master--202504101603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-online-manage-provider:master--202504101657", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504081714", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202504092337", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:v9.4.10-release---9.5.0-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2clickhouse:v3.7.1-master-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0410---ai_knowledge_sse--202504101104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-webhook/fs-webhook-provider:feature950---customInfo--202504092237", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---9.5.0_ai_knowledge--202504082237", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202504101657", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:bugfix---9.4.5-field_relation_validator--202504092123", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:bugfix---9.4.5-field_relation_validator--202504092106", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-cloud:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-webhook/fs-webhook-provider:feature950---customInfo--202504092355", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-tools/tenant-sandbox:feature---dev-9.5.0--202504101417", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504101134", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-wechat-customer-web:daiyun---0410---ai_knowledge_sse--202504101104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-processor:hotfix---async_trigger_approval_view_detail--202504092315", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504081502", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/ibss/ibss-site-dc:master--202504102124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:v9.5.1-release---9.5.0-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.5.2-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202504100003", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-org/fs-paas-org-provider:feature---group_by_user--202504072218", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.5.0--202504101928", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.4.5-master-20250410-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:v9.4.5-master-20250410-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-biz:release---9.5.0--202504100001", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-processor:hotfix---async_trigger_approval_view_detail--202504091208", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0_ai_knowledge--202504052316", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:master--202504101515", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.0_ai_knowledge--202504082237", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0--202504081328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:dev-9.5.0-fej--202504101517", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-recycling-task/fs-crm-recycling-task-web:v9.5.0-all-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-org/fs-paas-org-provider:feature---group_by_user--202504101021", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-81", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/checkins/checkins-biz:release--202504100010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-82", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-paas2bi-transfer:v2.2.1120-release-20250408", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-83", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-erp-sync-data/erp-sync-data-whole-war:v950.12-master-20250402", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-84", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:release---gray--202504101732", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-85", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-audioserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-86", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:develop---9.5.0--202504072317", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-87", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-wechat-customer-callback:daiyun---0410---ai_knowledge_sse--202504101104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-88", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/checkins/checkins-biz:master--202504092303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-89", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.4.5-master-20250409-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-90", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-task:dev-9.3.0--202504091813", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-91", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.12-develop-202504081551", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-92", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-transfer:v2025.01.64-release-transfer-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-93", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202504101603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-94", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-metric:master--202504082309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-95", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---ai-jyq--202504081834", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-96", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.65-hotfix---gray-20250407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-97", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-fileserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-98", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202504100031", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-99", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-cgi:v940.9-20250224", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-100", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.0--202504052242", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-101", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:master--202504101601", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-0", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-0-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.5-Activity-feature---acvivity---active-record-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.4.5-gray-02-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-server:master--202503071150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250306-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-20250305-2", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.48-hotfix---gray-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:master--202503052253", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:yan---bugfix---check_spare_parts_consumption_detail_quantity--202503071200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:bugfix---dependency--202503062027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:develop---9.4.5--202503061129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:develop---9.4.5--202503061036", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/client-log2es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:master--202503052245", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503071146", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/er-manager-all/er-manager-deploy:main--202503061917", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-cloud:master--202503052246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-945--202503052345", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-biz:v9.4.10-master-20250307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-server:cloud--202503071411", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:bugfix---dependency--202503061918", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:feature---dev-9.4.0-event-store--202503061128", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.4.5--202503061036", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:develop---9.4.5--202503061037", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--202503060103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-20250305-3", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:master--202503052307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:hotfix---approval_opinion_update--202503052250", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:hotfix---approval_opinion_update--202503060158", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-processor:hotfix---i18n_deal--202503052326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--202503052323", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:master--202503052307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-app-task/fs-paas-app-task-web:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2clickhouse:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:v2024.11.48-hotfix---gray-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--202503071027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503061826", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:feature---950_multi_shopping_mall_950--202503061559", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-web:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-processor:master--202503052246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:develop---9.4.0--202503070024", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--202503052328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:master--202503052300", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.12-release-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/ibss/ibss-site-dc:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:hotfix950---rag--202503060049", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--202503060038", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:master--202503052246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503061603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.4.5-all-01-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--202503060126", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.4.0-gray-20250306-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:master--202503052246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-public-abutment:master--202503052308", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.4.5--202503071515", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:master--202503071027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.4.5-all-01-mysql8.0-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--202503060040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:feature---activity-list-v2-zwc--202503060010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.4.5-all-01-mysql8.0-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/store-es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-pay-all/fs-pay/fs-pay-business-provider:daiyun---0303---support_union_expire_time--202503052301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.61-master-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-task:master--202503071413", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-manage:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.4.5-all-01-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0228---support_qx_rich_card_waiting--202503052255", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-20250305-4", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:master--202503052246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--202503052239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:master--202503071657", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-bot-crm-helper:master--202503071025", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--202503061312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202503061312", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-1", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-1-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:feature---activity-list-v2-zwc--********0126", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--********1656", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:master--202503052246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-task:master--202503071427", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-ui:feature---config-service-v2.0-ui--********2003", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-cloud:hotfix---i18n_deal--202503052346", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-20250305-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--********0055", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.4.5-gray-10-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-web:v9.4.5-all-01-mysql8.0-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:hotfix950---rag--********0101", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:v9.4.0Activity-develop---9.4.0-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:jacoco--202503071420", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:feature---fix_sql_join--202503071311", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202503071027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/er-manager-all/er-manager-deploy:main-0307--202503070145", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:master--********1821", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/gateway-log2es:master--********0838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:journal-export-optimize-wz-v2--********0014", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:master--202503052307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250307-2", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-task:cloud--202503071427", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-copier:hotfix---get_metadata_sql_specify_table--202503071302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/er-manager-all/er-manager-deploy:main-0307--202503070154", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:master--********1412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250307-3", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250307-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:bugfix---dependency--********2042", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:feature---calculate--202503052329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:v9.4.6-delete_local_quartz_i18n-v2-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/fs-appserver-holiday-v2-server:cloud--202503071413", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.4.0-gray-********-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250307-4", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--********0010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.4.5-all-01-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:feature---calculate--********0006", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-recording-provider:feature---946-searchHistory--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--********1550", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:hotfix---gatatner--********1547", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:bugfix---dependency--********1104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-web:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-transfer:v2025.01.47-release-transfer-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--202503071511", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:master--********1823", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:feature---activity-list-v2-zwc--********0010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:v9.4.6-delete_local_quartz_i18n-v2-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-server:master--202503071410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-945--********0009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/customized_dev/fs-fmcg-customized-server/fs-fmcg-customized-biz:master--202503052330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.62-hotfix---gray-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-manage:v9.4.5-all-01-mysql8.0-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.50-hotfix-temp-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.4.5-gray-08-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202503071513", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-task:master--202503071411", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.2-feature---acvivity---active-record-20250307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.1-feature---acvivity---active-record-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--********2301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:master--202503052305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.5-03-develop---9.4.5-20250305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202503071027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:950-bot-agent-v2-1--********1609", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:950-bot-agent-v2-1--********1557", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:feature---dev-9.4.5-event-store--********1522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-cgi:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:feature---activity-list-v2-zwc--********0126", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:feature---activity-list-v2-zwc--********0010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--********1716", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:hotfix---gatatner--********1622", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
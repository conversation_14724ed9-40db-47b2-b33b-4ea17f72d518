{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-1", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-1-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-server/fs-sail-wechat:v7.6.0-role-plat-20210708-version--202412132056", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.149-feature---940-20250110", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-940--202501192327", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-biz:v9.4.9-hotfix---fix_find_record_field_mapping-20250226", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-processor:v9.4.5-hotfix---fix_i18n-20250122", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone/fs-stone-dataserver:v910-unify-multi-cluster", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-fileserver:v920.1-unify-multi-cluster-fix", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone/fs-stone-metaserver:v910-unify-multi-cluster", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-upload-consumer:v905-unify-multi-cluster-20240524", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-********-4", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-todo-provider:release---945--202502262257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-biz:master--202501222034", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-processor:v9.4.0-online-v1-release---9.4.0-20250117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-xt-proxy/fs-xt-proxy-provider:v1.2.6-master-20220628", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/i18n-setting:v9.4.5-gray-04-develop---9.4.5-20250220", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs:v1.0.5-k8s-20240614", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-operator/paas-db-provider:release---9.4.0--202412312315", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/pg-scanner:v6.3.2-master-20250119", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-feishu-gateway/fs-qywx-web:tag-master-20250110", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/weex-bundle-console/bundle-console-web:feature---master-cloud-merge--202412051838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/weex-bundle-console/bundle-console-service:feature---master-cloud-merge--202412112258", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.4.5-gray-01-develop---9.4.5-20250228", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202502141924", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-crm-report/fs-bi-crm-report-web:master--202503042311", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online:release-agg-dispatcher--202501142106", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.50-hotfix-temp-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.60-master-20250228", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:v2024.11.29-hotfix---gray-20250220", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:release--********1952", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:develop---9.4.5--202502192251", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.4.5--202502142238", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.5-02-develop---9.4.5-20250221", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.4.5--202502282246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.4.5-develop---9.4.5-20250222", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.5-03-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.4.5-gray-01-develop---9.4.5-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---fix_button_param_value_display--202502212307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-document-converter/fs-document-convert-web-big:3.41.1-unify-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.4.0_activity--202502282251", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-945--********2345", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-945--202502200104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-file-server:v3-jdk-fix-20250119", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:master--202501161146", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:dev-9.4.5--202502282323", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.4.0_activity--20**********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:feature---dev-9.4.0-event-store--202501161137", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.4.5-gray01-develop---9.4.5-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:release-945--202502282343", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:release-945--202502282342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:release-945--202502282343", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.4.5-gray-10-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.4.5-gray-06-develop---9.4.5-20250219", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.4.5-gray-02-develop---9.4.5-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-provider:2-release-945-20250222", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:v9.4.5-gray-01-develop---9.4.5-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:feature---approval_no_match_branch_topic--202502262349", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:1-release-945-20250221", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:release-945--202502200104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-945--202502150219", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-biz:2-release-945-20250221", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-provider:1-release-945-20250213", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:develop---9.4.5--202502211717", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-945--202503060009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone-watch-tower/fs-stone-transfer:main", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.4.5-gray-01-develop---9.4.5-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.4.5-gray-08-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/surey/fs-open-surey-web:0.2.1-version-0816-202408162350", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-manage:v9.4.5-all-01-mysql8.0-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/ibss/ibss-site-dc:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/gateway-log2es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503061603", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2clickhouse:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:feature---dev-9.4.5-event-store--202503061522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-web:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:feature---dev-9.4.0-event-store--202503061128", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.48-hotfix---gray-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:master--********2300", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:feature---activity-list-v2-zwc--202503060126", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:master--********2253", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503061716", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-81", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-82", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-web:v9.4.5-all-01-mysql8.0-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-83", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-84", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--202503060126", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-85", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-pay-all/fs-pay/fs-pay-business-provider:daiyun---0303---support_union_expire_time--********2301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-86", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.61-master-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-87", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master-gray--********2010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-88", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/customized_dev/fs-fmcg-customized-server/fs-fmcg-customized-biz:master--********2007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-89", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--********1949", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-90", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/client-log2es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-91", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:journal-export-optimize-wz-v2--202503060014", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-92", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--202503060040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-93", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-public-abutment:master--********2308", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-94", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-95", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--202503060103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-96", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-97", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202503061312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-98", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.4.5-all-01-mysql8.0-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-99", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:v9.4.0Activity-develop---9.4.0-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-100", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.4.0-gray-********-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-101", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:bugfix---dependency--202503061104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-102", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-web:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-103", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:master--********2245", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-104", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--********2007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-105", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503061656", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-106", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:master--********2307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-107", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:master--********2246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-108", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-109", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:hotfix---disable_workflow_definition_delete_local_quartz_i18n--********2131", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-110", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--********2010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-111", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-manager:feat---http-sd--202503061550", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-112", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.4.5-all-01-mysql8.0-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-113", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:master--********2307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-114", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.4.0-gray-********-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-115", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-recording-provider:feature---946-searchHistory--********2052", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-116", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:950-bot-agent-v2-1--202503061609", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-117", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:hotfix---approval_opinion_update--202503060158", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-118", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:feature---calculate--********2329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-119", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.4.5-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-120", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--********2323", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-121", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-manage:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-122", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-app-task/fs-paas-app-task-web:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-123", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/store-es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-124", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:master--********2246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-125", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:v9.4.5-delete_local_quartz_i18n-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-126", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-127", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-cgi:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-128", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:feature---activity-list-v2-zwc--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-129", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:hotfix---i18n_deal--********1849", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-130", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2es:master--202503060838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-131", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:hotfix950---rag--202503060049", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-132", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---activity-list-v2-zwc--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-133", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:feature---activity-list-v2-zwc--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-134", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:hotfix---gatatner--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-135", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:hotfix---gatatner--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-136", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-137", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-oncall:v2-main-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-138", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-transfer:v2025.01.47-release-transfer-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-139", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:hotfix---approval_opinion_update--********2250", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-140", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-cloud:master--********2246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-141", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-142", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.4.5-all-01-develop---9.4.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-143", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:feature---950_multi_shopping_mall_950--202503061559", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-144", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:950-bot-agent-v2-1--202503061557", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-145", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.4.5--202503061036", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-146", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:master--********2246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-147", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:master--********2037", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-148", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:feature---calculate--202503060006", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-149", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:v9.4.3-hotfix---i18n_deal-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-150", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:develop---9.4.5--202503061037", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-151", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:hotfix---disable_workflow_definition_delete_local_quartz_i18n--********2131", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-152", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-admin-provider:v9.4.0-gray-********-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-153", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:v9.4.5-delete_local_quartz_i18n-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-154", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:feature---activity-list-v2-zwc--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-155", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:master--********2246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-156", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-********-3", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-157", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-cloud:hotfix---i18n_deal--********2346", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-158", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:master--********2307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-159", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-********-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-160", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-processor:master--********2246", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-161", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:develop---9.4.5--202503061129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-162", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:feature---activity-list-v2-zwc--202503060126", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-163", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow-session/fs-flow-session-biz:master--********2158", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-164", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--202503061312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-165", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-166", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:develop---9.4.5--202503061036", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-167", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--********2239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-168", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-169", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-170", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v940-gray-********-2", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
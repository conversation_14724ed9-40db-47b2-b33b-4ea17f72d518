{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-0", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-0-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.5.0-all-mysql8-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-web:v9.5.0-all-mysql8-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-manage:v9.5.0-all-mysql8-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.5.0-all-mysql8-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.5.0-gray-01-develop---9.5.0-20250325", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.2.0-all-01-develop---9.2.0-20240912", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-ai-detector/fs-ai-detector-provider:ai_model--202504130140", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-task:cloud--202501161127", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-office-v2/fs-appserver-holiday-v2-server:cloud--202412061753", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202504101740", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--202504101740", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--202501161513", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-schedule/fs-appserver-schedule-provider:release---945--202503281526", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:master--202503090005", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202504122307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:master--202501161146", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:master--202504130051", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg/fs-fmcg-service:dev-9.5.0--202504152301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.5.0--202504111123", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-task:dev-9.3.0--202412132301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-linkapp-provider:v9.5.0-master-20250426-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.5.0-master-20250426-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.5.0-master-20250426-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.5.0-master-20250426-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:v9.5.0-master-20250426-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.5.0-master-20250426-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter-hj:master-cloud--202504130043", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master-cloud--202504130043", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master-cloud--202504252306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:master-cloud--202504130043", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-dim-online:cloud-dispatcher--202412132305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-offline:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online-calculate:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-paas2bi-transfer:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-feed2pg-online:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-crm-report/fs-bi-crm-report-web:v2025.01.27-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:v2024.11.71-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-export:v2024.11.40-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-goal/fs-bi-goal-web:v2025.1.16-master-20250418", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-org/fs-bi-org-entrance:v2.0.169-master-20241220", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-permission/fs-bi-permission-access:v2024.12.12-master-20241220", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-stat-transfer:v1.0.59-940-master-20250117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:v2024.11.43-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-devops:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata-ant:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
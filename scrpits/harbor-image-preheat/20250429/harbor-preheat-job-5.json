{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-5", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-5-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing-statistic/fs-marketing-statistic-provider:master--202503211915", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--202504251838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master--202504251838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master--202504251839", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master--202504251839", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master--202504251838", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-sms-platform/fs-sms-platform-provider:master--202502281720", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-sms-platform/fs-sms-platform-web:master--202502281720", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open-msg/fs-open-msg/fs-open-msg-web:tag_main_2025_0321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-erp-sync-data/fs-erp-oa:master_1113_oa", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-erp-sync-data/fs-erp-oa:v940_fix_language--202503082339", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-provider:master--202503211914", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/oauth/fs-oauth-base-web:v910-20240713-master-2", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-document-converter/fs-document-convert-web-big:3.6.2-unify-20250424", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-document-preview-service/fs-document-preview-cgi:v940-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-login-cgi:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-user-login-biz:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-user-login-provider:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-xt-proxy/fs-xt-proxy-provider:v1.2.6-master-20220628", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/paas/fs-message/fs-crm-notify-provider:master--202504142221", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/paas/fs-message/fs-todo-provider:release---950--202503211131", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/paas/fs-paas-function-engine/fs-paas-function-service-background-provider:master-202408302257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-cgi:v950-20250304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-provider:v950-20250304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-netdisk/fs-netdisk-biz:v940.3-20250118", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-uc/fs-uc-provider:master--202501082303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-warehouse-batch/warehouse-batch-biz:v940.2-master-20250119", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-warehouse/fs-warehouse-provider:920.2-master-foneshare-20241105", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk23", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk23-v241128", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-beefly1.0.7:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-ffmpeg3.3.4:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-function:dragonwell8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-wkhtmltopdf:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:ali-dragonwell8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9-doc-converter:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9-python3:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-3", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-3-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.0--202504101417", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-paas-reference-service:master--202503122303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-paas-reference-service:v8.4.125-dev-9.0.0-20240426", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-action-centre/fs-paas-action-web:feature---action_list--202504122331", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.5.0--202504161924", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:master--202503052300", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-api-bus/fs-apibus-global:v8.7.37-support-499-errorlog-master-20241022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-api-bus/fs-apibus-ncrm:v8.7.37-support-499-errorlog-master-20241022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-api-bus/fs-apibus-paas:v8.7.37-support-499-errorlog-master-20241022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-app-task/fs-paas-app-task-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.5.0-all-02-master-20250418", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:release-950--202504092330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-provider:release-950--202504092330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-task:1-release-950-20250328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-bizconf/fs-paas-bizconf-web:master-202410181912", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-calculate-task/fs-paas-calculate-task-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-auth/service:master--202504130100", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-auth/worker:master--202504130101", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-tools/hamster-server:hotfix---reload_i18n_client_data--202412182347", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-tools/tenant-sandbox:feature---dev-9.5.0--202503212319", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:release---9.5.0--202504092301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-gnomon/fs-paas-gnomon-executor:feature---supplement_log--202502212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-job-schedule/fs-paas-job-schedule-web:v6.7.65-feature---950_waiting_notify-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-leica/paas-leica-sync:feature---dev-9.3.0-202411232335", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-provider:feature---dev-950--202503300023", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-surrogate:feature---dev-950--202503212313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-metadata-dataloader:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-org/fs-paas-org-provider:feature---group_by_user--202504101021", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-recycle:feature---dev-9.5.0--202503212326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:feature---skip_invalid_tenant--202503082330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-resource/resource-starter:main--202503212234", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-rule/fs-paas-rule-service-provider:feature---950_rule_right_value_matching_fromMaster--202504101930", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-service/paas-describe-web:feature---dev-915-202410121843", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:v9.5.4-release---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:v9.5.9-hotfix---auto_task_rerun_function-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-console:v9.3.6-release---9.4.0-20250117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/fs-pod-console:feature---new_database_refresh_console--202502102313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/fs-pod-service:feature---ch-db-optimize--202504130102", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/pod-console:master--202504130103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:dev-950--202503260034", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/i18n-setting:develop---9.5.0--202504101930", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-console:feature---optimize_data_validate--202504161501", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-operator/paas-db-provider:feature---fix-clickhouse-timeout--202503272313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v5.7.45-split-query-master-20240128", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
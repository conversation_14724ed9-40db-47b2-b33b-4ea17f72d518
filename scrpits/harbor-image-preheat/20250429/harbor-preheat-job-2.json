{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-2", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-2-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-industry-interface/fs-bi-industry-interface-provider:v2.2.1120-release-20240307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.81-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-dataplatform-paas2gp-transfer/fs-bi-billboard-server:dev-v1.0.0-billboard-20241030", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-dataplatform-paas2gp-transfer/fs-bi-paas2gp-transfer:v1.0.2-paas2hive-dev-20250101", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-k8s-tomcat-test/fs-k8s-tomcat-test-biz:main-202406201645", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:master--202504251900", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:master--202504251900", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-950--********2301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-950--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-lifecycle:release-950--********2303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:release-950--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:release-950--********2303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-950--********2301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-new-schedule/fs-new-schedule-web:feature---950_activity--202503242023", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:release-950--202503282304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:master--202504232310", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/gateway-log2es:master--202504102312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2clickhouse:master--202504102312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2es:master--202504102312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-provider:develop---9.5.0_calendar--202504102040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-task:develop---9.5.0_calendar--202504102040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-web:develop---9.5.0_calendar--202504102040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-si/erpdss-outer-connector/connector-runtimev2-fs:v950-main-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-erp-sync-data/erp-sync-data-whole-war:v950.13-master-20250409", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-fcp:develop---9.4.0--202412312337", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0410---ai_knowledge_sse--202504111020", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-materail/fs-open-material-web:special-for-sales-forecast-skip-sandbox-copy_--202502282342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--202504251952", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-app-view/fs-app-view-biz:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-app-view/fs-app-view-cgi:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-document-converter/fs-document-convert-web:3.5.4-unify-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-experience-account-provider:release-9.5.0--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:release-9.5.0--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-org-adapter/fs-plat-org-adapter-provider:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-biz:release-950--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-provider:release-950--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-polling/fs-polling-cgi:master--202501162329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-polling/fs-polling-provider:master--202501162329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-task-manage/fs-task-manage-provider:v1.0.4-master-20240320", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/javacommon/logconf/logconfig-web:master-202407142001", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:master--202504222051", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:v9.0.0-gray-02-develop---9.0.0-20240410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-server-wrapper:release---950--********1132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata-option/fs-metadata-option-provider:v1.0.9-feature-batch-create-v2-20231117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.4.5--202503082340", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
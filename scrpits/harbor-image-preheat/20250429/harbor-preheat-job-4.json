{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-4", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-4-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-copier:hotfix---get_metadata_sql_specify_table--202503071302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/pg-scanner:v5.7.45-split-query-master-20240128", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-cep/fs-cep-provider:release-9.5.0--202503212328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-customer-component/fs-customer-component-provider:master--202504122330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-ext-contact/fs-ext-contact-provider:master-202410262342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-id/fs-id-server:v1.0.7-update-fastjson-master-20220627", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-recording-provider:v1.0.242-feature---946-searchHistory-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-wishful-ocr-web:feature---920-record-202408301623", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-bot-crm-helper:master--202503281848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:master--202503071027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-provider:master-202410232010", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-extension-biz:master-202410232009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-extension-provider:master-202410232009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-objgroup-manage-provider:master--202412312324", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-plugin-provider:master-202408301557", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-search-message-provider:master-202408301557", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-search-provider:master-202409121911", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-task/fs-qixin-task-provider:master-202410232007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-online-manage-provider:master-202407262109", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202504111113", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202504111113", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--202504111113", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.157-feature---950_wait_notify-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.5.0-all-01-develop---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/javaserviceconsole/fs-console-web:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/javaserviceconsole/fs-console-web:master--202504032239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.5.2-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/wechat-union/fs-wechat-union/fs-wechat-proxy-callback:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/wechat-union/fs-wechat-union/fs-wechat-sender-task:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/wechat-union/fs-wechat-union/fs-wechat-union-all:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:master--202504111426", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-server:cloud--202503071411", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/er-manager-all/er-manager-deploy:v930-main-20250411-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v955-master-20250425-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-admin-system/sync-data-admin-web:v910-master-20240915-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-mq-provider:v955-master-20250425-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-task:v910-master-20240915-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-file-server:v3-jdk-fix-20250119", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone-auth:v2.1-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-audioserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-cgi:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-cloud:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-fileserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-metaserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-proxy:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-upload-consumer:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
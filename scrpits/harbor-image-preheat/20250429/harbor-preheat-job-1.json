{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-1", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-1-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-mq:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-sqlengine:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-sqlgenerator:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.89-master-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-task:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-transfer:v2025.01.75-release-transfer-20250413", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/async-job-export:v9.4.8-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/async-job-import:v9.4.4-master-20250117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:feature---dev-9.5.0--202504130059", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.7.13-main-20250314", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.8.5-main-20250416", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202503192159", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-egress-proxy:v3.4.2-master-20250114", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-eye-monitor/eye-backend:master-202410121027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-hubble/hubble-index:feature---support_outtenant_pool_fromdev930--202501161129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-hubble/hubble-query:feature---support_outtenant_pool_fromdev930--202501161129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-hubble/hubble-wal-log:feature---support_outtenant_pool_fromdev930--202501161129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-cgi:v2.1.101-refactor---reduce-clear-describe-cache-20240420", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-console:master--202502090012", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-message:hotfix---940_hotfix--202501102303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---sandbox_copy_add_dispatcher--202504101711", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-mq-proxy/cloud-mq-consumer:feature---master-gray-egress-nginx-202411261641", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-mq-proxy/cloud-mq-proxy:v1.0.8-master-20240331", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm-after-action/fs-bpm-after-action-biz:hotfix---502_and_502_timeout_error--202504182249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm-after-action/fs-bpm-after-action-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-cloud:v9.4.13-hotfix---fix_search_log-20250418", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow-inspection/fs-flow-inspection-biz:v1.0.20-hotfix---update_sendMessageContent-20240706", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-biz:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-cloud:v9.5.2-release---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-cloud:v9.4.19-release---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-cloud:v9.5.0-online-v1-release---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-recycling-task/fs-crm-recycling-task-web:develop---9.5.0--202504212330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:develop---9.5.0--202504131820", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.5.0--202504251916", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202504182201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.5.0-all-02-develop---9.5.0-20250413", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.0--develop---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-cgi:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.5.0-gray-02-develop---9.5.0-20250409", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-cloud:master--202504220142", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
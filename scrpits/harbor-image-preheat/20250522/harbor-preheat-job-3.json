{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-3", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-3-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:daiyun---feat_create_rag--202505191813", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-crm-notify-provider:master--202505221403", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:feature---rag_dispatcher_task--202505210015", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.5--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master--202505212001", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:master--202505201706", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-v2/checkins-v2-task:master--202505221543", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-provider:master--202505221013", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:release-955--202505221154", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-business-broker:master--202505221038", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/fs-feishu-web:kny---lark_send--202505212314", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:release---9.6.0--202505211825", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-manage:v9.5.5-all01-mysql8.0-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-datax/datax-pusher:master--202505221907", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505212316", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.5--202505221108", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-external:master--202505221403", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:feature---9.5.5-test--202505221144", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-sign-provider:v1.0.247-bugfix---960---fdd-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--202505191615", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-955--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-processor:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-955--202505221058", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505191434", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-main-20250523-955", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:release-955--202505201641", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-wechat-customer-web:daiyun---0508---optimize_transfer_artificial--202505221133", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.86-master-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202505211943", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-app-task/fs-paas-app-task-web:v9.5.5-all-01-master-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:v9.5.14-release---9.6.0_oneFlow-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master-gray--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:master--202505221046", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:release-955--202505212007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221822", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:v9.5.2-release---9.6.0-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:develop---9.5.5--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-ai/fs-fmcg-ai-server:main--202505212256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221744", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---tcc_has_no_defintion--202505221551", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/checkins/checkins-biz:master--202505221043", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-online-manage-provider:master--202505221039", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-idp-helper:feat---users--202505191706", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-develop-20250517", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221612", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505221522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-955--202505221057", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:hotfix---trigger_bpm_only_fetch_part_data--202505220018", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:feat---mengniu-url--202505212256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-task/fs-qixin-task-provider:master--202505212238", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-web:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:master--202505221156", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:feature---config-gray-product-v2.0--202505192249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-provider:master--202505221442", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-task:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-955--202505221133", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:feature---feedPG-zwc--202505220058", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:v9.4.18-hotfix---trigger_bpm_only_fetch_part_data-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:develop---9.5.5--202505221551", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-org/fs-paas-org-provider:feature---ignore_db_exception--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---feedPG-zwc--202505220058", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-task:master--202505221048", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.5--202505221141", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-push-broker:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505212302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221734", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-all/fs-crm-all-web:v9.5.5-all-01-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-provider:feature---dev-955--202505221148", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-ext-compose:master--202505221350", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-fsc/fs-fsc-cgi:v950-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-web:master--202505221048", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.6.0--202505201851", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.5.5--202505221135", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-processor:master--202505221014", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v2.2.1132-feature---paas2ChButDispatcher-7.0-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master--202505221501", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-7", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-7-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-org/fs-paas-org-provider:feature---ignore_db_exception--********1021", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--********1709", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-sales/fs-crm-fmcg-sales-web:dev-9.5.5-inspection-2.0--********1123", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:feature---feedPG-zwc--********0124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow-session/fs-flow-session-biz:master--202505212307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---955_ai_knowledge_v3--202505172304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-meeting/fs-crm-meeting-web:main--********1150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:iteration---m5--********1148", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---9.5.5--********1108", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-console:feature---app_load_nginx_optimize--202505212325", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--********1132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:master--********1041", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:tag-master-20250521-test", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.5-all-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-955--********1132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-experience-account-provider:master--********1135", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0508---optimize_transfer_artificial--********1133", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-external:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--********1407", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.5.5--********1150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0508---optimize_transfer_artificial--********1136", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---955_ai_knowledge_v3--202505211953", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:master--202505201626", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-955--********1201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-outer-web:master--********1013", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-transfer:v2025.01.99-release-transfer-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-plugin-provider:master--********1436", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-idp-helper:v1.1.2-main-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-sign-provider:v1.0.245-bugfix---960---fdd-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-webhook/fs-webhook-provider:feature950---customInfo--202505212258", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0508---optimize_transfer_artificial--202505192339", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-955--********1201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-ext-compose:bugfix---jar--********1056", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider:master--********1038", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-uploader:master--********1014", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master-cloud--********1157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master-gray--********1116", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-provider:master--********1048", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--********1653", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-task:master--********1130", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.96-hotfix-chenjf-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:feature---paas2ChButDispatcher-7.0--202505202311", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master-gray--********1116", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-expression/fs-expression-biz:master--********0018", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.102-master-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:v9.5.13-release---9.6.0_oneFlow-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-calculate-task/fs-paas-calculate-task-web:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-hotfix-0515--********1151", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.97-hotfix---gray-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-provider:master--********1130", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-provider:feature---dev-955--202505211759", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-manage:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-recycle:feature---optimize-recycle-rate--********1131", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:master--********1039", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505201044", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0508---optimize_transfer_artificial--********1548", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-webhook/fs-webhook-provider:feature950---customInfo--********1524", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---app_load_nginx_optimize--********1512", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/fs-pod-console:hotfix---bug_repair--202505192239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:master--********1038", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-web:master--********1013", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/fs-qywx-web:chenzx---950---main-20250516--202505211259", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-export:v2024.11.44-master-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-biz:release-955--********1445", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--********1151", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.5.5-all-01-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:v9.5.5-all-01-master-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master-gray--202505212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.6.0--202505202113", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.5--********1146", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-sales/fs-crm-fmcg-sales-web:dev-9.5.5--********1125", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-feishu-gateway/fs-dingtalk-web-self-build-app:chenxb950---fix---deploy-cloud--202505211124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-task:develop---9.5.5---marketing_email--********1145", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:master-cloud--202505211959", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:master--202505192325", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
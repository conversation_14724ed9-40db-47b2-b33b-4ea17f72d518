{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-1", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-1-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:v2024.11.42-feature---AITag-20250423", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-develop-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-big-file-manager/fs-big-file-manager-biz:v950.1-unify-20250416", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:release---9.5.5--202505092338", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm-after-action/fs-bpm-after-action-biz:hotfix---502_and_502_timeout_error--202504182249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-processor:hotfix---fix_search_log--202504260124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-cep/fs-cep-provider:release-9.5.0--202503212328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:release--202505162233", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:develop---9.5.5--202505212007", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-sales/fs-crm-fmcg-sales-web:dev-9.5.5--202505142255", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:develop---9.5.5--202505211946", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.5.0-all-02-master-20250418", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:master--202505161954", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---955_ai_knowledge_v3--202505220027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-crm-notify-provider:master--202505212244", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:master--202505162217", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.5.0-gray-02-develop---9.5.0-20250409", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---i18n--202505072327", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-processor:hotfix---i18n--202505072329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/connector/fs-custom-provider/fs-custom-biz:feature---dev--202505212301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-customer-component/fs-customer-component-provider:master--202504122330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-datapt-pangu-rs/fs-datapt-employee-profiler:fix-error-release-20250425", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/fs-dingtalk-web-self-build-app:chenxb950---fix---todo--202505142106", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-document-converter/fs-document-convert-web:3.5.6-unify-20250416", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.5.0-master-20250516-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.5.0-master-20250516-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-linkapp-provider:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:v9.5.0-master-20250516-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-erp-sync-data/erp-sync-data-all:v960-master-20250514", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-erp-sync-data/erp-sync-data-web:v960-master-20250510", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---955_ai_knowledge_v3--202505212054", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/client-log2es:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/gateway-log2es:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2clickhouse:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2es:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/store-es:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:master--202505071955", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:master--202505071955", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-biz:v9.5.11-release---9.6.0_oneFlow-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-processor:v9.5.11-release---9.6.0_oneFlow-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/customized_dev/fs-fmcg-customized-server/fs-fmcg-customized-excel:master-orion-fpyj-202411152252", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-public-abutment:master--202503052308", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:v9.0.0-gray-02-develop---9.0.0-20240410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-si/fs-k3cloud/fs-k3cloud-provider:v2.8.73-master-20231208", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---955_ai_knowledge_v3--202505100009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--202505212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master-gray--202505212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master-gray--202505212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master-gray--202505212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-server:release---950--202503211132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-server-web:release---950--202503211133", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.0--202504101417", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-new-schedule/fs-new-schedule-web:feature---950_activity--202503242023", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:daiyun---0508---optimize_transfer_artificial--202505200020", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-wechat-customer-callback:daiyun---0410---ai_knowledge_sse--202504101104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-wechat-customer-web:daiyun---0410---ai_knowledge_sse--202504101104", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-materail/fs-open-material-web:master--202502282340", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:release-950--202504092327", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master--202504252312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:master--202504252312", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:develop---9.5.0-field_validate--202504252321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:master--202504271953", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-provider:release-950--202504092330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-calculate-task/fs-paas-calculate-task-web:v9.5.0-all-02-master-20250421", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:v8.4.127-release-java01_merge870-20231101", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-job-schedule/fs-paas-job-schedule-web:v6.7.65-feature---950_waiting_notify-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-provider:feature---dev-950--202504282304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-metadata-dataloader:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:v9.5.12-release---9.6.0_oneFlow-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-recording-provider:feature---946-searchHistory--202503052322", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:addVillageArea_fromMaster--202504232322", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-sign-provider:v1.0.246-bugfix---960---fdd-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-wishful-ocr-web:release---950--202503252054", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:hotfix---trigger_one_workflow_trigger_function_update--202505162301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-processor:hotfix---trigger_one_workflow_trigger_function_update--202505162302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-app-view/fs-app-view-biz:release---9.5.0--202504020009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-app-view/fs-app-view-cgi:release---9.5.0--202504020009", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:master--202504260002", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
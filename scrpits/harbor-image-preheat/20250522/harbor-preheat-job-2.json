{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-2", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-2-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:master--202505071955", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-login-cgi:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-login/fs-login-cgi-compatible:release---9.4.0--202503312255", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-user-login-biz:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-user-login-provider:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-org-adapter/fs-plat-org-adapter-provider:master--202502192313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:master--202504282259", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-biz:release-950--202504092329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-provider:release-950--202504092328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-bot-crm-helper:master--202503211043", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-task/fs-qixin-task-provider:master--202503212305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-server/fs-sail-admin:release--202503082308", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-server/fs-sail-wechat:release--202503082308", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-server/fs-sail-wechat:v7.6.0-role-plat-20210708-version--202503082305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.157-feature---950_wait_notify-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:master--202505071955", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-biz:v9.4.21-hotfix---fix_apply_history_no_content-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-processor:v9.4.11-hotfix---fix_sales_process_id-20250319", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone-watch-tower/fs-stone-transferbackupmaster:v1.0.0-20250517", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone/fs-stone-dataserver:v910-unify-multi-cluster", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-fileserver:v920.1-unify-multi-cluster-fix", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone/fs-stone-metaserver:v910-unify-multi-cluster", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-upload-consumer:v905-unify-multi-cluster-20240524", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v956-gray-20250521-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/paas/fs-message/fs-todo-provider:release---950--202503211131", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-biz:v9.5.2-hotfix---definition_name_i18n_clear-20250507", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-processor:hotfix---workflow_trigger_of_batch_from_tag--202504301406", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-xt-proxy/fs-xt-proxy-provider:v1.2.6-master-20220628", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/i18n-setting:develop---9.5.5--202505092309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs:v1.0.5-k8s-20240614", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v2.2.1135-feature---paas2ChButDispatcher-7.0-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-operator/paas-db-provider:feature---fix-clickhouse-create--202504252305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/pg-scanner:master--202504092333", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/fs-qywx-web:tag-main-20250516-fix-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/weex-bundle-console/bundle-console-web:master--202503142224", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/weex-bundle-console/bundle-console-service:feature---master-cloud-merge--202412112258", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.5.4-develop---9.5.0-20250419", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.4.5-all-04-develop---9.4.5-20250328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202505092256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-crm-report/fs-bi-crm-report-web:v2025.01.35-hotfix---gray-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online:v2.6-agg-release-dispatcher-20250509", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.100-hotfix---gray-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.85-hotfix---gray-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:v2024.11.44-hotfix---gray-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-cep/fs-cep-provider:master--202504212153", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:develop---9.5.5_dataReport--202505212302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.5.5-import-gray-03-develop---9.5.5-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.5--202505161950", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:develop---9.5.5--202505162212", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---9.5.5--202505202302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:develop---9.5.0--202505162207", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:v9.5.1-hotfix---trigger_callback_mq_error-20250419", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-document-converter/fs-document-convert-web-big:3.41.1-unify-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.5.5--202505192031", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-955--202505162342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-955--202505162343", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-file-process:v1.9.6-20250517", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-file-server:v3.5.1-20250427", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:dev-955--202505162233", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:dev-9.5.5--202505152301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg/fs-fmcg-service:dev-9.5.0--202505092313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.5--202505130047", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:master--202504222343", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.5.5-gray-06-develop---9.5.5-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.5.5-gray-06-develop---9.5.5-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-calculate-task/fs-paas-calculate-task-web:v9.5.5-gray-02-develop---9.5.5-20250515", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-recycle:feature---dev-9.5.0--202504101419", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:release-955--202505092301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:release-955--202505162342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-biz:release-955--202505092301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--202504302259", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-955--202505092304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone-watch-tower/fs-stone-transfer:main", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.5.5-gray-01-develop---9.5.5-20250428", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.5.5-gray-03-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/customize-ka/hisense-custom-provider/hisense-custom-biz:jishikucun-sand_box-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-ext-compose:master--202505221401", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master--202505221111", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-provider:release-955--202505221431", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master--202505221111", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
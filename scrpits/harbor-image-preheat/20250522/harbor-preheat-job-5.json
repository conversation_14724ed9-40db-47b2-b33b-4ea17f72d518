{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-5", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-5-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---tcc_has_no_defintion--202505212349", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-login/fs-user-login-provider:bugfix---fix_create_enterprise--202505221608", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221546", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---9.5.5--202505192031", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:tag-master-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202505221406", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter-hj:master-cloud--202505211959", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master--202505201706", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--202505191622", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-task-lto-sfa/fs-crm-task-lto-sfa-web:develop---9.5.5--202505221551", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-lifecycle:release-955--202505221423", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-web:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---tcc_has_no_defintion--202505212310", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-console:feature---optimize_data_validate--202505211848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:v2024.11.78-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.98-hotfix---gray-20250520", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master-gray--202505221116", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/manufacturing/fs-crm-manufacturing/fs-crm-web:develop---955_ai_knowledge_v3--202505212348", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:master--202505172335", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-goal/fs-bi-goal-web:v2025.1.20-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505221158", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-recycling-task/fs-crm-recycling-task-web:develop---9.5.5--202505221031", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-955--202505221448", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-fsc/fs-fsc-cgi:v950-20250418", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--202505221039", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-web:develop---9.5.5---marketing_email--202505221130", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-document-preview-service/fs-document-preview-cgi:v950-20250518", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:hotfix---gray--202505172342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221704", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-surrogate:feature---dev-955--202505211800", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v2.2.1130-feature---paas2ChButDispatcher-7.0-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-linkapp-provider:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:release-955--202505221404", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-wechat:master--202505221401", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:hotfix---tcc_has_no_defintion--202505220030", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-web:v9.5.5-all01-mysql8.0-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-fsc/fs-fsc-cgi:v940.9-20250224", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata-ant:v2025.01.102-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--202505221420", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-push-broker:master--202505221038", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-task:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/paas/fs-metadata/fs-metadata-service:dev-9.5.5--202505211236", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-mq-provider:v956-master-20250523-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-provider:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter-hj:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:feature---9.6.0---chunk-split-zhangxf--202505192342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---ai_knowledge_and_video_cloud_upload--202505191743", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v956-master-20250523-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-v2/checkins-v2-task:master--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:master--202505221040", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-biz:master--202505212353", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:feature960---nl2sql-cmy--202505210020", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-uploader:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:v9.5.5-all-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---service_del_tagIndices_mengniu--202505221105", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:master--202505221152", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-wechat-customer-callback:daiyun---0508---optimize_transfer_artificial--202505221133", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-mq-provider:v956-gray-20250521-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:feature---feedPG-zwc--202505220123", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-stone-watch-tower/fs-stone-transferbackupmaster:v1.0.1-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---9.5.5--202505221141", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-message:feature---app_load_nginx_optimize--202505221109", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-cloud:v9.5.13-release---9.6.0_oneFlow-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.5-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:master--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:master--202505221401", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master-cloud--202505211959", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing-statistic/fs-marketing-statistic-provider:master--202505211855", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:release---9.6.0--202505201851", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing-statistic/fs-marketing-statistic-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-surrogate:feature---dev-955--202505221148", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-cloud:v9.5.12-release---9.6.0_oneFlow-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master-cloud--202505211959", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--202505211937", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.8.8-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-main-20250517", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221739", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-crm-fmcg-wq/fs-crm-fmcg-wq-web:master--202505221543", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
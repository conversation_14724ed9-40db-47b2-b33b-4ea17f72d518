{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-4", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-4-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-message:feature---service_del_tagIndices_mengniu--202505221105", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.5--202505211307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-task-sfa-ai/fs-crm-task-sfa-ai-web:master-all-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-reward/fs-crm-fmcg-reward-web:develop---9.5.5--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-business-broker:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-provider:develop---9.5.5---marketing_email--202505221130", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-crm-report/fs-bi-crm-report-web:v2025.01.36-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-oncall:v2-main-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---ai_knowledge_and_video_cloud_upload--202505191729", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:release-955--202505221423", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-task:dev-9.5.5--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-kingdee:master--202505221052", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-task:develop---9.5.5---marketing_email--202505221130", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master--202505221111", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:v9.5.5-all-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505201928", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--202505221110", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-task:master--202505221108", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-online-manage-provider:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-wechat:master--202505221052", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-task/fs-qixin-task-provider:master--202505221400", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:v9.5.2-release---9.6.0-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.102-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:release-955--202505221442", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-public-abutment:master-delivery-order--202505192256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:master--202505221151", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505221137", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.5.5-gray-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master--202505221437", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:master--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-meeting/fs-crm-meeting-web:main--202505221012", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing-statistic/fs-marketing-statistic-provider:master--202505221109", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:feature---9.5.5-test--202505221131", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:master--202505220944", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.5.0-all-01-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505221417", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-main-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-datax/datax-pusher:feature---master-datasource-mengniu--202505202236", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:hotfix---gray-temp--202505180138", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-cgi:v940.9-20250224", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:master--202505221152", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-ext-compose:master--202505212249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505201940", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master-gray--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:feature---rag_dispatcher_task--202505202345", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.6.0--202505181914", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-webhook/fs-webhook-provider:feature950---customInfo--202505221512", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-develop-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505201733", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/pg-scanner:master--202505201626", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505221528", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-console:feature---app_load_nginx_optimize--202505212316", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:dev-955--202505211944", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.19-main-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:feat---mengniu-url--202505201033", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-idp-helper:v1.1.3-main-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master-gray--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221113", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master-gray--202505221116", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505172253", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-task:dev-9.5.5--202505221136", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg-sales/fs-crm-fmcg-sales-web:dev-9.5.0-inspection-2.0--202505202257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.18-develop-20250517", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:master--202505220016", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v2.2.1134-feature---paas2ChButDispatcher-7.0-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-task-sfa-ai/fs-crm-task-sfa-ai-web:master--202505221028", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master-gray--202505221116", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202505221039", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.5.5-all01-mysql8.0-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.5.5-all01-mysql8.0-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/customize-ka/hisense-custom-provider/hisense-custom-biz:sand_box--202505210931", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.5.5-all-01-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.5.0--202505221406", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-provider:develop---9.5.5---marketing_email--202505221145", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
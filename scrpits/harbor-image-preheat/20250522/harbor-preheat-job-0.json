{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-0", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-0-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk11", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk24", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk11", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk23", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk24", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk8-jemalloc", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:ali-dragonwell8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-datax/datax-pusher:feature---datasource-20250325-config--202504172239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fast-notifier/notifier-server:v5.2.1-fix-start-failed-master-20240322", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:hotfix---2025-04-15--202504162201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-industry/industry-interface:release--202505142303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-industry/industry-ods:release--202505142304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-big-file-manager/fs-big-file-manager-biz:v910-unify-20240814", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-qds/fs-bug-feedback:v2.0.42-fs-20231226", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-qds/fs-bug-info:v2.0.42-fs-20231226", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-qds/fs-new-tapd-helper:v2.0.42-fs-20231226", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact-v2/fxiaoke-parent-pom-release/qixin/fs-customer-component/fs-customer-component-provider:develop---9.0.0", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-intelligence-scoring/fs-intelligence-scoring-web:v1.0.1-opportunity_score-20240414", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-datapt-pangu-rs/fs-datapt-employee-profiler:v2.2.1120-release-20230922", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/customized_dev/fs-fmcg-customized-server/fs-fmcg-customized-biz:master--202503052330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.5.0--202504111123", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fs-open-si/fs-k3cloud/fs-k3cloud-task:v2.8.49-xjy-0719upgrade-20230720", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-k8s-tomcat-test/fs-k8s-tomcat-test-biz:master-202408221652", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/open-custom/fs-open-custom/fs-open-custom-iot:kai---develop-iot2-202411142310", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/open-custom/fs-open-custom/fs-open-custom-other:develop---master-gray--202503142257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/open-custom/fs-open-custom/fs-open-custom-pay:develop---master-gray--202503142257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/open-custom/fs-open-custom/fs-open-custom-print:develop---master-gray--202503142257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.6.0--202505211825", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:feature---dev-9.5.5--202504282205", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-netdisk/fs-netdisk-biz:v945-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-experience-account-provider:feature960---i18n--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:feature960---i18n--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-server/fs-sail-wechat:v8.8.0-role-plat-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:release-950--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-uc/fs-uc-provider:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open-platform/open-api-all/open-api-gateway-web:v940-gray-20250507-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-datax/datax-pusher:feature---refresh-data--202504281519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-backup:v7.0.5-delete-split-master-20250327", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:release--202505212256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:develop---9.5.5--202505162351", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/crm_java/fs-crm-udobj/fs-crm-udobj-web:v7.6.1-develop-8.6.0-20230721", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:master--202505072322", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/client-action-router/action-router-service:master-custom-data--202503281823", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.5.0-gray-01-develop---9.5.0-20250325", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/cloud-controll-center/controll-cgi:v2.0.32-master-20240115", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-server:hotfix---export_crm_notify--202505212302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-history:release--202505092258", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-mq-proxy/cloud-mq-consumer:feature---master-gray-egress-nginx-202409261932", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online:v2.4-agg-release-20240915", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-auth/service:feature---refresh-team-auth--202505161922", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.8.9-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/bizcommon/egress-api-ext-resource:2.6", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-active-session-manage/fs-active-session-manage-provider:master-202410121941", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-ai-detector/fs-ai-detector-provider:ai_model--202504130140", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-schedule/fs-appserver-schedule-provider:release---945--202502282309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-crm-report/fs-bi-crm-report-web:v2025.01.33-feature---AITagFinal-20250514", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-offline:v2.4-agg-release-offline-202502052147", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online-calculate:v1.0.0-agg-release-**********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:v2024.11.77-feature---AITagFinal-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-export:v2024.11.43-hotfix---gray-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-goal/fs-bi-goal-web:v2025.1.19-release---gray-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-industry-interface/fs-bi-industry-interface-provider:v2.2.1120-release-20240730", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata:v2025.01.80-feature---AITagFinal-20250418", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata-ant:v2025.01.102-master-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-mq:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-org/fs-bi-org-entrance:hotfix---gray--202412132314", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-permission/fs-bi-permission-access:v2024.12.12-hotfix---gray-20241213", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-scheduler/fs-bi-scheduler-console:master-202411011419", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-sqlengine:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-sqlgenerator:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.101-feature---AITagFinal-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-stat-transfer:v1.0.62-master-20250515", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-task:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.83-feature---AITagFinal-20250509", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
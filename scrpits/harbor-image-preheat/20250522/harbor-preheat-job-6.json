{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-6", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-6-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.5.5-01-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---app_load_nginx_optimize--202505221109", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-recycle:feature---optimize-recycle-rate--202505221023", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-wishful-ocr-web:master--202505221430", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-rest:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505221139", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-mengniu-hotfix--202505172256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:master--202505221124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:feature---feedPG-zwc--202505220123", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:release-955--202505211953", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/fs-feishu-web:kny---lark_send--202505221153", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-recycling-task/fs-crm-recycling-task-web:develop---9.5.5--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider:master--202505211532", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221733", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221550", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-outer-web:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.5--202505221022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-task:master--202505221107", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-955--202505221056", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:release-955--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-provider:master--202505221014", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:feature---9.5.5-test--202505221155", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.0-develop---9.5.0-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v2.2.1133-feature---paas2ChButDispatcher-7.0-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-cloud:v9.4.21-hotfix---fix_apply_history_no_content-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:feature---9.6.0---chunk-split-zhangxf--202505200005", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--202505221133", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-outer-web:master--202505221048", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-erp-sync-data/erp-sync-data-whole-war:v960-master-20250519", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-biz:release---9.6.0_oneFlow--202505221333", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202505221114", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221737", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-public-abutment:master-delivery-order--202505202256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---app_load_nginx_optimize--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-38", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:master--202505221038", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-39", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:master-temp--202505180135", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-40", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master--202505221401", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-41", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-42", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-43", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-44", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-45", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:release-955--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-46", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-47", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:feature---feedPG-zwc--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-48", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---ai_knowledge_and_video_cloud_upload--202505191801", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-49", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:master--202505221544", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-50", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-message:feature---app_load_nginx_optimize--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-51", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-task:master--202505221013", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-52", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-53", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-54", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-biz:v9.4.19-hotfix---trigger_bpm_only_fetch_part_data-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-55", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202505221039", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-56", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505221710", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-57", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-kingdee:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-58", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master-gray--202505221204", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-59", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:master--202505221043", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-60", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:bidata--202505202101", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-61", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.5.0--202505211952", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-62", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-new-schedule/fs-new-schedule-web:master--202505220018", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-63", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-task/fs-qixin-task-provider:master--202505220018", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-64", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-expression/fs-expression-provider:master--202505221403", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-65", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:release---9.6.0_oneFlow--202505221332", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-66", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-web:develop---9.5.5---marketing_email--202505221145", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-67", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-appframework/fs-paas-app-udobj-web:v9.5.0-all-01-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-68", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.86-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-69", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-task-manage/fs-task-manage-provider:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-70", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-71", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:v2024.11.45-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-72", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-copier:master--202505201626", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-73", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-task:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-74", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-75", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---ai_knowledge_and_video_cloud_upload--202505191755", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-76", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service-v2:master--202505221546", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-77", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-agent/chatbi-bootstrap:feature---v4--202505221444", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-78", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-notice/fs-notice-provider:release-955--202505212315", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-79", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---service_del_tagIndices_mengniu--202505221515", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-80", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:master--202505221042", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}
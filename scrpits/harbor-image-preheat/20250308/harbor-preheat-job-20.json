{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "harbor-preheat-job-20", "namespace": "kube-public", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-0-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250308-3", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master-yuanqi-gray--202503072343", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-erp-sync-data/erp-sync-data-web:v950.6-guoyd_master-20250307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/sfa/fs-crm-sfa/fs-crm-web:develop---9.4.5--202503072301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250308-4", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/sync-data/fs-sync-data-all:v930-bugfix-20250308-2", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}